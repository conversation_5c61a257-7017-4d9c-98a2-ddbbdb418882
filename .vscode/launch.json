{"configurations": [{"command": "pnpm dev", "name": "Next.js: debug server-side", "request": "launch", "type": "node-terminal"}, {"name": "Next.js: debug client-side", "request": "launch", "type": "chrome", "url": "http://localhost:3000"}, {"command": "pnpm dev", "name": "Next.js: debug full stack", "request": "launch", "serverReadyAction": {"action": "debugWithChrome", "pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s"}, "type": "node-terminal"}], "version": "0.2.0"}