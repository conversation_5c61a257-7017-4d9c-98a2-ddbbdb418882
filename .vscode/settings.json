{
  "Lua.diagnostics.globals": ["ngx"],
  "Lua.runtime.version": "LuaJIT",
  "Lua.workspace.library": ["${3rd}/OpenResty/library"],

  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  "editor.formatOnPaste": true,
  "editor.formatOnSave": true,

  "eslint.runtime": "node",
  "eslint.useFlatConfig": true,
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],

  "files.associations": {
    "*.css": "tailwindcss",
    ".env*": "properties"
  },

  // Not sure why `fileWatcher` is using 100% CPU, exclude some folders to reduce the load
  "files.watcherExclude": {
    "**/.auth/**": true,
    "**/.eslint-config-inspector": true,
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/.hg/store/**": true,
    "**/.mdx/**": true,
    "**/.next/**": true,
    "**/.turbo/**": true,
    "**/coverage/**": true,
    "**/dist/**": true,
    "**/node_modules/**": true,
    "**/playwright-report/**": true,
    "**/test-results/**": true,
    "**/volumes/**": true
  },

  "github-actions.workflows.pinned.workflows": [
    ".github/workflows/docker-nginx-lua.yml",
    ".github/workflows/docker-web.yml"
  ],
  "i18n-ally.enabledFrameworks": ["react", "next-intl", "general"],
  "i18n-ally.extract.autoDetect": true,
  "i18n-ally.keysInUse": [
    "layout.about",
    "layout.privacy-policy",
    "layout.terms-of-service",
    "layout.blog",
    "layout.facebook",
    "layout.github",
    "layout.instagram",
    "layout.links",
    "layout.projects",
    "layout.youtube",
    "admin.nav.comments",
    "admin.nav.general",
    "admin.nav.users"
  ],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.localesPaths": ["packages/i18n/messages"],

  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "'([^']*)'"],
    ["cn\\(([^)]*)\\)", "'([^']*)'"],
    "className\\: '([^']*)'",
    ["classNames\\: {([^}]*)", "'([^']*)'"]
  ],

  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.preferences.preferTypeOnlyAutoImports": true,
  "typescript.tsdk": "node_modules/typescript/lib"
}
