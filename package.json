{"private": true, "name": "proxy-orb", "author": "momo <<EMAIL>> (https://github.com/momo/)", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "type": "module", "scripts": {"build": "turbo build", "build:onlinevpn": "turbo build --filter=./apps/onlinevpn", "build:packages": "turbo build --filter=./packages/*", "build:server": "NODE_ENV=production concurrently -n \"sw,proxy\" \"pnpm build:sw\" \"wait-on -d 3000 -v file:apps/nginx-lua/html/__po.intcp.js && docker-compose build nginx-lua\"", "build:sw": "NODE_ENV=production turbo build --filter=@momo/service-worker --no-cache", "build:web": "turbo build --filter=./apps/web", "check": "turbo lint && turbo type-check && pnpm format:check && pnpm check:spelling && pnpm check:knip", "check:knip": "cross-env VITE_CJS_IGNORE_WARNING=true dotenv -e .env.local -- knip", "check:npm": "pnpm dlx npm-check-updates --deep --interactive --format group", "clean": "turbo clean && rm -rf .turbo coverage", "db:check": "turbo db:check", "db:generate": "turbo db:generate", "db:migrate": "turbo db:migrate", "db:push": "turbo db:push", "db:seed": "turbo db:seed", "db:studio": "turbo db:studio", "dev": "concurrently -n \"kill-all,server,web\" \"pnpm dev:kill-all\" \"pnpm dev:server\" \"pnpm dev:web\"", "dev:all": "turbo dev --concurrency=12", "dev:kill-all": "kill -9 $(lsof -t -i:3000)", "dev:onlinevpn": "turbo dev --filter=@momo/onlinevpn --concurrency=12", "dev:packages": "turbo dev --filter=./packages/* --concurrency=12", "dev:server": "concurrently -n \"sw,proxy\" \"turbo dev --filter=@momo/service-worker --concurrency=12\" \"cd apps/nginx-lua && wait-on -d 3000 -v file:../service-worker/dist/__po.intcp.js && make dev\"", "dev:web": "turbo dev --filter=@momo/web --concurrency=12", "format:check": "prettier --cache --check --ignore-path .gitignore --ignore-path .prettierignore .", "format:write": "prettier --cache --write --list-different --ignore-path .gitignore --ignore-path .prettierignore .", "preinstall": "npx --yes only-allow pnpm", "lint": "turbo lint && eslint . --max-warnings 0", "lint:fix": "turbo lint:fix && eslint --fix . && pnpm format:write", "prepare": "husky && pnpm build:packages", "release": "changeset publish", "test:e2e": "turbo test:e2e", "test:e2e:inspector": "turbo test:e2e:inspector", "test:e2e:install": "playwright install", "test:e2e:ui": "turbo test:e2e:ui", "test:unit": "vitest run", "test:unit:coverage": "vitest --coverage --ui", "test:unit:ui": "vitest --ui", "type-check": "turbo type-check", "version": "changeset version"}, "dependencies": {"next-mdx-remote": "^5.0.0"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.11", "@momo/eslint-config": "workspace:*", "@momo/prettier-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@playwright/test": "^1.49.1", "@turbo/gen": "^2.3.3", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.17.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "knip": "^5.41.1", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "tsup": "8.3.0", "tsx": "^4.19.2", "turbo": "^2.3.3", "typescript": "5.7.2", "vitest": "^2.1.8", "wait-on": "^7.2.0"}, "engines": {"node": ">=20.11.0", "pnpm": ">=9"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --ignore-pattern '*.bundled_*.mjs'"], "**/*": "prettier --write --ignore-unknown --loglevel error"}, "packageManager": "pnpm@10.12.4"}