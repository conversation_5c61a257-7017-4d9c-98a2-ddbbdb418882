{"private": true, "name": "@momo/env", "version": "0.0.0", "description": "The validated environment variables for <PERSON><PERSON>'s projects", "license": "MIT", "type": "module", "main": "./src/index.ts", "scripts": {"clean": "rm -rf .turbo", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "dependencies": {"@t3-oss/env-nextjs": "^0.11.1", "zod": "^3.24.1"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}