import { createEnv } from '@t3-oss/env-nextjs'
import { vercel } from '@t3-oss/env-nextjs/presets'
import { z } from 'zod'

export const flags = {
  comment: process.env.NEXT_PUBLIC_FLAG_COMMENT === 'true',
  auth: process.env.NEXT_PUBLIC_FLAG_AUTH === 'true',
  stats: process.env.NEXT_PUBLIC_FLAG_STATS === 'true',
  analytics: process.env.NEXT_PUBLIC_FLAG_ANALYTICS === 'true',
  likeButton: process.env.NEXT_PUBLIC_FLAG_LIKE_BUTTON === 'true'
}

export const env = createEnv({
  skipValidation: !!process.env.CI,
  extends: [vercel()],

  shared: {
    NODE_ENV: z.enum(['development', 'production', 'test']).default('development')
  },

  server: {
    LOG_LEVEL: z
      .enum(['error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly'])
      .default('info'),
    JWT_SECRET: z.string().min(1),
    FALLBACK_SERVERS: z.string().min(1),

    REDIS_HOST: z.string().min(1),
    REDIS_PORT: z.coerce.number().min(1),
    REDIS_DB: z.coerce.number().min(0),
    REDIS_USERNAME: z.string().min(1),
    REDIS_PASSWORD: z.string().min(1),
    REDIS_KEY_PREFIX: z.string().min(1),

    HEARTBEAT_WORKER_KEY: z.string().min(1),
    HEARTBEAT_ACTIVE_WORKERS_KEY: z.string().min(1),

    ...(flags.auth
      ? {
          AUTH_SECRET: z.string().min(1),
          GOOGLE_CLIENT_ID: z.string().min(1),
          GOOGLE_CLIENT_SECRET: z.string().min(1),
          GITHUB_CLIENT_ID: z.string().min(1),
          GITHUB_CLIENT_SECRET: z.string().min(1)
        }
      : {}),

    ...(flags.stats
      ? {
          GOOGLE_API_KEY: z.string().min(1),
          GITHUB_TOKEN: z.string().min(1),
          WAKATIME_API_KEY: z.string().min(1)
        }
      : {}),

    ...(flags.likeButton
      ? {
          IP_ADDRESS_SALT: z.string().min(1)
        }
      : {}),

    DATABASE_URL: z.string().url(),
    UPSTASH_REDIS_REST_URL: z.string().url(),
    UPSTASH_REDIS_REST_TOKEN: z.string().min(1),
    REACT_SCAN_MONITOR_API_KEY: z.string().optional()
  },
  client: {
    NEXT_PUBLIC_FLAG_COMMENT: z.string().min(1).optional(),
    NEXT_PUBLIC_FLAG_AUTH: z.string().min(1).optional(),
    NEXT_PUBLIC_FLAG_STATS: z.string().min(1).optional(),
    NEXT_PUBLIC_FLAG_ANALYTICS: z.string().min(1).optional(),
    NEXT_PUBLIC_FLAG_LIKE_BUTTON: z.string().min(1).optional(),
    NEXT_PUBLIC_PROXY_SERVER_URL: z.string().min(1).optional()
  },
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,

    NEXT_PUBLIC_FLAG_COMMENT: process.env.NEXT_PUBLIC_FLAG_COMMENT,
    NEXT_PUBLIC_FLAG_AUTH: process.env.NEXT_PUBLIC_FLAG_AUTH,
    NEXT_PUBLIC_FLAG_STATS: process.env.NEXT_PUBLIC_FLAG_STATS,
    NEXT_PUBLIC_FLAG_ANALYTICS: process.env.NEXT_PUBLIC_FLAG_ANALYTICS,
    NEXT_PUBLIC_FLAG_LIKE_BUTTON: process.env.NEXT_PUBLIC_FLAG_LIKE_BUTTON,
    NEXT_PUBLIC_PROXY_SERVER_URL: process.env.NEXT_PUBLIC_PROXY_SERVER_URL
  },

  emptyStringAsUndefined: true
})
