{"name": "@momo/tailwind-config", "version": "0.0.18", "description": "The Tailwind CSS configuration for <PERSON><PERSON>'s projects", "license": "MIT", "author": "momo <<EMAIL>> (https://github.com/momo/)", "homepage": "https://github.com/proxyorb/proxyorb.com#readme", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "bugs": {"url": "https://github.com/proxyorb/proxyorb.com/issues"}, "main": "tailwind.config.ts", "files": ["src"], "scripts": {"clean": "rm -rf .turbo", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "dependencies": {"@tailwindcss/typography": "^0.5.15", "tailwindcss-animate": "^1.0.7"}, "peerDependencies": {"tailwindcss": "3.x"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*", "tailwindcss": "^3.4.17"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}