{"name": "@momo/ui", "version": "0.0.45", "description": "The UI components for <PERSON><PERSON>'s projects", "license": "MIT", "author": "momo <<EMAIL>> (https://github.com/momo/)", "homepage": "https://github.com/proxyorb/proxyorb.com#readme", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "bugs": {"url": "https://github.com/proxyorb/proxyorb.com/issues"}, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "files": ["dist"], "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "sideEffects": false, "types": "./dist/index.d.ts", "dependencies": {"@icons-pack/react-simple-icons": "10.2.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/react-visually-hidden": "^1.1.1", "class-variance-authority": "^0.7.1", "cmdk": "^1.0.4", "lucide-react": "^0.469.0", "merge-refs": "^1.3.0", "motion": "^11.17.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-textarea-autosize": "^8.5.6", "sonner": "1.7.1", "vaul": "^1.1.2"}, "peerDependencies": {"@momo/utils": ">=0", "@tanstack/react-table": "^8", "next": "^15.2.3", "react": "^19", "react-dom": "^19"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tailwind-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@momo/utils": "workspace:*", "@tanstack/react-table": "^8.20.6", "@types/react": "19.0.2", "@types/react-dom": "19.0.2", "next": "^15.2.3", "react": "19.0.0", "react-dom": "19.0.0", "tailwindcss": "^3.4.17"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}