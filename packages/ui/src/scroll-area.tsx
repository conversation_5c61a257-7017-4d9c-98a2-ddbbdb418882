'use client'

import { cn } from '@momo/utils'
import * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area'
import React from 'react'

type ScrollAreaProps = React.ComponentProps<typeof ScrollAreaPrimitive.Root>

export const ScrollArea = (props: ScrollAreaProps) => {
  const { className, children, ...rest } = props

  return (
    <ScrollAreaPrimitive.Root className={cn('relative overflow-hidden', className)} {...rest}>
      <ScrollAreaPrimitive.Viewport className='size-full rounded-[inherit]'>
        {children}
      </ScrollAreaPrimitive.Viewport>
      <ScrollBar />
      <ScrollAreaPrimitive.Corner />
    </ScrollAreaPrimitive.Root>
  )
}

type ScrollBarProps = React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>

export const ScrollBar = (props: ScrollBarProps) => {
  const { className, orientation = 'vertical', ...rest } = props

  return (
    <ScrollAreaPrimitive.ScrollAreaScrollbar
      orientation={orientation}
      className={cn(
        'flex touch-none select-none transition-colors',
        orientation === 'vertical' && 'h-full w-2 border-l border-l-transparent p-px',
        orientation === 'horizontal' && 'h-2 flex-col border-t border-t-transparent p-px',
        className
      )}
      {...rest}
    >
      <ScrollAreaPrimitive.ScrollAreaThumb className='bg-border/70 hover:bg-border/80 relative flex-1 rounded-full transition-colors' />
    </ScrollAreaPrimitive.ScrollAreaScrollbar>
  )
}
