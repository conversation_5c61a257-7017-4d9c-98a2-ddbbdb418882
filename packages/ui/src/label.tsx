'use client'

import { cn } from '@momo/utils'
import * as LabelPrimitive from '@radix-ui/react-label'
import { cva, type VariantProps } from 'class-variance-authority'

export const labelVariants = cva([
  'text-sm font-medium leading-none',
  'peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
])

type LabelProps = React.ComponentProps<typeof LabelPrimitive.Root> &
  VariantProps<typeof labelVariants>

export const Label = (props: LabelProps) => {
  const { className, ...rest } = props

  return <LabelPrimitive.Root className={cn(labelVariants(), className)} {...rest} />
}
