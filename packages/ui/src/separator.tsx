'use client'

import { cn } from '@momo/utils'
import * as SeparatorPrimitive from '@radix-ui/react-separator'

type SeparatorProps = React.ComponentProps<typeof SeparatorPrimitive.Root>

export const Separator = (props: SeparatorProps) => {
  const { className, orientation = 'horizontal', decorative = true, ...rest } = props

  return (
    <SeparatorPrimitive.Root
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'bg-border shrink-0',
        orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-px',
        className
      )}
      {...rest}
    />
  )
}
