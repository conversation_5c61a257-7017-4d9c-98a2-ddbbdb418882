import React from 'react'

type LogoProps = React.SVGAttributes<SVGElement>

export const Logo = (props: LogoProps) => {
  return (
    <svg
      version='1.0'
      xmlns='http://www.w3.org/2000/svg'
      width={160}
      height={42}
      viewBox='0 0 900 240'
      preserveAspectRatio='xMidYMid meet'
      {...props}
    >
      <defs>
        {/* 定义渐变 */}
        <linearGradient id='orbGradient' x1='0%' y1='0%' x2='100%' y2='100%'>
          <stop offset='0%' style={{ stopColor: '#4F46E5' }} /> {/* Indigo 600 */}
          <stop offset='100%' style={{ stopColor: '#818CF8' }} /> {/* Indigo 400 */}
        </linearGradient>

        {/* 定义发光效果 */}
        <filter id='glow'>
          <feGaussianBlur stdDeviation='2' result='coloredBlur' />
          <feMerge>
            <feMergeNode in='coloredBlur' />
            <feMergeNode in='SourceGraphic' />
          </feMerge>
        </filter>
      </defs>

      {/* 背景圆环 - 使用渐变色 */}
      <circle
        cx='120'
        cy='120'
        r='80'
        fill='none'
        stroke='url(#orbGradient)'
        strokeWidth='10'
        strokeDasharray='25 12'
        className='animate-spin-slow'
        style={{ filter: 'url(#glow)' }}
      />

      {/* 中心球体 - 使用渐变色 */}
      <circle
        cx='120'
        cy='120'
        r='35'
        fill='url(#orbGradient)'
        opacity='0.9'
        style={{ filter: 'url(#glow)' }}
      />

      {/* ProxyOrb 文字 - 优化字体和间距 */}
      <g transform='translate(240, 150)'>
        <text
          fill='currentColor'
          fontSize='95'
          fontFamily='var(--font-geist-sans), system-ui, -apple-system, sans-serif'
          fontWeight='600'
          letterSpacing='0.5'
          style={{
            fontFeatureSettings: "'ss01' on, 'ss02' on",
            fontVariationSettings: "'wght' 600"
          }}
        >
          <tspan className='text-slate-600 dark:text-slate-100' letterSpacing='0.2'>
            Proxy
          </tspan>
          <tspan className='text-indigo-600 dark:text-indigo-500' letterSpacing='0.8' dx='8'>
            Orb
          </tspan>
        </text>
      </g>
    </svg>
  )
}
