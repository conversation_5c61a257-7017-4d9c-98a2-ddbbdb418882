/**
 * Adapted from: https://github.com/delbaoliveira/website/blob/59e6f181ad75751342ceaa8931db4cbcef86b018/ui/BlurImage.tsx
 */
'use client'

import { cn } from '@momo/utils'
import NextImage from 'next/image'
import { type ComponentProps, useState } from 'react'

type ImageProps = {
  imageClassName?: string
  lazy?: boolean
} & ComponentProps<typeof NextImage>

export const BlurImage = (props: ImageProps) => {
  const { alt, src, className, imageClassName, lazy = true, ...rest } = props
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  return (
    <div className={cn('overflow-hidden', className)}>
      <NextImage
        className={cn(
          'transition-all duration-700 ease-in-out',
          isLoading && !hasError && 'scale-[1.02] opacity-70 blur-sm',
          !isLoading && 'scale-100 opacity-100 blur-0',
          hasError && 'opacity-50',
          imageClassName
        )}
        src={src}
        alt={alt}
        loading={lazy ? 'lazy' : undefined}
        priority={!lazy}
        quality={100}
        onLoad={() => {
          setIsLoading(false)
          setHasError(false)
        }}
        onError={() => {
          setIsLoading(false)
          setHasError(true)
        }}
        {...rest}
      />
    </div>
  )
}
