{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"allowJs": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "checkJs": false, "ignoreReadonlyErrors": true, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noStrictGenericChecks": true, "noUnusedLocals": false, "noUnusedParameters": false, "strict": false, "strictBindCallApply": false, "strictFunctionTypes": false, "strictNullChecks": false, "strictPropertyInitialization": false, "suppressImplicitAnyIndexErrors": true, "useDefineForClassFields": false}, "display": "Relaxed"}