{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": true, "checkJs": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": false, "isolatedModules": true, "lib": ["ES2023", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleDetection": "force", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "target": "ESNext"}, "display": "<PERSON><PERSON><PERSON>"}