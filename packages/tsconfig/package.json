{"name": "@momo/tsconfig", "version": "0.0.10", "description": "The typescript config for <PERSON><PERSON>'s projects", "license": "MIT", "author": "momo <<EMAIL>> (https://github.com/momo/)", "homepage": "https://github.com/proxyorb/proxyorb.com#readme", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "bugs": {"url": "https://github.com/proxyorb/proxyorb.com/issues"}, "type": "module", "files": ["base.json", "nextjs.json", "react-library.json", "service-worker.json"], "dependencies": {"next": "^15.2.3"}, "peerDependencies": {"typescript": "5.x"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}