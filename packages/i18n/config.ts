export const supportedLanguages = [
  {
    code: 'en',
    label: 'English',
    icon: '🇬🇧',
    default: true
  },
  {
    code: 'es',
    label: '<PERSON><PERSON>a<PERSON><PERSON>',
    icon: '🇪🇸'
  },
  {
    code: 'hi',
    label: 'हिन्दी',
    icon: '🇮🇳'
  },
  {
    code: 'ar',
    label: 'العربية',
    icon: '🇸🇦'
  },
  {
    code: 'fr',
    label: 'Français',
    icon: '🇫🇷'
  },
  {
    code: 'ru',
    label: 'Русский',
    icon: '🇷🇺'
  },
  {
    code: 'pt',
    label: 'Português',
    icon: '🇵🇹'
  },
  {
    code: 'id',
    label: 'Bahasa Indonesia',
    icon: '🇮🇩'
  },
  {
    code: 'ja',
    label: '日本語',
    icon: '🇯🇵'
  },
  {
    code: 'de',
    label: 'Deutsch',
    icon: '🇩🇪'
  },
  {
    code: 'bn',
    label: 'বাংলা',
    icon: '🇧🇩'
  },
  {
    code: 'ur',
    label: 'اردو',
    icon: '🇵🇰'
  },
  {
    code: 'fa',
    label: 'فارسی',
    icon: '🇮🇷'
  },
  {
    code: 'tr',
    label: 'Türkçe',
    icon: '🇹🇷'
  },
  {
    code: 'ko',
    label: '한국어',
    icon: '🇰🇷'
  },
  {
    code: 'vi',
    label: 'Tiếng Việt',
    icon: '🇻🇳'
  },
  {
    code: 'it',
    label: 'Italiano',
    icon: '🇮🇹'
  },
  {
    code: 'pa',
    label: 'ਪੰਜਾਬੀ',
    icon: '🇮🇳'
  },
  {
    code: 'zh-Hans',
    label: '简体中文',
    icon: '🇨🇳'
  },
  {
    code: 'zh-Hant',
    label: '繁體中文',
    icon: '🇹🇼'
  }
] as const

export const i18n = {
  locales: supportedLanguages.map(({ code }) => code),
  // @ts-ignore
  defaultLocale: supportedLanguages.find(({ default: isDefault }) => isDefault)?.code ?? 'en'
} as const
