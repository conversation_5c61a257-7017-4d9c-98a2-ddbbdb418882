{"private": true, "name": "@momo/shared", "version": "0.0.0", "description": "The shared stuff for <PERSON><PERSON>'s projects", "license": "MIT", "type": "module", "main": "./src/index.ts", "scripts": {"clean": "rm -rf .turbo", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}