{"name": "@momo/prettier-plugin-package-json", "version": "0.0.2", "description": "A prettier plugin using prettier-package-json", "license": "MIT", "author": "momo <<EMAIL>> (https://github.com/momo)", "homepage": "https://github.com/proxyorb/proxyorb.com#readme", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "bugs": {"url": "https://github.com/proxyorb/proxyorb.com/issues"}, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "files": ["dist"], "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "sideEffects": false, "types": "./dist/index.d.ts", "dependencies": {"prettier-package-json": "^2.8.0"}, "peerDependencies": {"prettier": "3.x"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}