{"private": true, "name": "@momo/db", "version": "0.0.0", "description": "The ORM for <PERSON><PERSON>'s projects", "license": "MIT", "type": "module", "main": "./src/index.ts", "scripts": {"clean": "rm -rf .turbo", "db:check": "dotenv -e ../../.env.local -- drizzle-kit check", "db:generate": "dotenv -e ../../.env.local -- drizzle-kit generate", "db:migrate": "dotenv -e ../../.env.local -- tsx ./src/migrate.ts", "db:push": "dotenv -e ../../.env.local -- drizzle-kit push", "db:seed": "dotenv -e ../../.env.local -- tsx ./src/seed.ts", "db:studio": "dotenv -e ../../.env.local -- drizzle-kit studio", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "dependencies": {"@momo/env": "workspace:*", "@paralleldrive/cuid2": "^2.2.2", "drizzle-orm": "^0.38.3"}, "peerDependencies": {"pg": "^8"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@types/node": "^22.10.2", "drizzle-kit": "^0.30.1"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}