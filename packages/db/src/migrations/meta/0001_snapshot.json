{"_meta": {"columns": {}, "schemas": {}, "tables": {}}, "dialect": "postgresql", "enums": {"public.role": {"name": "role", "schema": "public", "values": ["user", "admin"]}}, "id": "552bc70c-7f10-4129-9511-df2b5b922167", "prevId": "13864ac5-e3f2-4132-b612-8a864ba40220", "schemas": {}, "sequences": {}, "tables": {"public.account": {"columns": {"access_token": {"name": "access_token", "notNull": false, "primaryKey": false, "type": "text"}, "expires_at": {"name": "expires_at", "notNull": false, "primaryKey": false, "type": "integer"}, "id_token": {"name": "id_token", "notNull": false, "primaryKey": false, "type": "text"}, "provider": {"name": "provider", "notNull": true, "primaryKey": false, "type": "text"}, "provider_account_id": {"name": "provider_account_id", "notNull": true, "primaryKey": false, "type": "text"}, "refresh_token": {"name": "refresh_token", "notNull": false, "primaryKey": false, "type": "text"}, "scope": {"name": "scope", "notNull": false, "primaryKey": false, "type": "text"}, "session_state": {"name": "session_state", "notNull": false, "primaryKey": false, "type": "text"}, "token_type": {"name": "token_type", "notNull": false, "primaryKey": false, "type": "text"}, "type": {"name": "type", "notNull": true, "primaryKey": false, "type": "text"}, "user_id": {"name": "user_id", "notNull": true, "primaryKey": false, "type": "text"}}, "compositePrimaryKeys": {"account_provider_provider_account_id_pk": {"columns": ["provider", "provider_account_id"], "name": "account_provider_provider_account_id_pk"}}, "foreignKeys": {"account_user_id_user_id_fk": {"columnsFrom": ["user_id"], "columnsTo": ["id"], "name": "account_user_id_user_id_fk", "onDelete": "cascade", "onUpdate": "no action", "tableFrom": "account", "tableTo": "user"}}, "indexes": {}, "name": "account", "schema": "", "uniqueConstraints": {}}, "public.comment": {"columns": {"body": {"name": "body", "notNull": true, "primaryKey": false, "type": "text"}, "created_at": {"default": "now()", "name": "created_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "id": {"name": "id", "notNull": true, "primaryKey": true, "type": "text"}, "is_deleted": {"default": false, "name": "is_deleted", "notNull": true, "primaryKey": false, "type": "boolean"}, "parent_id": {"name": "parent_id", "notNull": false, "primaryKey": false, "type": "text"}, "post_id": {"name": "post_id", "notNull": true, "primaryKey": false, "type": "text"}, "updated_at": {"default": "now()", "name": "updated_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "user_id": {"name": "user_id", "notNull": true, "primaryKey": false, "type": "text"}}, "compositePrimaryKeys": {}, "foreignKeys": {"comment_post_id_post_slug_fk": {"columnsFrom": ["post_id"], "columnsTo": ["slug"], "name": "comment_post_id_post_slug_fk", "onDelete": "no action", "onUpdate": "no action", "tableFrom": "comment", "tableTo": "post"}, "comment_user_id_user_id_fk": {"columnsFrom": ["user_id"], "columnsTo": ["id"], "name": "comment_user_id_user_id_fk", "onDelete": "no action", "onUpdate": "no action", "tableFrom": "comment", "tableTo": "user"}}, "indexes": {}, "name": "comment", "schema": "", "uniqueConstraints": {}}, "public.guestbook": {"columns": {"body": {"name": "body", "notNull": true, "primaryKey": false, "type": "text"}, "created_at": {"default": "now()", "name": "created_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "id": {"name": "id", "notNull": true, "primaryKey": true, "type": "text"}, "updated_at": {"default": "now()", "name": "updated_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "user_id": {"name": "user_id", "notNull": true, "primaryKey": false, "type": "text"}}, "compositePrimaryKeys": {}, "foreignKeys": {"guestbook_user_id_user_id_fk": {"columnsFrom": ["user_id"], "columnsTo": ["id"], "name": "guestbook_user_id_user_id_fk", "onDelete": "no action", "onUpdate": "no action", "tableFrom": "guestbook", "tableTo": "user"}}, "indexes": {}, "name": "guestbook", "schema": "", "uniqueConstraints": {}}, "public.likes_session": {"columns": {"created_at": {"default": "now()", "name": "created_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "id": {"name": "id", "notNull": true, "primaryKey": true, "type": "text"}, "likes": {"default": 0, "name": "likes", "notNull": true, "primaryKey": false, "type": "integer"}}, "compositePrimaryKeys": {}, "foreignKeys": {}, "indexes": {}, "name": "likes_session", "schema": "", "uniqueConstraints": {}}, "public.post": {"columns": {"created_at": {"default": "now()", "name": "created_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "likes": {"default": 0, "name": "likes", "notNull": true, "primaryKey": false, "type": "integer"}, "slug": {"name": "slug", "notNull": true, "primaryKey": true, "type": "text"}, "views": {"default": 0, "name": "views", "notNull": true, "primaryKey": false, "type": "integer"}}, "compositePrimaryKeys": {}, "foreignKeys": {}, "indexes": {}, "name": "post", "schema": "", "uniqueConstraints": {}}, "public.rate": {"columns": {"comment_id": {"name": "comment_id", "notNull": true, "primaryKey": false, "type": "text"}, "like": {"name": "like", "notNull": true, "primaryKey": false, "type": "boolean"}, "user_id": {"name": "user_id", "notNull": true, "primaryKey": false, "type": "text"}}, "compositePrimaryKeys": {"rate_user_id_comment_id_pk": {"columns": ["user_id", "comment_id"], "name": "rate_user_id_comment_id_pk"}}, "foreignKeys": {"rate_comment_id_comment_id_fk": {"columnsFrom": ["comment_id"], "columnsTo": ["id"], "name": "rate_comment_id_comment_id_fk", "onDelete": "cascade", "onUpdate": "no action", "tableFrom": "rate", "tableTo": "comment"}, "rate_user_id_user_id_fk": {"columnsFrom": ["user_id"], "columnsTo": ["id"], "name": "rate_user_id_user_id_fk", "onDelete": "no action", "onUpdate": "no action", "tableFrom": "rate", "tableTo": "user"}}, "indexes": {}, "name": "rate", "schema": "", "uniqueConstraints": {}}, "public.session": {"columns": {"expires": {"name": "expires", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "session_token": {"name": "session_token", "notNull": true, "primaryKey": true, "type": "text"}, "user_id": {"name": "user_id", "notNull": true, "primaryKey": false, "type": "text"}}, "compositePrimaryKeys": {}, "foreignKeys": {"session_user_id_user_id_fk": {"columnsFrom": ["user_id"], "columnsTo": ["id"], "name": "session_user_id_user_id_fk", "onDelete": "cascade", "onUpdate": "no action", "tableFrom": "session", "tableTo": "user"}}, "indexes": {}, "name": "session", "schema": "", "uniqueConstraints": {}}, "public.user": {"columns": {"created_at": {"default": "now()", "name": "created_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "email": {"name": "email", "notNull": true, "primaryKey": false, "type": "text"}, "email_verified": {"name": "email_verified", "notNull": false, "primaryKey": false, "type": "timestamp (3)"}, "id": {"name": "id", "notNull": true, "primaryKey": true, "type": "text"}, "image": {"name": "image", "notNull": false, "primaryKey": false, "type": "text"}, "name": {"name": "name", "notNull": false, "primaryKey": false, "type": "text"}, "role": {"default": "'user'", "name": "role", "notNull": true, "primaryKey": false, "type": "role", "typeSchema": "public"}, "updated_at": {"default": "now()", "name": "updated_at", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}}, "compositePrimaryKeys": {}, "foreignKeys": {}, "indexes": {}, "name": "user", "schema": "", "uniqueConstraints": {"user_email_unique": {"columns": ["email"], "name": "user_email_unique", "nullsNotDistinct": false}}}, "public.verification_token": {"columns": {"expires": {"name": "expires", "notNull": true, "primaryKey": false, "type": "timestamp (3)"}, "identifier": {"name": "identifier", "notNull": true, "primaryKey": false, "type": "text"}, "token": {"name": "token", "notNull": true, "primaryKey": false, "type": "text"}}, "compositePrimaryKeys": {"verification_token_identifier_token_pk": {"columns": ["identifier", "token"], "name": "verification_token_identifier_token_pk"}}, "foreignKeys": {}, "indexes": {}, "name": "verification_token", "schema": "", "uniqueConstraints": {}}}, "version": "7"}