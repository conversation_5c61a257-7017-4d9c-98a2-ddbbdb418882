{"private": true, "name": "@momo/mdx", "version": "0.0.0", "description": "The MDX compiler for <PERSON><PERSON>'s projects", "license": "MIT", "type": "module", "main": "./src/index.ts", "bin": {"mdx": "./cli.js"}, "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "sideEffects": false, "dependencies": {"@shikijs/rehype": "^1.24.4", "@shikijs/transformers": "^1.24.4", "chokidar": "^4.0.3", "commander": "^12.1.0", "cosmiconfig": "^9.0.0", "fast-glob": "^3.3.2", "github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "hast": "^1.0.0", "jiti": "^2.4.2", "mdx-bundler": "^10.0.3", "pluralize": "^8.0.0", "react": "19.0.0", "react-dom": "19.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "shiki": "^1.24.4", "ts-morph": "^24.0.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.4", "@types/mdx": "^2.0.13", "@types/node": "^22.10.2", "@types/pluralize": "^0.0.33", "@types/react": "19.0.2", "@types/react-dom": "19.0.2"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}