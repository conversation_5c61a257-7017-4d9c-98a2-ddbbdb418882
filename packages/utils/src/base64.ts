/**
 * Base64 解码函数，同时支持浏览器和 Node.js 环境
 * @param str 要解码的字符串
 * @param padding 是否处理padding。如果为true，保持原有padding；如果为false，移除padding
 */
export function decode(str: string, padding = true): string {
  try {
    // 处理padding
    let processedStr = str
    if (!padding) {
      // 移除所有padding字符
      processedStr = str.replace(/=+$/, '')
    } else if (!/=$/.test(str)) {
      // 添加必要的padding
      const paddingLength = 4 - (str.length % 4)
      if (paddingLength < 4) {
        processedStr = str + '='.repeat(paddingLength)
      }
    }

    // 浏览器环境
    if (typeof window !== 'undefined') {
      return atob(processedStr)
    }
    // Node.js 环境
    return Buffer.from(processedStr, 'base64').toString()
  } catch (e) {
    return str
  }
}

/**
 * Base64 编码函数，同时支持浏览器和 Node.js 环境
 * @param str 要编码的字符串
 * @param padding 是否包含padding。如果为true，保留padding；如果为false，移除padding
 */
export function encode(str: string, padding = true): string {
  try {
    let encoded: string
    // 浏览器环境
    if (typeof window !== 'undefined') {
      encoded = btoa(str)
    } else {
      // Node.js 环境
      encoded = Buffer.from(str).toString('base64')
    }

    // 处理padding
    if (!padding) {
      return encoded.replace(/=+$/, '')
    }
    return encoded
  } catch (e) {
    return str
  }
}
