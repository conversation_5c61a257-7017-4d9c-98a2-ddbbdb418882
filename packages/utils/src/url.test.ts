import { describe, expect, it } from 'vitest'

import { toProxyUrl, toOriginalUrl, decodePot } from './url'

describe('URL utilities', () => {
  describe('toOriginalUrl', () => {
    it('should restore basic URL correctly', () => {
      const proxyUrl = 'https://n1.proxyorb.com/?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
      expect(toOriginalUrl(proxyUrl)).toBe('https://tableconvert.com/')
    })

    it('should restore URL with path correctly', () => {
      const proxyUrl =
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
      expect(toOriginalUrl(proxyUrl)).toBe('https://tableconvert.com/excel-to-json')
    })

    it('should restore URL with additional query params', () => {
      const proxyUrl =
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t&theme=dark'
      expect(toOriginalUrl(proxyUrl)).toBe('https://tableconvert.com/excel-to-json?theme=dark')
    })

    it('should restore URL with hash', () => {
      const proxyUrl = 'https://n1.proxyorb.com/?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t#section1'
      expect(toOriginalUrl(proxyUrl)).toBe('https://tableconvert.com/#section1')
    })

    it('should restore URL with query params and hash', () => {
      const proxyUrl =
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t&theme=dark#section1'
      expect(toOriginalUrl(proxyUrl)).toBe(
        'https://tableconvert.com/excel-to-json?theme=dark#section1'
      )
    })

    it('should return original URL if no __pot param', () => {
      const url = 'https://n1.proxyorb.com/excel-to-json'
      expect(toOriginalUrl(url)).toBe(url)
    })

    it('should return original URL if invalid __pot param', () => {
      const url = 'https://n1.proxyorb.com/excel-to-json?__pot=invalid'
      expect(toOriginalUrl(url)).toBe(url)
    })
  })

  describe('toProxyUrl', () => {
    const proxyOrigin = 'https://n1.proxyorb.com'

    it('should create basic proxy URL correctly', () => {
      const realUrl = 'https://tableconvert.com'
      const result = toProxyUrl(realUrl, proxyOrigin)
      expect(result).toBe('https://n1.proxyorb.com/?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t')
    })

    it('should create proxy URL with path correctly', () => {
      const realUrl = 'https://tableconvert.com/excel-to-json'
      const result = toProxyUrl(realUrl, proxyOrigin)
      expect(result).toBe(
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
      )
    })

    it('should create proxy URL preserving query params', () => {
      const realUrl = 'https://tableconvert.com/excel-to-json?theme=dark'
      const result = toProxyUrl(realUrl, proxyOrigin)
      expect(result).toBe(
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t&theme=dark'
      )
    })

    it('should create proxy URL preserving hash', () => {
      const realUrl = 'https://tableconvert.com/excel-to-json#section1'
      const result = toProxyUrl(realUrl, proxyOrigin)
      expect(result).toBe(
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t#section1'
      )
    })

    it('should return original URL if invalid proxy origin', () => {
      const realUrl = 'https://tableconvert.com'
      const result = toProxyUrl(realUrl, 'invalid-proxy')
      expect(result).toBe(realUrl)
    })

    it('should handle URLs with multiple query parameters', () => {
      const realUrl = 'https://tableconvert.com/excel-to-json?theme=dark&lang=en'
      const result = toProxyUrl(realUrl, proxyOrigin)
      expect(result).toBe(
        'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t&theme=dark&lang=en'
      )
    })

    it('should handle URLs without protocol', () => {
      const testCases = [
        {
          input: 'tableconvert.com',
          expected: 'https://n1.proxyorb.com/?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
        },
        {
          input: 'tableconvert.com/excel-to-json',
          expected: 'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
        },
        {
          input: 'tableconvert.com/excel-to-json?theme=dark',
          expected:
            'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t&theme=dark'
        },
        {
          input: 'tableconvert.com/excel-to-json#section1',
          expected:
            'https://n1.proxyorb.com/excel-to-json?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t#section1'
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(toProxyUrl(input, proxyOrigin)).toBe(expected)
      })
    })

    it('should preserve existing protocol if provided', () => {
      const testCases = [
        {
          input: 'http://tableconvert.com',
          expected: 'https://n1.proxyorb.com/?__pot=aHR0cDovL3RhYmxlY29udmVydC5jb20'
        },
        {
          input: 'https://tableconvert.com',
          expected: 'https://n1.proxyorb.com/?__pot=aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(toProxyUrl(input, proxyOrigin)).toBe(expected)
      })
    })

    it('should handle international domain names', () => {
      const testCases = [
        {
          input: '中文.com',
          expected: toProxyUrl('https://xn--fiq228c.com', proxyOrigin)
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(toProxyUrl(input, proxyOrigin)).toBe(expected)
      })
    })

    it('should handle domains with special characters', () => {
      const testCases = [
        {
          input: 'sub-domain.example.com',
          expected: 'https://n1.proxyorb.com/?__pot=aHR0cHM6Ly9zdWItZG9tYWluLmV4YW1wbGUuY29t'
        },
        {
          input: 'test.co.uk',
          expected: 'https://n1.proxyorb.com/?__pot=aHR0cHM6Ly90ZXN0LmNvLnVr'
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(toProxyUrl(input, proxyOrigin)).toBe(expected)
      })
    })

    it('should handle unicode characters in URLs', () => {
      const testCases = [
        {
          input: 'https://example.com/测试',
          expected: toProxyUrl('https://example.com/测试', proxyOrigin)
        },
        {
          input: 'https://example.com/?q=测试',
          expected: toProxyUrl('https://example.com/?q=测试', proxyOrigin)
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(toProxyUrl(input, proxyOrigin)).toBe(expected)
      })
    })

    it('should handle special characters in URLs', () => {
      const testCases = [
        {
          input: 'https://example.com/?q=a+b',
          expected: toProxyUrl('https://example.com/?q=a+b', proxyOrigin)
        },
        {
          input: 'https://example.com/?q=a&b=c',
          expected: toProxyUrl('https://example.com/?q=a&b=c', proxyOrigin)
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(toProxyUrl(input, proxyOrigin)).toBe(expected)
      })
    })
  })

  describe('decodePot', () => {
    it('should decode basic base64 string correctly', () => {
      const encoded = 'aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t'
      expect(decodePot(encoded)).toBe('https://tableconvert.com')
    })

    it('should handle padding automatically', () => {
      const testCases = [
        {
          input: 'aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t==', // 两个 padding
          expected: 'https://tableconvert.com'
        },
        {
          input: 'aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t=', // 一个 padding
          expected: 'https://tableconvert.com'
        },
        {
          input: 'aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t', // 无 padding
          expected: 'https://tableconvert.com'
        }
      ]

      testCases.forEach(({ input, expected }) => {
        expect(decodePot(input)).toBe(expected)
      })
    })

    it('should handle non-URL base64 strings', () => {
      const encoded = 'SGVsbG8gV29ybGQ' // "Hello World"
      expect(decodePot(encoded)).toBe('Hello World')
    })

    it('should return original string for invalid base64', () => {
      const invalidCases = [
        'not-base64',
        '!@#$%^&*',
        '',
        'aHR0cHM6Ly90YWJsZWNvbnZlcnQuY29t!' // 包含非base64字符
      ]

      invalidCases.forEach((invalid) => {
        expect(decodePot(invalid)).toBe(invalid)
      })
    })

    it('should handle unicode characters correctly', () => {
      const encoded = '5Lit5paH5rWL6K+V' // "中文测试" in base64
      expect(decodePot(encoded)).toBe('中文测试')
    })

    it('should handle special characters in URLs', () => {
      const encoded = 'aHR0cHM6Ly9leGFtcGxlLmNvbS8/YT0xJmI9MiNzZWN0aW9u'
      expect(decodePot(encoded)).toBe('https://example.com/?a=1&b=2#section')
    })
  })
})
