import { encode, decode } from './base64'

const isWebSocket = (protocol: string) => protocol.startsWith('ws')

const createUrl = (url: string): URL => {
  try {
    if (!url.includes('://')) {
      url = 'https://' + url
    }
    return new URL(url)
  } catch {
    throw new Error('Invalid URL')
  }
}

export function decodePot(pot: string): string {
  try {
    return /^[A-Za-z0-9+/]*={0,4}$/.test(pot) ? decode(pot, false) : pot
  } catch {
    return pot
  }
}

export function toOriginalUrl(proxyUrl: string): string {
  try {
    const url = createUrl(proxyUrl)
    const pot = url.searchParams.get('__pot')
    if (!pot) return proxyUrl

    // 解码并创建原始URL
    const originalUrl = createUrl(decodePot(pot))

    // 构建最终URL
    const finalUrl = new URL(url.href)
    finalUrl.protocol = isWebSocket(url.protocol) ? url.protocol : originalUrl.protocol
    finalUrl.host = originalUrl.host
    finalUrl.pathname = url.pathname === '/' ? originalUrl.pathname : url.pathname
    finalUrl.searchParams.delete('__pot')

    return finalUrl.href
  } catch {
    return proxyUrl
  }
}

export function toProxyUrl(realUrl: string, proxyOrigin: string): string {
  try {
    const originalUrl = createUrl(realUrl)
    const proxyUrl = createUrl(proxyOrigin)

    // 构建代理URL
    const finalUrl = new URL(proxyUrl.href)
    finalUrl.protocol = isWebSocket(originalUrl.protocol) ? originalUrl.protocol : proxyUrl.protocol
    finalUrl.pathname = originalUrl.pathname
    finalUrl.search = originalUrl.search
    finalUrl.hash = originalUrl.hash

    // 添加 pot 参数
    finalUrl.searchParams.set('__pot', encode(originalUrl.origin, false))

    return finalUrl.href
  } catch {
    return realUrl
  }
}

export function formatTargetUrl(url: string): string {
  const urlObj = createUrl(url)
  if (!['http:', 'https:'].includes(urlObj.protocol)) {
    throw new Error('URL must start with http:// or https://')
  }
  return url
}

export function deletePot(urlOrSearch: string): string {
  try {
    if (urlOrSearch.startsWith('?')) {
      const params = new URLSearchParams(urlOrSearch)
      params.delete('__pot')
      return params.toString() ? `?${params.toString()}` : ''
    }

    const url = new URL(urlOrSearch)
    url.searchParams.delete('__pot')
    return url.href
  } catch {
    return urlOrSearch
  }
}

export function normalizeUrl(url: string): string {
  try {
    const { origin, pathname } = new URL(url)
    return origin + pathname
  } catch {
    return url
  }
}
