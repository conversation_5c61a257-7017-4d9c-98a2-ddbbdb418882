/**
 * SEO optimization utilities
 */

/**
 * Truncate meta description to optimal length for SEO
 * @param description - The original description text
 * @param maxLength - Maximum length (default: 155 for optimal SEO)
 * @returns Truncated description with ellipsis if needed
 */
export const truncateMetaDescription = (description: string, maxLength = 155): string => {
  if (!description) return ''

  // Remove extra whitespace and normalize
  const cleanedDescription = description.trim().replace(/\s+/g, ' ')

  if (cleanedDescription.length <= maxLength) {
    return cleanedDescription
  }

  // Find the last complete word before the limit
  const truncated = cleanedDescription.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')

  // If we found a space and it's not too close to the beginning
  if (lastSpaceIndex > maxLength * 0.8) {
    return truncated.substring(0, lastSpaceIndex) + '...'
  }

  // Otherwise, just cut at the character limit
  return truncated + '...'
}

/**
 * Optimize meta description for different contexts
 */
export const optimizeMetaDescription = {
  /** Desktop SEO (155 chars) */
  desktop: (description: string) => truncateMetaDescription(description, 155),

  /** Mobile SEO (130 chars) */
  mobile: (description: string) => truncateMetaDescription(description, 130),

  /** Conservative approach (120 chars) */
  conservative: (description: string) => truncateMetaDescription(description, 120),

  /** Default optimal (155 chars) */
  default: (description: string) => truncateMetaDescription(description, 155)
}
