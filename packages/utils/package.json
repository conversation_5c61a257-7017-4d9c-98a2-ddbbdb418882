{"name": "@momo/utils", "version": "0.0.17", "description": "The utility functions for <PERSON><PERSON>'s projects", "license": "MIT", "author": "momo <<EMAIL>> (https://github.com/momo/)", "homepage": "https://github.com/proxyorb/proxyorb.com#readme", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "bugs": {"url": "https://github.com/proxyorb/proxyorb.com/issues"}, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "files": ["dist"], "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest", "type-check": "tsc --noEmit"}, "sideEffects": ["dist/chunk-*.js", "dist/index.js"], "types": "./dist/index.d.ts", "dependencies": {"clsx": "^2.1.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "vitest": "^2.1.8"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}