import type { SupportedLanguage, I18nConfig } from './types'

/**
 * Create i18n configuration from supported languages
 */
export function createI18nConfig(supportedLanguages: readonly SupportedLanguage[]): I18nConfig {
  return {
    locales: supportedLanguages.map(({ code }) => code),
    defaultLocale: supportedLanguages.find(({ default: isDefault }) => isDefault)?.code ?? 'en'
  }
}

/**
 * Get default language from supported languages
 */
export function getDefaultLanguage(
  supportedLanguages: readonly SupportedLanguage[]
): SupportedLanguage {
  return supportedLanguages.find(({ default: isDefault }) => isDefault) ?? supportedLanguages[0]!
}

/**
 * Check if a locale is supported
 */
export function isSupportedLocale(
  locale: string,
  supportedLanguages: readonly SupportedLanguage[]
): boolean {
  return supportedLanguages.some(({ code }) => code === locale)
}
