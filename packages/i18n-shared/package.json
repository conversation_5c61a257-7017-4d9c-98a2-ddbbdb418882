{"private": true, "name": "@momo/i18n-shared", "version": "0.0.0", "description": "Shared internationalization types and utilities for momo's projects", "license": "MIT", "type": "module", "exports": {"./types": "./types.ts", "./utils": "./utils.ts"}, "scripts": {"clean": "rm -rf .turbo", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "dependencies": {"next-intl": "^3.26.3"}, "peerDependencies": {"next": "^15.2.3"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@types/react": "19.0.2", "next": "^15.2.3"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}