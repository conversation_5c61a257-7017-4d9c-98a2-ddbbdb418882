{"compilerOptions": {"allowJs": true, "baseUrl": ".", "esModuleInterop": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "es6"], "module": "esnext", "moduleResolution": "bundler", "noEmit": true, "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}], "resolveJsonModule": true, "skipLibCheck": true, "strict": true}, "exclude": ["node_modules"], "extends": "@momo/tsconfig/base.json", "include": ["**/*.ts", "**/*.tsx"]}