{"private": true, "name": "@momo/kv", "version": "0.0.0", "description": "The key-value store for <PERSON><PERSON>'s projects", "license": "MIT", "type": "module", "main": "./src/index.ts", "scripts": {"clean": "rm -rf .turbo", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "dependencies": {"@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.3"}, "devDependencies": {"@momo/env": "workspace:*", "@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}