#!/bin/bash
# Inspired by <PERSON><PERSON><PERSON> Kit & Fu<PERSON>

# Function to get absolute filename
get_abs_filename() {
  echo "$(cd "$(dirname "$1")" && pwd)/$(basename "$1")"
}

DIR=$(get_abs_filename $(dirname "$0"))
TMP=$(get_abs_filename "$DIR/../node_modules/.tmp")

mkdir -p $TMP
cd $TMP

if [ "$CI" ]; then
	(umask 0077; echo "$UPDATE_REPO_SSH_KEY" > ~/ssh_key;)
	export GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=accept-new -i ~/ssh_key'
fi

rm -rf eslint-config-inspector
git clone --depth 1 --single-branch --branch main https://github.com/momo/eslint-config-inspector.git

cd eslint-config-inspector
pnpm eslint-config-inspector build
cp -r $DIR/../.eslint-config-inspector/* $TMP/eslint-config-inspector

# Create a README.md file
cat <<EOF > README.md
# eslint-config-inspector

This is a ESLint config inspector generated by [@momo/eslint-config](https://github.com/proxyorb/proxyorb.com/tree/main/packages/eslint-config)
EOF

if [ "$CI" ]; then
  # Set global Git configuration
  git config --global user.name "github-actions[bot]"
  git config --global user.email "github-actions[bot]@users.noreply.github.com"
fi

git add -A
git commit -m "version $npm_package_version"

<NAME_EMAIL>:momo/eslint-config-inspector.git main -f
