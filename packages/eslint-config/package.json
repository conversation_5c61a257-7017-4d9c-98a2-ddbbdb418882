{"name": "@momo/eslint-config", "version": "0.1.19", "description": "The eslint config for <PERSON><PERSON>'s projects", "license": "MIT", "author": "momo <<EMAIL>> (https://github.com/momo/)", "homepage": "https://github.com/proxyorb/proxyorb.com#readme", "repository": {"type": "git", "url": "git+https://github.com/proxyorb/proxyorb.com.git"}, "bugs": {"url": "https://github.com/proxyorb/proxyorb.com/issues"}, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "files": ["dist"], "scripts": {"build": "tsup", "clean": "rm -rf .turbo dist", "dev": "# concurrently \"tsup --watch\" \"eslint-config-inspector\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "postpublish": "bash ./scripts/update-git-repo.sh", "type-check": "tsc --noEmit"}, "sideEffects": false, "types": "./dist/index.d.ts", "dependencies": {"@eslint-react/eslint-plugin": "^1.22.1", "@eslint/js": "^9.17.0", "@next/eslint-plugin-next": "^15.1.3", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.18.2", "eslint-config-flat-gitignore": "^0.3.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sonarjs": "^3.0.1", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-testing-library": "^7.1.1", "eslint-plugin-turbo": "^2.3.3", "eslint-plugin-unicorn": "^56.0.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0"}, "peerDependencies": {"eslint": "^9.10.0"}, "devDependencies": {"@eslint/config-inspector": "^0.7.0", "@momo/tsconfig": "workspace:*", "@types/eslint": "^9.6.1", "eslint": "^9.17.0"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}