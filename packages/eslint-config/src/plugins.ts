// eslint-disable-next-line @typescript-eslint/ban-ts-comment -- missing types
// @ts-nocheck
export { default as eslintPlugin } from '@eslint/js'
export { default as reactPlugin } from '@eslint-react/eslint-plugin'
export { default as nextPlugin } from '@next/eslint-plugin-next'
export { default as typescriptPlugin } from '@typescript-eslint/eslint-plugin'
export { default as typescriptParser } from '@typescript-eslint/parser'
export { default as prettierConfig } from 'eslint-config-prettier'
export { default as eslintCommentsPlugin } from 'eslint-plugin-eslint-comments'
export * as importPlugin from 'eslint-plugin-import'
export { default as jsxA11yPlugin } from 'eslint-plugin-jsx-a11y'
export { default as playwrightPlugin } from 'eslint-plugin-playwright'
export { default as prettierPlugin } from 'eslint-plugin-prettier'
export { default as reactHooksPlugin } from 'eslint-plugin-react-hooks'
export { default as simpleImportSortPlugin } from 'eslint-plugin-simple-import-sort'
export * as sonarjsPlugin from 'eslint-plugin-sonarjs'
export { default as tailwindcssPlugin } from 'eslint-plugin-tailwindcss'
export { default as testingLibraryPlugin } from 'eslint-plugin-testing-library'
export * as turboPlugin from 'eslint-plugin-turbo'
export { default as unicornPlugin } from 'eslint-plugin-unicorn'
export { default as unusedImportsPlugin } from 'eslint-plugin-unused-imports'
