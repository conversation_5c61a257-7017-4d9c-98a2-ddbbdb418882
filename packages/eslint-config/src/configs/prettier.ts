import type { <PERSON><PERSON> } from 'eslint'

import { prettierConfig, prettierPlugin } from '../plugins'

export const prettier: Linter.Config[] = [
  {
    name: 'momo:prettier',
    plugins: {
      prettier: prettierPlugin
    },
    rules: {
      // Avoid conflicts
      ...prettierConfig.rules,

      'prettier/prettier': 'error',
      'arrow-body-style': 'off',
      'prefer-arrow-callback': 'off'
    }
  }
]
