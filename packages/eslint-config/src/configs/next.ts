import type { <PERSON><PERSON> } from 'eslint'

import { nextPlugin } from '../plugins'

export const next: Linter.Config[] = [
  {
    name: 'momo:next',
    plugins: {
      '@next/next': nextPlugin as any
    },
    rules: {
      ...(nextPlugin.configs?.recommended?.rules as any),
      ...(nextPlugin.configs?.['core-web-vitals']?.rules as any),

      '@next/next/no-html-link-for-pages': 'off'
    }
  }
]
