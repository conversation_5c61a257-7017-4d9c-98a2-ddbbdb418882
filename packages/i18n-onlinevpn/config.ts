import type { SupportedLanguage } from '@momo/i18n-shared/types'
import { createI18nConfig } from '@momo/i18n-shared/utils'

export const supportedLanguages = [
  {
    code: 'en',
    label: 'English',
    icon: '🇬🇧',
    default: true
  },
  {
    code: 'es',
    label: 'Esp<PERSON>ñol',
    icon: '🇪🇸'
  },
  {
    code: 'hi',
    label: 'हिन्दी',
    icon: '🇮🇳'
  },
  {
    code: 'ar',
    label: 'العربية',
    icon: '🇸🇦'
  },
  {
    code: 'fr',
    label: 'Français',
    icon: '🇫🇷'
  },
  {
    code: 'ru',
    label: 'Русский',
    icon: '🇷🇺'
  },
  {
    code: 'pt',
    label: 'Português',
    icon: '🇵🇹'
  },
  {
    code: 'id',
    label: 'Bahasa Indonesia',
    icon: '🇮🇩'
  },
  {
    code: 'ja',
    label: '日本語',
    icon: '🇯🇵'
  },
  {
    code: 'de',
    label: 'Deutsch',
    icon: '🇩🇪'
  },
  {
    code: 'bn',
    label: 'বাংলা',
    icon: '🇧🇩'
  },
  {
    code: 'ur',
    label: 'اردو',
    icon: '🇵🇰'
  },
  {
    code: 'fa',
    label: 'فارسی',
    icon: '🇮🇷'
  },
  {
    code: 'tr',
    label: 'Türkçe',
    icon: '🇹🇷'
  },
  {
    code: 'ko',
    label: '한국어',
    icon: '🇰🇷'
  },
  {
    code: 'vi',
    label: 'Tiếng Việt',
    icon: '🇻🇳'
  },
  {
    code: 'it',
    label: 'Italiano',
    icon: '🇮🇹'
  },
  {
    code: 'pa',
    label: 'ਪੰਜਾਬੀ',
    icon: '🇮🇳'
  },
  {
    code: 'zh-Hans',
    label: '简体中文',
    icon: '🇨🇳'
  },
  {
    code: 'zh-Hant',
    label: '繁體中文',
    icon: '🇹🇼'
  }
] as const satisfies readonly SupportedLanguage[]

export const i18n = createI18nConfig(supportedLanguages)
