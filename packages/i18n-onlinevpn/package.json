{"private": true, "name": "@momo/i18n-onlinevpn", "version": "0.0.0", "description": "Internationalization package for OnlineVPN project", "license": "MIT", "type": "module", "exports": {"./config": "./config.ts", "./client": "./client.ts", "./server": "./server.ts", "./middleware": "./middleware.ts", "./routing": "./routing.ts", "./plugin": "./plugin.ts", "./messages/*.json": "./messages/*.json", "./messages/en.json": "./messages/en.json", "./messages/zh-Hans.json": "./messages/zh-Hans.json", "./messages/zh-Hant.json": "./messages/zh-Hant.json", "./messages/es.json": "./messages/es.json", "./messages/fr.json": "./messages/fr.json", "./messages/de.json": "./messages/de.json", "./messages/ja.json": "./messages/ja.json", "./messages/ko.json": "./messages/ko.json", "./messages/pt.json": "./messages/pt.json", "./messages/ru.json": "./messages/ru.json", "./messages/ar.json": "./messages/ar.json", "./messages/hi.json": "./messages/hi.json", "./messages/bn.json": "./messages/bn.json", "./messages/fa.json": "./messages/fa.json", "./messages/id.json": "./messages/id.json", "./messages/it.json": "./messages/it.json", "./messages/pa.json": "./messages/pa.json", "./messages/tr.json": "./messages/tr.json", "./messages/ur.json": "./messages/ur.json", "./messages/vi.json": "./messages/vi.json"}, "scripts": {"clean": "rm -rf .turbo", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "type-check": "tsc --noEmit"}, "dependencies": {"@momo/i18n-shared": "workspace:*", "next-intl": "^3.26.3"}, "peerDependencies": {"next": "^15.2.3"}, "devDependencies": {"@momo/eslint-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@types/react": "19.0.2", "next": "^15.2.3"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}