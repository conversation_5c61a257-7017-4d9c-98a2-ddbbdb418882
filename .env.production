# ---------------------------------------------------------------------------------------------------------
# API Proxy
# ---------------------------------------------------------------------------------------------------------

# API Proxy JWT Secret
JWT_SECRET="8o5%d*(3E?W^M6EuoDvEKye+YU"
LOG_LEVEL=info
FALLBACK_SERVERS=**************,*************

# Redis 连接配置
REDIS_HOST=redis-svc
REDIS_PORT=6379
REDIS_DB=0
REDIS_USERNAME=proxyorb
REDIS_PASSWORD=7o@6ByymWZXdLJpvlExo
REDIS_KEY_PREFIX=proxyorb:

# 心跳配置
HEARTBEAT_WORKER_KEY=worker
HEARTBEAT_ACTIVE_WORKERS_KEY=active_workers

# ---------------------------------------------------------------------------------------------------------
# Authentication
# @see https://next-auth.js.org/getting-started/example
# ---------------------------------------------------------------------------------------------------------
AUTH_SECRET=
AUTH_TRUST_HOST="true"

# Google OAuth
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Github OAuth
# @see https://github.com/settings/applications/new
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# ---------------------------------------------------------------------------------------------------------
# Database URL
# For views, likes, guestbook, comments, authentication.
# ---------------------------------------------------------------------------------------------------------
DATABASE_URL="postgres://postgres:postgres@localhost:5432/proxy"

# ---------------------------------------------------------------------------------------------------------
# Redis
# For caching
# ---------------------------------------------------------------------------------------------------------
UPSTASH_REDIS_REST_URL="http://127.0.01:8079"
UPSTASH_REDIS_REST_TOKEN="proxy"

# ---------------------------------------------------------------------------------------------------------
# Random string (used for hashing)
# To identify a single user when they like the post.
# We hash the IP address so we won't know the actual address.
#
# @see https://www.useapassphrase.com/
# ---------------------------------------------------------------------------------------------------------
IP_ADDRESS_SALT=

# ---------------------------------------------------------------------------------------------------------
# Discord Webhook
# For the notification of new comments from the guestbook.
# ---------------------------------------------------------------------------------------------------------
DISCORD_WEBHOOK_URL=



# ---------------------------------------------------------------------------------------------------------
# React Scan
# To detect performance issues in app
#
# @see https://dashboard.react-scan.com
# ---------------------------------------------------------------------------------------------------------
REACT_SCAN_MONITOR_API_KEY=

# ---------------------------------------------------------------------------------------------------------
# Flags
# To enable or disable some features.
# ---------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_FLAG_COMMENT=false
NEXT_PUBLIC_FLAG_AUTH=false
NEXT_PUBLIC_FLAG_STATS=false
NEXT_PUBLIC_FLAG_ANALYTICS=true
NEXT_PUBLIC_FLAG_GUESTBOOK_NOTIFICATION=false
NEXT_PUBLIC_FLAG_LIKE_BUTTON=false
