#!/bin/sh

# 设置环境变量简化路径引用
NGINX_ROOT=/usr/local/openresty/nginx

# 如果环境变量中没有设置DNS_SERVERS，则从容器内获取
if [ -z "$DNS_SERVERS" ]; then
    # 获取容器内DNS服务器，只保留IPv4地址（过滤掉以2开头的IPv6地址）
    DNS_SERVERS=$(cat /etc/resolv.conf | grep '^nameserver' | awk '{print $2}' | grep -v '^[0-9a-fA-F]*:' | tr '\n' ' ')

    # 如果没有找到DNS服务器，使用默认值
    if [ -z "$DNS_SERVERS" ]; then
        DNS_SERVERS="******* ******* ******* *******"
    fi

    # 确保DNS服务器列表中包含*******和*******
    if ! echo "$DNS_SERVERS" | grep -q "*******"; then
        DNS_SERVERS="$DNS_SERVERS *******"
    fi

    if ! echo "$DNS_SERVERS" | grep -q "*******"; then
        DNS_SERVERS="$DNS_SERVERS *******"
    fi

    # 将DNS_SERVERS导出为环境变量
    export DNS_SERVERS
fi

# 更新Nginx配置
sed -i "s/resolver DNS_PLACEHOLDER/resolver $DNS_SERVERS/" $NGINX_ROOT/conf/nginx.conf

# 删除默认的 html 文件
rm -f $NGINX_ROOT/html/*.html

# 创建SSL证书目录并设置权限
mkdir -p $NGINX_ROOT/ssl
chmod 700 $NGINX_ROOT/ssl

# 创建日志目录并设置权限
mkdir -p $NGINX_ROOT/logs
chown -R nobody:root $NGINX_ROOT/logs
chmod 2755 $NGINX_ROOT/logs

# 启动简单的日志轮转守护进程（如果脚本存在）
if [ -f "/log-rotate.sh" ]; then
    echo "Starting simple log rotation daemon..."
    /log-rotate.sh daemon &
else
    echo "Note: log-rotate.sh not found, logs will not be rotated automatically"
fi

# 启动磁盘监控守护进程（如果脚本存在且配置了 Telegram）
if [ -f "/disk-monitor.sh" ] && [ -n "$TG_BOT_TOKEN" ] && [ -n "$TG_CHAT_ID" ]; then
    echo "Starting disk monitoring daemon..."
    /disk-monitor.sh daemon &
else
    echo "Note: disk-monitor.sh not started (script missing or Telegram not configured)"
fi

# 启动OpenResty
exec openresty -g 'daemon off;'
