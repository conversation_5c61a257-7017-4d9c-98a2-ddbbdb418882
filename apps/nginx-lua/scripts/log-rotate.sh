#!/bin/sh

# =============================================================================
# 简单的 Nginx 日志轮转脚本
# 使用 Nginx 内置的 USR1 信号机制，无需额外依赖
# =============================================================================

set -e

# 配置变量
LOG_DIR="/usr/local/openresty/nginx/logs"
NGINX_PID_FILE="/var/run/nginx.pid"
RETENTION_DAYS="${LOG_RETENTION_DAYS:-7}"
DATE=$(date +%Y%m%d)

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*" >&2
}

# 检查 Nginx 是否运行
check_nginx() {
    if [ ! -f "$NGINX_PID_FILE" ]; then
        log_error "Nginx PID file not found: $NGINX_PID_FILE"
        return 1
    fi
    
    local nginx_pid=$(cat "$NGINX_PID_FILE")
    if ! kill -0 "$nginx_pid" 2>/dev/null; then
        log_error "Nginx process not running (PID: $nginx_pid)"
        return 1
    fi
    
    log_info "Nginx is running (PID: $nginx_pid)"
    return 0
}

# 轮转日志文件
rotate_logs() {
    log_info "Starting log rotation..."
    
    # 检查 Nginx 状态
    if ! check_nginx; then
        return 1
    fi
    
    local nginx_pid=$(cat "$NGINX_PID_FILE")
    
    # 轮转 access.log
    if [ -f "$LOG_DIR/access.log" ] && [ -s "$LOG_DIR/access.log" ]; then
        log_info "Rotating access.log"
        mv "$LOG_DIR/access.log" "$LOG_DIR/access.log.$DATE"
        
        # 后台压缩
        gzip "$LOG_DIR/access.log.$DATE" &
    fi
    
    # 轮转 error.log
    if [ -f "$LOG_DIR/error.log" ] && [ -s "$LOG_DIR/error.log" ]; then
        log_info "Rotating error.log"
        mv "$LOG_DIR/error.log" "$LOG_DIR/error.log.$DATE"
        
        # 后台压缩
        gzip "$LOG_DIR/error.log.$DATE" &
    fi
    
    # 发送 USR1 信号给 Nginx，重新打开日志文件
    log_info "Sending USR1 signal to Nginx (PID: $nginx_pid)"
    kill -USR1 "$nginx_pid"
    
    log_info "Log rotation completed"
}

# 清理过期日志
cleanup_old_logs() {
    log_info "Cleaning up logs older than $RETENTION_DAYS days..."
    
    local deleted_count=0
    
    # 清理 access 日志
    for file in "$LOG_DIR"/access.log.*; do
        if [ -f "$file" ] && [ "$(find "$file" -mtime +$RETENTION_DAYS 2>/dev/null)" ]; then
            log_info "Deleting old file: $(basename "$file")"
            rm -f "$file"
            deleted_count=$((deleted_count + 1))
        fi
    done
    
    # 清理 error 日志
    for file in "$LOG_DIR"/error.log.*; do
        if [ -f "$file" ] && [ "$(find "$file" -mtime +$RETENTION_DAYS 2>/dev/null)" ]; then
            log_info "Deleting old file: $(basename "$file")"
            rm -f "$file"
            deleted_count=$((deleted_count + 1))
        fi
    done
    
    log_info "Cleaned up $deleted_count old log files"
}

# 显示日志统计
show_stats() {
    log_info "Log Statistics:"
    log_info "==============="
    
    # 当前日志文件大小
    for log_file in "$LOG_DIR"/access.log "$LOG_DIR"/error.log; do
        if [ -f "$log_file" ]; then
            local size_mb=$(du -m "$log_file" 2>/dev/null | cut -f1)
            printf "  %-15s: %5d MB\n" "$(basename "$log_file")" "$size_mb"
        fi
    done
    
    # 轮转文件数量
    local access_count=$(ls -1 "$LOG_DIR"/access.log.* 2>/dev/null | wc -l)
    local error_count=$(ls -1 "$LOG_DIR"/error.log.* 2>/dev/null | wc -l)
    
    log_info "  Rotated access logs: $access_count"
    log_info "  Rotated error logs: $error_count"
    
    # 总大小
    local total_size=$(du -sh "$LOG_DIR" 2>/dev/null | cut -f1)
    log_info "  Total log directory size: $total_size"
}

# 启动后台轮转守护进程
start_daemon() {
    log_info "Starting log rotation daemon..."
    
    while true; do
        # 获取当前时间
        local current_hour=$(date +%H)
        local current_minute=$(date +%M)
        
        # 每天凌晨 2:00 执行轮转
        if [ "$current_hour" = "02" ] && [ "$current_minute" = "00" ]; then
            rotate_logs
            cleanup_old_logs
            # 等待 61 秒避免重复执行
            sleep 61
        else
            # 每分钟检查一次
            sleep 60
        fi
    done
}

# 显示帮助
show_help() {
    cat << EOF
Simple Nginx Log Rotation Script

Usage: $0 <command>

Commands:
  rotate      Manually rotate logs
  cleanup     Clean up old log files
  stats       Show log statistics
  daemon      Start background rotation daemon
  help        Show this help

Environment Variables:
  LOG_RETENTION_DAYS    Days to keep old logs (default: 7)

Examples:
  $0 rotate
  $0 stats
  $0 daemon &
EOF
}

# 主函数
main() {
    local command="${1:-help}"
    
    case "$command" in
        "rotate")
            rotate_logs
            ;;
        "cleanup")
            cleanup_old_logs
            ;;
        "stats")
            show_stats
            ;;
        "daemon")
            start_daemon
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
