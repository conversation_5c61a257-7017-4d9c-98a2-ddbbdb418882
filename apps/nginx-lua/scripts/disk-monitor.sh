#!/bin/sh

# =============================================================================
# 磁盘空间监控脚本
# 检查磁盘使用情况并通过 Telegram 发送告警通知
# =============================================================================

set -e

# 配置变量
LOG_DIR="/usr/local/openresty/nginx/logs"
DISK_THRESHOLD="${DISK_THRESHOLD:-80}"
DISK_CHECK_INTERVAL="${DISK_CHECK_INTERVAL:-3600}"  # 1小时
TG_BOT_TOKEN="${TG_BOT_TOKEN:-}"
TG_CHAT_ID="${TG_CHAT_ID:-}"
LAST_ALERT_FILE="/tmp/disk_alert_last_sent"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*" >&2
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $*"
}

# 检查 Telegram 配置
check_telegram_config() {
    if [ -z "$TG_BOT_TOKEN" ] || [ -z "$TG_CHAT_ID" ]; then
        log_error "Telegram configuration missing. Please set TG_BOT_TOKEN and TG_CHAT_ID environment variables."
        return 1
    fi
    return 0
}

# 发送 Telegram 消息
send_telegram_message() {
    local message="$1"

    if ! check_telegram_config; then
        return 1
    fi

    local api_url="https://api.telegram.org/bot${TG_BOT_TOKEN}/sendMessage"
    local payload="chat_id=${TG_CHAT_ID}&text=${message}"

    # 使用 wget 发送请求（Alpine Linux 默认包含）
    if command -v wget >/dev/null 2>&1; then
        local response=$(wget -qO- --post-data="$payload" "$api_url" 2>&1)
        if echo "$response" | grep -q '"ok":true'; then
            log_info "Telegram message sent successfully"
            return 0
        else
            log_error "Failed to send Telegram message: $response"
            return 1
        fi
    else
        log_error "wget not available for sending Telegram messages"
        return 1
    fi
}

# 获取磁盘使用率
get_disk_usage() {
    local path="$1"
    # 使用 df 命令获取使用率，去掉百分号
    df "$path" | awk 'NR==2 {gsub(/%/, "", $5); print $5}'
}

# 检查是否需要发送告警（防重复通知）
should_send_alert() {
    local current_time=$(date +%s)
    local min_interval=3600  # 最少1小时间隔

    if [ -f "$LAST_ALERT_FILE" ]; then
        local last_alert_time=$(cat "$LAST_ALERT_FILE" 2>/dev/null || echo 0)
        local time_diff=$((current_time - last_alert_time))

        if [ "$time_diff" -lt "$min_interval" ]; then
            log_info "Skipping alert (last sent $time_diff seconds ago, minimum interval: $min_interval)"
            return 1
        fi
    fi

    return 0
}

# 记录告警发送时间
record_alert_sent() {
    date +%s > "$LAST_ALERT_FILE"
}

# 检查磁盘使用情况
check_disk_usage() {
    log_info "Checking disk usage..."

    local alert_sent=false

    # 检查根目录
    local root_usage=$(get_disk_usage "/")
    log_info "Root filesystem usage: ${root_usage}%"

    if [ "$root_usage" -gt "$DISK_THRESHOLD" ]; then
        log_warn "Root filesystem usage (${root_usage}%) exceeds threshold (${DISK_THRESHOLD}%)"

        if should_send_alert; then
            local hostname=$(hostname)
            local external_ip="${EXTERNAL_IP:-N/A}"
            local region="${REGION:-N/A}"
            local message="🚨 Disk Alert%0A%0AHost: ${hostname}%0AExternal IP: ${external_ip}%0ARegion: ${region}%0A%0ARoot filesystem usage: ${root_usage}%25%0AThreshold: ${DISK_THRESHOLD}%25%0A%0APlease check disk space immediately!"

            if send_telegram_message "$message"; then
                record_alert_sent
                alert_sent=true
            fi
        fi
    fi

    # 检查日志目录（如果与根目录不同）
    local log_usage=$(get_disk_usage "$LOG_DIR")
    log_info "Log directory usage: ${log_usage}%"

    if [ "$log_usage" -gt "$DISK_THRESHOLD" ] && [ "$log_usage" != "$root_usage" ]; then
        log_warn "Log directory usage (${log_usage}%) exceeds threshold (${DISK_THRESHOLD}%)"

        if should_send_alert && [ "$alert_sent" = false ]; then
            local hostname=$(hostname)
            local external_ip="${EXTERNAL_IP:-N/A}"
            local region="${REGION:-N/A}"
            local message="🚨 Disk Alert%0A%0AHost: ${hostname}%0AExternal IP: ${external_ip}%0ARegion: ${region}%0A%0ALog directory usage: ${log_usage}%25%0AThreshold: ${DISK_THRESHOLD}%25%0A%0APlease check log directory space!"

            if send_telegram_message "$message"; then
                record_alert_sent
            fi
        fi
    fi

    log_info "Disk usage check completed"
}

# 显示磁盘统计信息
show_stats() {
    log_info "Disk Usage Statistics:"
    log_info "====================="

    # 显示根目录使用情况
    local root_usage=$(get_disk_usage "/")
    printf "  %-20s: %3d%% (threshold: %d%%)\n" "Root filesystem" "$root_usage" "$DISK_THRESHOLD"

    # 显示日志目录使用情况
    local log_usage=$(get_disk_usage "$LOG_DIR")
    printf "  %-20s: %3d%%\n" "Log directory" "$log_usage"

    # 显示详细的磁盘信息
    log_info ""
    log_info "Detailed disk information:"
    df -h / "$LOG_DIR" 2>/dev/null | while read line; do
        log_info "  $line"
    done

    # 显示配置信息
    log_info ""
    log_info "Configuration:"
    log_info "  Threshold: ${DISK_THRESHOLD}%"
    log_info "  Check interval: ${DISK_CHECK_INTERVAL}s"
    log_info "  Telegram configured: $([ -n "$TG_BOT_TOKEN" ] && [ -n "$TG_CHAT_ID" ] && echo "Yes" || echo "No")"
}

# 测试 Telegram 通知
test_telegram() {
    log_info "Testing Telegram notification..."

    if ! check_telegram_config; then
        return 1
    fi

    local hostname=$(hostname)
    local external_ip="${EXTERNAL_IP:-N/A}"
    local region="${REGION:-N/A}"
    local message="✅ Test Message%0A%0AHost: ${hostname}%0AExternal IP: ${external_ip}%0ARegion: ${region}%0A%0ADisk monitoring is working correctly!%0ATimestamp: $(date)"

    if send_telegram_message "$message"; then
        log_info "Telegram test successful"
    else
        log_error "Telegram test failed"
        return 1
    fi
}

# 启动守护进程
start_daemon() {
    log_info "Starting disk monitoring daemon..."
    log_info "Check interval: ${DISK_CHECK_INTERVAL} seconds"
    log_info "Disk threshold: ${DISK_THRESHOLD}%"

    while true; do
        check_disk_usage
        sleep "$DISK_CHECK_INTERVAL"
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
Disk Space Monitoring Script

Usage: $0 <command>

Commands:
  check       Check disk usage once
  stats       Show disk usage statistics
  daemon      Start background monitoring daemon
  test        Test Telegram notification
  help        Show this help

Environment Variables:
  TG_BOT_TOKEN         Telegram Bot Token (required)
  TG_CHAT_ID           Telegram Chat ID (required)
  DISK_THRESHOLD       Disk usage threshold percentage (default: 80)
  DISK_CHECK_INTERVAL  Check interval in seconds (default: 3600)

Examples:
  $0 check
  $0 stats
  $0 daemon &
  $0 test

Telegram Setup:
  1. Create a bot with @BotFather on Telegram
  2. Get your chat ID by messaging @userinfobot
  3. Set environment variables:
     export TG_BOT_TOKEN="your_bot_token"
     export TG_CHAT_ID="your_chat_id"
EOF
}

# 主函数
main() {
    local command="${1:-help}"

    case "$command" in
        "check")
            check_disk_usage
            ;;
        "stats")
            show_stats
            ;;
        "daemon")
            start_daemon
            ;;
        "test")
            test_telegram
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
