# 变量定义
DOCKER_COMPOSE = docker-compose
PROJECT_NAME = nginx-lua

# 颜色输出
GREEN = \033[0;32m
NC = \033[0m # No Color

.PHONY: help dev lint

# 默认目标
help:
	@echo "$(GREEN)Nginx Proxy Makefile Help$(NC)"
	@echo "Available commands:"
	@echo "  make dev      - Run container in development mode with logs"
	@echo "  make lint     - Run Lua linter"

# 开发模式运行
dev:
	@echo "$(GREEN)Starting $(PROJECT_NAME) in development mode...$(NC)"
	@mkdir -p logs
	@touch logs/{error,proxy}.log
	@echo '' > logs/error.log
	@echo '' > logs/proxy.log
	@NGINX_ENV=development $(DOCKER_COMPOSE) down $(PROJECT_NAME)-dev
	@NGINX_ENV=development $(DOCKER_COMPOSE) rm -f $(PROJECT_NAME)-dev
	@NGINX_ENV=development $(DOCKER_COMPOSE) build $(PROJECT_NAME)-dev
	@NGINX_ENV=development $(DOCKER_COMPOSE) up -d $(PROJECT_NAME)-dev
	@echo "$(GREEN)--------------tail -f logs/{error,proxy}.log--------------$(NC)"
	# @tail -f logs/{error,proxy}.log || true
	@NGINX_ENV=development $(DOCKER_COMPOSE) logs -f $(PROJECT_NAME)-dev

# 代码检查
lint:
	@echo "$(GREEN)Running Lua linter...$(NC)"
	@if command -v luacheck > /dev/null; then \
		luacheck lua/; \
	else \
		echo "luacheck not found. Please install: luarocks install luacheck"; \
	fi
