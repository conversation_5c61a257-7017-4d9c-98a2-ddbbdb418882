# ProxyOrb Nginx-Lua 项目完整业务流程分析

## 项目概览

ProxyOrb 是一个基于 OpenResty (Nginx + Lua) 构建的**动态代理服务器**，核心特点包括：

- 通过 URL 参数动态指定代理目标
- 支持 HTTP/HTTPS/WebSocket 协议
- 具备完整的认证、会话管理和前端集成能力
- 高性能、可扩展的代理架构

## 系统架构

### 核心组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Nginx 配置层                              │
├─────────────────────────────────────────────────────────────┤
│ • HTTP/HTTPS 服务器 (端口 80/443)                           │
│ • Stream 模块 (Redis SSL 代理)                              │
│ • 静态文件服务 (Service Worker 脚本)                        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Lua 中间件层                              │
├─────────────────────────────────────────────────────────────┤
│ • 请求 ID 生成                                              │
│ • 会话管理 (JWT)                                            │
│ • 认证检查                                                  │
│ • HTTP 处理器                                               │
│ • 响应处理                                                  │
│ • 响应体修改                                                │
│ • 指标统计                                                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    工具层                                    │
├─────────────────────────────────────────────────────────────┤
│ • URL 编解码 (Base64)                                       │
│ • Cookie 重写                                               │
│ • Referer 重写                                              │
│ • 重定向处理                                                │
│ • Redis 连接                                                │
│ • 心跳监控                                                  │
└─────────────────────────────────────────────────────────────┘
```

### 文件结构

```
apps/nginx-lua/
├── conf/                    # Nginx 配置文件
│   ├── nginx.conf          # 主配置文件
│   ├── stream.conf         # Stream 模块配置
│   └── conf.d/             # 子配置文件
│       ├── main.conf       # 主服务器配置
│       ├── maps.conf       # 映射配置
│       └── includes/       # 包含文件
├── lua/                    # Lua 脚本
│   ├── core/              # 核心模块
│   ├── handlers/          # 处理器
│   ├── middleware/        # 中间件
│   └── utils/             # 工具函数
├── html/                  # 静态文件
└── docs/                  # 文档
```

## 详细业务流程

### 1. 初始化阶段

#### 系统启动流程

**1.1 Nginx 主配置加载** (`nginx.conf`)

- 设置 worker 进程数和连接数限制
- 配置共享内存字典：
  - `limit_req`: 限流控制
  - `proxy_cache`: 代理缓存
  - `metrics`: 指标统计
  - `log_dict`: 日志缓存
- 加载 Lua 包路径和缓存设置
- 设置 DNS 解析器和 Cloudflare IP 白名单

**1.2 Lua 核心初始化** (`lua/core/init.lua`)

- 初始化共享内存字典
- 设置日志级别
- 输出初始化信息

**1.3 Worker 进程初始化** (`lua/core/init_worker.lua`)

- 输出环境变量信息
- 初始化心跳服务 (如果启用)
- 初始化指标统计
- 设置定时任务：
  - 日志清理定时器
  - Worker 退出检查定时器
  - 心跳定时器

### 2. 请求处理流程

#### 完整的请求生命周期

```mermaid
graph TD
    A[客户端请求] --> B{检查协议}
    B -->|HTTP| C[重定向到 HTTPS]
    B -->|HTTPS| D[生成请求 ID]

    D --> E[会话处理]
    E --> F{检查会话}
    F -->|无会话/无效| G[返回 Loading 页面]
    F -->|有效会话| H[认证检查]

    G --> I[前端异步验证]
    I --> J[设置 Cookie]
    J --> K[注册 Service Worker]
    K --> L[重定向到干净 URL]

    H --> M{检查 __pot 参数}
    M -->|缺失| N[返回错误]
    M -->|存在| O[HTTP 处理器]

    O --> P[解码目标 URL]
    P --> Q[设置代理变量]
    Q --> R[重写请求头]
    R --> S[代理转发]

    S --> T[响应头处理]
    T --> U[响应体处理]
    U --> V[返回给客户端]
```

#### 2.1 协议检查与重定向

- **HTTP 请求**: 自动重定向到 HTTPS (除健康检查端点外)
- **HTTPS 请求**: 继续处理流程
- **请求 ID 生成**: 为每个请求分配唯一标识符

#### 2.2 会话与认证处理

**会话获取** (`middleware/session.lua`)

1. 优先从 URL 参数 `__poss` 获取会话令牌
2. 其次从 Cookie 获取
3. 验证 JWT 令牌的有效性和过期时间

**认证检查** (`middleware/auth.lua`)

1. 检查会话是否已验证
2. 验证 `__pot` 参数是否存在
3. 如果会话来自 URL 参数，返回 loading 页面

**Loading 页面机制**

1. 返回轻量级 HTML 页面
2. 加载 JavaScript 脚本进行异步验证
3. 注册 Service Worker
4. 设置认证 Cookie
5. 清理 URL 参数并重定向

**API 端点** (`/__poa`)

- 验证会话有效性
- 设置 HttpOnly Cookie
- 返回验证结果

### 3. HTTP 代理处理

#### 3.1 URL 解析与重写

**URL 解析** (`handlers/http.lua`)

1. 从 `__pot` 参数获取 Base64 编码的目标 URL
2. 解码获取原始目标域名和协议
3. 构建完整的代理目标 URL

**请求重写**

- 转发所有请求头（除 host 外）
- 重写 Cookie 和 Referer 头部
- 对 Service Worker 脚本清除 Accept-Encoding

#### 3.2 响应处理

**响应头处理** (`middleware/response.lua`)

- 设置安全头部：
  - `X-Content-Type-Options: nosniff`
  - `X-Frame-Options: SAMEORIGIN`
  - `X-XSS-Protection: 1; mode=block`
- 重写响应 Cookie
- 处理 CORS 头部
- 处理重定向 Location 头部
- 隐藏服务器信息

**响应体修改** (`middleware/body.lua`)

- 检测 Service Worker 脚本 (带 `__posw=1` 参数的 JS 文件)
- 注入代理脚本包装器
- 缓存响应块并在最后一块时统一处理

### 4. WebSocket 支持

**WebSocket 代理配置**

- 专门的 `/__pows` 端点处理 WebSocket 连接
- 设置适当的 Upgrade 和 Connection 头部
- 配置长连接超时 (3600s)
- 禁用代理缓冲以支持实时通信

### 5. 静态文件服务

**前端资源管理**

- `__po.sw.js`: Service Worker 脚本
- `__po.loading.js`: Loading 页面脚本
- `__po.intcp.js`: 拦截器脚本
- `__po.fp.js`: 指纹识别脚本
- `__po.pm.js`: 页面管理脚本

## 关键技术特性

### URL 编码机制

- 使用 URL 安全的 Base64 编码目标 URL
- 自动添加/移除 padding
- 支持多种协议：HTTP/HTTPS/WebSocket/FTP/RTMP/RTSP

### 安全特性

- JWT 会话令牌验证
- HttpOnly 和 Secure Cookie
- CSRF 防护 (SameSite=None)
- 内容安全策略 (CSP)
- 服务器信息隐藏

### 性能优化

- 共享内存缓存
- 连接池管理
- 异步处理
- 响应缓冲优化
- 定时清理任务

### 监控与日志

- 请求 ID 追踪
- 指标统计收集
- 心跳监控
- 结构化日志记录
- Redis 状态监控

## 部署架构

### 基础设施组件

- **Nginx/OpenResty**: 主要代理服务器
- **Redis**: 会话存储和状态管理 (通过 SSL 连接)
- **前端脚本**: Service Worker 和页面管理
- **SSL 证书**: HTTPS 和 Redis SSL 连接

### 环境配置

- 支持开发/生产环境切换
- 环境变量配置管理
- Docker 容器化部署
- 健康检查端点

## 使用场景

这个代理服务器主要适用于：

1. **网络访问增强**: 通过代理访问受限网站
2. **隐私保护**: 隐藏真实 IP 和身份信息
3. **开发调试**: 快速创建临时代理点
4. **内容过滤**: 在代理层面修改请求/响应
5. **负载均衡**: 动态路由到不同后端服务

## 总结

ProxyOrb 项目展现了 OpenResty 在构建高性能、功能丰富的代理服务器方面的强大能力。通过 Lua 脚本实现了复杂的业务逻辑，同时保持了良好的性能和可扩展性。

项目的核心优势：

- **动态性**: 通过 URL 参数动态指定代理目标
- **安全性**: 完整的认证和会话管理机制
- **性能**: 基于 OpenResty 的高性能架构
- **可扩展性**: 模块化的中间件设计
- **兼容性**: 支持多种协议和现代 Web 技术
