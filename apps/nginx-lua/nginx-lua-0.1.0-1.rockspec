package = "nginx-lua"
version = "0.1.0-1"
source = {
   url = "git://github.com/your-username/nginx-lua",
   tag = "v0.1.0"
}
description = {
   summary = "A dynamic proxy server based on OpenResty",
   detailed = [[
      A proxy server that can dynamically route requests based on URL parameters,
      with support for HTTP, HTTPS and WebSocket protocols.
   ]],
   homepage = "https://github.com/your-username/nginx-lua",
   license = "MIT"
}
dependencies = {
   "lua >= 5.1",
   "lua-resty-jwt",
   "lua-resty-redis",
   "lua-resty-cookie"
}
build = {
   type = "make"
}
