# 心跳服务说明

本项目实现了基于Redis的心跳机制，用于维护worker节点的可用性和状态信息。采用单独Worker键 + Sorted Set二级索引的设计，充分利用Redis的自动过期机制，实现高效、可靠的心跳服务。

## 工作原理

1. **服务注册**：worker启动时向Redis注册自己的信息
2. **心跳保活**：worker定期发送心跳更新Redis中的信息和时间戳
3. **自动过期**：利用Redis的TTL机制，超时未更新的worker键自动过期
4. **二级索引**：使用Sorted Set维护活跃worker列表，支持按时间戳排序
5. **增量清理**：心跳过程中随机执行增量清理，保持索引一致性
6. **退出处理**：worker退出时自动从索引中移除
7. **自动恢复**：当索引键丢失时，能够自动从worker键恢复

## 配置说明

心跳服务的配置位于`lua/core/config.lua`文件中：

```lua
-- Redis配置
_M.redis = {
    host = os.getenv("REDIS_HOST"),           -- Redis服务器地址
    port = tonumber(os.getenv("REDIS_PORT")), -- Redis服务器端口
    username = os.getenv("REDIS_USERNAME"),   -- Redis用户名（Redis 6.0+）
    password = os.getenv("REDIS_PASSWORD"),   -- Redis密码
    db_index = tonumber(os.getenv("REDIS_DB")), -- Redis数据库索引
    timeout = 1000,                           -- 连接超时时间（毫秒）
    pool_size = 100,                          -- 连接池大小
    idle_timeout = 60000,                     -- 空闲连接超时时间（毫秒）
    key_prefix = os.getenv("REDIS_KEY_PREFIX"), -- Redis键前缀
}

-- 心跳配置
_M.heartbeat = {
    enabled = os.getenv("HEARTBEAT_ENABLED") == "true", -- 是否启用心跳功能
    worker_ttl = tonumber(os.getenv("HEARTBEAT_WORKER_TTL")) or 60, -- worker过期时间（秒）
    worker_key = os.getenv("HEARTBEAT_WORKER_KEY") or "worker:", -- worker键前缀
    active_workers_key = os.getenv("HEARTBEAT_ACTIVE_WORKERS_KEY") or "active_workers" -- 活跃worker索引键名
}
```

## 环境变量

可以通过以下环境变量配置心跳服务：

- `REDIS_HOST`: Redis服务器地址
- `REDIS_PORT`: Redis服务器端口
- `REDIS_USERNAME`: Redis用户名（Redis 6.0+）
- `REDIS_PASSWORD`: Redis密码
- `REDIS_DB`: Redis数据库索引
- `REDIS_KEY_PREFIX`: Redis键前缀
- `HEARTBEAT_ENABLED`: 是否启用心跳功能（true/false）
- `HEARTBEAT_INTERVAL`: 心跳发送间隔（秒）
- `HEARTBEAT_WORKER_TTL`: worker过期时间（秒）
- `HEARTBEAT_WORKER_KEY`: worker键前缀
- `HEARTBEAT_ACTIVE_WORKERS_KEY`: 活跃worker索引键名

## Redis数据结构

心跳服务使用两个Redis数据结构：

1. **单独键(String)**：存储每个worker的详细信息
   - 键名：`{redis.key_prefix}{heartbeat.worker_key}{worker_id}`
   - 示例：`proxyorb:worker:127.0.0.1`
   - 值：worker详细信息（JSON格式）
   - 特性：设置TTL，自动过期

2. **有序集合(Sorted Set)**：存储活跃worker的ID和时间戳
   - 键名：`{redis.key_prefix}{heartbeat.active_workers_key}`
   - 示例：`proxyorb:active_workers`
   - 成员：worker ID
   - 分数：最后心跳时间（Unix时间戳）
   - 特性：**永久存在**，通过清理机制维护一致性

## 核心功能实现

### 1. 心跳注册

```lua
-- 通过Redis注册 - 单独Worker键 + Sorted Set索引
local function register_to_redis(worker_info)
    local worker_id = worker_info.id
    local worker_key = config.redis.key_prefix .. config.heartbeat.worker_key .. worker_id
    local index_key = config.redis.key_prefix .. config.heartbeat.active_workers_key
    local current_time = ngx.now()
    local ttl = config.heartbeat.worker_ttl
    local worker_json = cjson.encode(worker_info)

    -- 使用Lua脚本原子性执行多个操作
    local script = [[
        -- 1. 设置worker详细信息，带TTL
        redis.call('SETEX', KEYS[1], ARGV[1], ARGV[2])

        -- 2. 添加到活跃worker索引
        redis.call('ZADD', KEYS[2], ARGV[3], ARGV[4])

        -- 3. 获取当前活跃worker数量
        local count = redis.call('ZCARD', KEYS[2])

        return count
    ]]

    -- 执行Lua脚本并返回结果
    -- ...
end
```

### 2. 获取活跃Worker列表

```lua
-- 获取活跃worker列表 - 从Sorted Set索引获取
function _M.get_active_workers()
    local index_key = config.redis.key_prefix .. config.heartbeat.active_workers_key
    local current_time = ngx.now()
    local expire_time = current_time - config.heartbeat.worker_ttl
    local worker_key = config.redis.key_prefix .. config.heartbeat.worker_key

    -- 使用Lua脚本原子性获取活跃worker
    local script = [[
        -- 检查索引键是否存在
        local exists = redis.call('EXISTS', KEYS[1])
        if exists == 0 then
            return {workers = {}, count = 0, exists = false}
        end

        -- 1. 获取活跃的worker IDs
        local worker_ids = redis.call('ZRANGEBYSCORE', KEYS[1], ARGV[1], '+inf')
        -- 2. 批量获取worker信息
        -- 3. 清理过期的worker索引
        -- ...
    ]]

    -- 如果索引键不存在，尝试使用SCAN恢复
    -- ...
}
```

### 3. 清理过期Worker

```lua
-- 清理过期worker - 基于Sorted Set的清理
function _M.cleanup_expired_workers()
    local index_key = config.redis.key_prefix .. config.heartbeat.active_workers_key
    local current_time = ngx.now()
    local expire_time = current_time - config.heartbeat.worker_ttl
    local worker_key = config.redis.key_prefix .. config.heartbeat.worker_key

    -- 使用Lua脚本原子性执行清理操作
    local script = [[
        -- 1. 使用ZRANGEBYSCORE获取所有可能过期的worker
        local expired_workers = redis.call('ZRANGEBYSCORE', KEYS[1], 0, ARGV[1])

        -- 2. 检查每个worker的键是否存在
        -- 3. 从索引中删除过期worker
        -- ...
    ]]
}
```

### 4. 退出处理

```lua
-- 设置退出处理函数，确保worker退出时从索引中移除
function _M.setup_exit_handler()
    local worker_id = get_external_ip()
    local index_key = config.redis.key_prefix .. config.heartbeat.active_workers_key

    -- 注册进程退出处理函数
    ngx.on_exit(function()
        -- 尝试从Redis索引中移除
        pcall(function()
            return redis_util.exec(function(redis)
                redis:zrem(index_key, worker_id)
                return true
            end)
        end)
    end)
}
```

## 优势特点

1. **自动过期**：利用Redis的键过期机制自动清理过期worker，无需复杂的手动清理逻辑
2. **高效查询**：使用Sorted Set作为二级索引，支持按时间戳排序和范围查询
3. **实现简单**：代码逻辑清晰，易于理解和维护
4. **高可靠性**：多重保障机制，确保数据一致性
5. **高扩展性**：适用于各种规模的部署，从小型到大型
6. **增量清理**：心跳过程中随机执行增量清理，分散系统负担
7. **自动恢复**：当索引键丢失时，能够自动从worker键恢复
8. **退出处理**：worker退出时自动从索引中移除，保持索引干净
9. **重试机制**：心跳发送失败时自动重试，提高可靠性
10. **并发优化**：移除索引键TTL设置，减少竞争问题

## 并发优化

为了解决多个worker同时操作同一个索引键的竞争问题，本实现采用了以下优化策略：

1. **移除索引键TTL**：索引键不再设置过期时间，减少了多个worker同时尝试更新TTL的竞争
2. **原子操作**：使用Lua脚本确保worker注册、获取和清理操作的原子性
3. **增量清理**：随机执行清理操作，分散系统负担
4. **退出处理**：worker退出时自动从索引中移除，减少垃圾数据
5. **自动恢复**：当索引键丢失时，能够自动从worker键恢复

## Master端实现建议

Master端需要实现以下功能：

1. **获取活跃Worker列表**：
   - 从Redis中获取活跃worker列表
   - 使用`ZRANGEBYSCORE`命令获取指定时间范围内的worker

   ```lua
   -- 获取活跃worker列表
   local index_key = "proxyorb:active_workers"
   local current_time = ngx.now()
   local expire_time = current_time - 60 -- 60秒过期时间

   local worker_ids = redis:zrangebyscore(index_key, expire_time, "+inf")
   ```

2. **获取Worker详细信息**：
   - 根据worker ID获取详细信息
   - 使用管道批量获取多个worker信息

   ```lua
   -- 批量获取worker信息
   redis:init_pipeline()
   for _, worker_id in ipairs(worker_ids) do
       local worker_key = "proxyorb:worker:" .. worker_id
       redis:get(worker_key)
   end

   local results = redis:commit_pipeline()
   ```

3. **负载均衡**：
   - 根据worker负载情况进行智能分配
   - 考虑连接数、请求数等因素
   - 可以利用Sorted Set的分数机制实现基于负载的排序

## 部署说明

1. 确保Redis服务可用（推荐Redis 6.0+）
2. 设置正确的环境变量
3. 启动worker服务
4. 确认心跳服务正常工作：
   ```bash
   # 检查Redis中的worker信息
   redis-cli -h <redis-host> -p <redis-port> -a <password> --no-auth-warning
   > ZRANGE proxyorb:active_workers 0 -1 WITHSCORES
   > GET proxyorb:worker:127.0.0.1
   ```

## 故障排查

1. **心跳服务未启动**：
   - 检查Redis连接是否正常
   - 查看日志中是否有错误信息
   - 确认`HEARTBEAT_ENABLED`环境变量是否设置为`true`

2. **worker信息未正确存储**：
   - 检查Redis中是否有worker键
   - 检查worker键的TTL是否正确设置
   - 检查worker ID是否正确生成

3. **索引不一致**：
   - 检查活跃worker索引是否包含正确的worker ID
   - 手动触发清理过程：`_M.cleanup_expired_workers()`
   - 检查是否有多个worker使用相同的worker ID

4. **Redis连接问题**：
   - 检查Redis服务器地址和端口是否正确
   - 检查用户名和密码是否正确
   - 检查网络连接是否正常

5. **索引键丢失**：
   - 检查日志中是否有"活跃worker索引键不存在"的警告
   - 确认自动恢复机制是否正常工作
   - 手动触发获取活跃worker列表，触发自动恢复
