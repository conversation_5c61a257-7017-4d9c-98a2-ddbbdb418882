# OpenResty 执行阶段与处理顺序

## Nginx 请求处理阶段与 Lua 执行顺序

OpenResty 允许在 Nginx 处理请求的不同阶段注入 Lua 代码，下面是这些阶段的完整执行顺序：

1. **init_by_lua_block**
   - 在 Nginx master 进程加载配置时执行一次
   - 通常用于预加载模块、初始化全局配置
   - 只执行一次，所有 worker 进程共享

2. **init_worker_by_lua_block**
   - 在每个 Nginx worker 进程启动时执行
   - 用于设置定时任务（ngx.timer.at）、初始化 worker 级别的资源
   - 每个 worker 进程执行一次

3. **ssl_certificate_by_lua_block**
   - SSL 握手阶段执行
   - 用于动态处理 SSL 证书和密钥
   - 可实现 SNI 支持、动态证书选择等功能

4. **set_by_lua_block**
   - 用于设置变量值
   - 在 rewrite 阶段执行，但先于 rewrite_by_lua_block
   - 执行环境受限，不支持异步操作和子请求

5. **rewrite_by_lua_block**
   - 在 Nginx rewrite 阶段执行
   - 用于 URL 重写、设置变量和请求属性修改
   - 可以使用 ngx.req.\* API 修改请求

6. **access_by_lua_block**
   - 在 Nginx access 阶段执行
   - 用于访问控制、身份认证和授权
   - 可以通过 ngx.exit 返回状态码中断请求

7. **balancer_by_lua_block**
   - 用于实现动态负载均衡
   - 在 Nginx 的负载均衡阶段执行
   - 可动态选择上游服务器

8. **content_by_lua_block**
   - 生成内容阶段
   - 是完整的请求处理器，可以生成响应内容
   - 与 proxy_pass 等内容处理指令互斥

9. **header_filter_by_lua_block**
   - 响应头过滤阶段
   - 可以修改、添加、删除响应头
   - 执行于发送响应头之前

10. **body_filter_by_lua_block**
    - 响应体过滤阶段
    - 可以修改响应体内容
    - 分块处理大型响应

11. **log_by_lua_block**
    - 日志记录阶段
    - 请求结束后执行，不影响响应返回
    - 适合进行请求统计和日志记录

## 典型的代理请求处理流程

在一个正向代理或反向代理应用中，一个请求的典型处理流程为：

```
客户端请求 → access_by_lua_block（认证） → rewrite_by_lua_block（请求处理）
→ proxy_pass（转发请求） → header_filter_by_lua_block（处理响应头）
→ body_filter_by_lua_block（处理响应体）→ log_by_lua_block（日志记录）→ 响应返回客户端
```

## 各阶段上下文和限制

各个执行阶段有不同的上下文限制：

| 阶段            | 支持异步操作 | 支持 yield  | 支持子请求 | 支持修改请求 | 支持修改响应 |
| --------------- | ------------ | ----------- | ---------- | ------------ | ------------ |
| init            | 否           | 否          | 否         | N/A          | N/A          |
| init_worker     | 是（timer）  | 是（timer） | 否         | N/A          | N/A          |
| ssl_certificate | 是           | 是          | 否         | N/A          | N/A          |
| set             | 否           | 否          | 否         | 否           | 否           |
| rewrite         | 是           | 是          | 是         | 是           | 否           |
| access          | 是           | 是          | 是         | 是           | 否           |
| balancer        | 否           | 否          | 否         | 否           | 否           |
| content         | 是           | 是          | 是         | 是           | 是           |
| header_filter   | 否           | 否          | 否         | 否           | 仅响应头     |
| body_filter     | 否           | 否          | 否         | 否           | 仅响应体     |
| log             | 是           | 是          | 是         | 否           | 否           |

## 性能考虑

在选择使用哪个执行阶段时，应考虑以下因素：

1. **尽早拒绝非法请求**：在 access 阶段进行认证和授权，可以避免不必要的处理
2. **最小化执行时间**：特别是在 header_filter 和 body_filter 阶段，这些阶段会处理每个响应包
3. **合理使用异步操作**：在支持异步的阶段，使用 ngx.timer.\* 避免阻塞请求处理
4. **数据共享**：使用共享内存（ngx.shared.DICT）在请求和 worker 之间共享数据

## 特殊场景

1. **WebSocket 处理**：需要特别关注 header_filter 阶段以确保正确传递 upgrade 头
2. **大文件传输**：在 body_filter 阶段需谨慎处理，避免内存占用过高
3. **动态上游选择**：使用 balancer_by_lua_block 实现细粒度的负载均衡策略

## 最佳实践

1. 在合适的阶段执行相应的逻辑，避免跨阶段依赖
2. 使用 lua_shared_dict 在不同请求间共享数据
3. 合理设置共享内存大小，避免内存耗尽
4. 在关键路径上最小化 Lua 代码执行时间
5. 使用内置缓存机制提高性能
