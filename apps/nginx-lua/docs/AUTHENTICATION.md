# 前置加载与异步鉴权服务

本项目实现了基于前置加载页面的异步鉴权机制，通过简洁的loading页面减少用户等待时间，并在后台完成token验证、ServiceWorker注册等工作。整个流程对用户无感知，提供更流畅的代理体验。

## 工作原理

1. **首次访问**：用户访问带有`__pot`和`__poss`参数的URL
2. **前置加载**：服务器返回轻量级loading页面
3. **异步验证**：JS从URL读取token，异步发送到后端验证
4. **状态持久化**：验证成功后设置`__poss` cookie
5. **加载ServiceWorker**：注册并激活代理功能所需的ServiceWorker
6. **URL清理**：自动跳转到不带token参数的干净URL
7. **后续请求**：基于cookie进行鉴权，无需重复验证

## 流程图

```mermaid
graph TD
    A[用户请求] --> B{检查URL中__poss}
    B -->|有token| C[返回简洁loading页]
    B -->|无token| D[返回错误页面]
    C --> E[执行__po.loading.js]
    E --> F[异步验证token]
    F -->|成功| G[种下__poss cookie]
    F -->|失败| H[显示错误信息]
    G --> I[注册ServiceWorker]
    I --> J[刷新到相同URL但移除token]
    J --> K[正常代理流程]
```

## 配置说明

鉴权服务的配置位于`lua/core/config.lua`文件中：

```lua
-- 基础配置
_M.base = {
    session_key = "__poss",   -- 认证cookie名称
    pot_key = "__pot",    -- 目标URL参数名
    special_paths = {
        favicon = "/favicon.ico",
        static_prefix = "/__po."
    }
}
```

## URL参数说明

- `__pot`：目标URL参数，Base64编码的目标网站域名
- `__poss`：会话令牌参数，用于验证用户身份

## 鉴权流程详解

### 1. 初始请求处理

当用户首次访问系统时，请求URL应包含以下参数：

- `https://proxy.example.com/?__pot=<encoded_url>&__poss=<token>`

系统会检查URL参数，并根据参数存在情况做出不同处理：

```lua
function _M.check()
    -- 获取URL参数
    local args = ngx.req.get_uri_args()
    local token = args[TOKEN_pot_key]  -- __poss
    local pot = args[pot_key]          -- __pot

    -- 获取cookie
    local cookie_obj = cookie:new()
    local pos = cookie_obj:get(session_key)

    -- 如果有cookie且有pot参数，验证通过
    if pos and pot then
        return set_auth_status(true)
    end

    -- 如果有token，提供loading页面
    if token then
        return serve_loading_page()
    end

    -- 缺少必要参数，返回错误
    exception.throw_unauthorized("Missing authentication token")
end
```

### 2. 前置Loading页面

系统返回的loading页面极为简洁：

```html
<!doctype html>
<html>
  <head>
    <title>Loading</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="/__po.fp.js"></script>
    <script src="/__po.loading.js"></script>
    <script preload src="/__po.intcp.js"></script>
  </head>
  <body>
    <!-- 页面内容由JS动态创建 -->
  </body>
</html>
```

### 3. 异步验证流程

loading.js负责处理完整的验证流程：

```javascript
// 验证token
private async verifyToken(): Promise<boolean> {
  try {
    // 更新加载文本
    this.loadingText.textContent = 'Verifying credentials...';

    // 发送验证请求
    const response = await fetch('/__poa', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({token: this.config.token})
    });

    // 只检查状态码
    if (response.status !== 200) {
      throw new Error(`Authentication failed with status: ${response.status}`);
    }

    return true;
  } catch (error) {
    this.showError(error.message || 'Verification failed');
    return false;
  }
}
```

### 4. API端点实现

`/__poa`端点处理token验证和cookie设置：

```lua
function _M.handle_auth_api()
    -- 获取请求体中的token
    ngx.req.read_body()
    local body = cjson.decode(ngx.req.get_body_data() or "{}")
    local token = body.token

    -- 简单检查token存在性
    if not token or token == "" then
        return ngx_exit(ngx.HTTP_BAD_REQUEST)
    end

    -- 设置cookie
    local cookie_obj = cookie:new()
    cookie_obj:set({
        key = "__poss",
        value = token,
        path = "/",
        httponly = true,
        secure = ngx.var.scheme == "https",
        samesite = "Strict",
        max_age = 86400 * 7  -- 7天有效期
    })

    -- 只返回200状态码，不返回内容
    return ngx_exit(ngx.HTTP_OK)
end
```

### 5. ServiceWorker注册

验证成功后，系统注册ServiceWorker：

```javascript
private async registerServiceWorker(): Promise<void> {
  try {
    const swManager = ServiceWorkerManager.getInstance();

    // 注册并等待激活
    await swManager.register({immediate: true});
    await swManager.waitForControl();

    console.log('[Client] Service Worker registered and activated');
  } catch (error) {
    throw error;
  }
}
```

### 6. URL清理及跳转

完成所有步骤后，系统会清理URL中的token参数：

```javascript
private buildCleanUrl(): string {
  const url = new URL(window.location.href);
  url.searchParams.delete('__poss'); // 移除token参数
  return url.toString();
}

// 跳转到干净URL
window.location.replace(cleanUrl);
```

## 安全特性

1. **HttpOnly Cookie**：防止客户端JavaScript访问cookie
2. **Secure标志**：确保cookie只通过HTTPS传输
3. **SameSite策略**：防止跨站请求伪造(CSRF)攻击
4. **短暂令牌**：URL中的token仅用于首次验证，验证后立即从URL移除
5. **无内容响应**：API端点验证成功时不返回内容，减少信息泄露

## Nginx配置

```nginx
# 认证API端点
location = /__poa {
    access_by_lua_block {
        require("middleware.request_id").handle()
    }

    # 设置安全相关头部
    add_header X-Content-Type-Options "nosniff";
    add_header Cache-Control "no-store, no-cache, must-revalidate";

    # 调用认证处理函数
    content_by_lua_block {
        require("middleware.auth").handle_auth_api()
    }
}
```

## 优势特点

1. **用户体验优化**：前置loading页面让用户立即看到反馈
2. **无感加载**：异步验证和ServiceWorker注册不阻塞用户体验
3. **URL清洁**：验证后自动移除token参数，增强安全性
4. **轻量实现**：HTML极简，复杂逻辑放在JS中
5. **渐进增强**：显示友好的加载文本和精美动画
6. **错误处理**：验证失败时显示清晰的错误信息
7. **兼容性好**：支持各种现代浏览器
8. **安全可靠**：采用多种安全措施保护用户信息

## 客户端集成

要集成此鉴权系统，客户端需要：

1. 生成合法token（具体生成方式取决于授权机制）
2. 构建带有`__pot`和`__poss`参数的URL
3. 引导用户访问构建的URL

示例URL：

```
https://proxy.example.com/?__pot=aHR0cHM6Ly9leGFtcGxlLmNvbQ==&__poss=eyJpZCI6InVzZXIxIn0=
```

## 故障排查

1. **验证失败**：
   - 检查token是否正确格式
   - 查看浏览器控制台错误信息
   - 检查网络请求状态码

2. **Cookie未设置**：
   - 确认浏览器未禁用cookie
   - 检查Secure标志是否与当前协议匹配
   - 查看服务端日志中cookie设置是否成功

3. **ServiceWorker未注册**：
   - 确认浏览器支持ServiceWorker
   - 检查ServiceWorker脚本是否能正确加载
   - 查看浏览器控制台中的注册错误

4. **URL参数问题**：
   - 确保`__pot`参数正确编码
   - 确保`__poss`参数存在且有效
   - URL编码特殊字符以避免解析问题
