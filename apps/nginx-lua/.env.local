# Nginx Lua 开发环境配置

# 系统配置
TZ=Asia/Shanghai

# 服务标识配置
HOSTNAME=worker-dev
EXTERNAL_IP=dp.local
REGION=default

# 运行环境配置
NGINX_ENV=development
JWT_SECRET=8o5e+YU[hrsCe\_cSim|

# Redis 连接配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6380
REDIS_DB=0
REDIS_USERNAME=proxyorb
REDIS_PASSWORD=7o@6ByymWZXdLJpvlExo
REDIS_KEY_PREFIX=proxyorb:

# 日志轮转配置（由外部脚本处理）
LOG_LEVEL=INFO
LOG_RETENTION_DAYS=3

# 磁盘监控配置
DISK_THRESHOLD=80
DISK_CHECK_INTERVAL=3600

# Telegram 通知配置
TG_BOT_TOKEN=**********************************************
TG_CHAT_ID=652634878

# 心跳配置
HEARTBEAT_ENABLED=true
HEARTBEAT_INTERVAL=10
HEARTBEAT_WORKER_TTL=10
HEARTBEAT_WORKER_KEY=worker_dev
HEARTBEAT_ACTIVE_WORKERS_KEY=active_workers_dev
