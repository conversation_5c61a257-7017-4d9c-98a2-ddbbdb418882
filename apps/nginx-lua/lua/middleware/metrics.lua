local logger = require "utils.logger"
local config = require "core.config"

local _M = {}
local log = logger.new("metrics")


-- 初始化指标计数器
function _M.init()
    local dict = ngx.shared[config.metrics.dict_name]
    if not dict then
        log.error("Failed to get shared dict: %s", config.metrics.dict_name)
        return false
    end

    -- 初始化计数器（如果不存在）
    local success, err, forcible = dict:add(config.metrics.total_requests, 0)
    if not success and err ~= "exists" then
        log.error("Failed to initialize total_requests counter: %s", err)
    end

    success, err, forcible = dict:add(config.metrics.current_connections, 0)
    if not success and err ~= "exists" then
        log.error("Failed to initialize current_connections counter: %s", err)
    end

    return true
end

-- 记录请求
function _M.record_request()
    local dict = ngx.shared[config.metrics.dict_name]
    if not dict then
        return false
    end

    -- 增加总请求数
    local new_value, err = dict:incr(config.metrics.total_requests, 1, 0)
    if err then
        log.error("Failed to increment total_requests: %s", err)
        return false
    end

    -- 增加当前连接数
    local new_conn, err = dict:incr(config.metrics.current_connections, 1, 0)
    if err then
        log.error("Failed to increment current_connections: %s", err)
        return false
    end

    return true
end

-- 记录请求完成
function _M.record_request_complete()
    local dict = ngx.shared[config.metrics.dict_name]
    if not dict then
        return false
    end

    -- 减少当前连接数
    local value, err = dict:incr(config.metrics.current_connections, -1, 0)
    if err then
        log.error("Failed to decrement current_connections: %s", err)
        return false
    end

    -- 确保连接数不为负
    if value < 0 then
        dict:set(config.metrics.current_connections, 0)
    end

    return true
end

-- 处理器：记录请求开始
function _M.handle_request()
    return _M.record_request()
end

-- 处理器：记录请求结束
function _M.handle_response()
    return _M.record_request_complete()
end

-- 获取当前指标
function _M.get_metrics()
    local dict = ngx.shared[config.metrics.dict_name]
    if not dict then
        return { requests = 0, connections = 0 }
    end

    return {
        requests = dict:get(config.metrics.total_requests) or 0,
        connections = dict:get(config.metrics.current_connections) or 0
    }
end

return _M
