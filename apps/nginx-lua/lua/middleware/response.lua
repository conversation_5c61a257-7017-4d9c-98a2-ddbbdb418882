local logger = require "utils.logger"
local cookie_util = require "utils.cookie"
local redirect_util = require "utils.redirect"

local _M = {}
local log = logger.new("response")

-- 缓存常用的 ngx API 以提高性能
local ngx_header = ngx.header

local function set_security_headers(is_websocket)
    if is_websocket then
        -- WebSocket连接需要特定的头部
        ngx_header["Upgrade"] = "websocket"
        ngx_header["Connection"] = "upgrade"
    else
        -- 设置基本安全头部
        ngx_header["X-Content-Type-Options"] = "nosniff"
        ngx_header["X-Frame-Options"] = "SAMEORIGIN"
        ngx_header["X-XSS-Protection"] = "1; mode=block"
    end
end

local function handle_cors_headers(is_websocket)
    -- 设置通用CORS头部
    ngx_header["Access-Control-Allow-Origin"] = ngx.var.original_origin
    ngx_header["Access-Control-Allow-Headers"] = "*"
    ngx_header["Access-Control-Allow-Credentials"] = "true"

    -- WebSocket连接的特殊CORS头部
    if is_websocket then
        ngx_header["Access-Control-Allow-Headers"] =
        "upgrade,connection,sec-websocket-key,sec-websocket-protocol,sec-websocket-version"
        ngx_header["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    end
end


function _M.handle()
    local status = ngx.status
    local is_websocket = ngx.ctx.is_websocket

    -- 如果头部已经发送，不再处理
    if ngx.headers_sent then
        log.debug("Headers already sent, skipping response handling")
        return true
    end

    -- 获取响应头
    local headers = ngx.resp.get_headers()

    -- 设置安全相关头部
    set_security_headers(is_websocket and status == 101)
    -- 处理Cookie重写
    cookie_util.rewrite_response_cookies(headers, ngx.var.original_host)
    -- 处理CORS
    handle_cors_headers(is_websocket)

    -- 处理重定向
    local location = headers["Location"]
    if location and redirect_util.is_redirect_status(status) then
        local new_location = redirect_util.process_redirect_url(location, ngx.var.original_origin)
        if new_location then
            ngx_header["Location"] = new_location

            -- 将301永久重定向转换为302临时重定向，避免浏览器缓存重定向
            if status == 301 then
                ngx.status = 302
                log.info("Converting 301 redirect to 302 for proxy transparency")
            end
        end
    end

    -- 隐藏服务器信息
    ngx_header["Server"] = "cloudflare"
    ngx_header["X-Powered-By"] = nil
    ngx_header["X-Power-By"] = nil

    -- 修改响应体时，清除响应头
    if ngx.var.is_posw_js == "1" then
        ngx.header["Content-Length"] = nil
        ngx.header["Last-Modified"] = nil
        ngx.header["ETag"] = nil
    end

    -- 添加宽松的 CSP 策略
    ngx.header["Content-Security-Policy"] =
    "upgrade-insecure-requests; frame-ancestors 'self'; default-src * data: blob: filesystem: about: ws: wss: gap: 'unsafe-inline' 'unsafe-eval'"

    return true
end

return _M
