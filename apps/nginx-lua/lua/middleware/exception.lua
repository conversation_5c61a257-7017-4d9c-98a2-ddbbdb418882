local cjson = require "cjson.safe"
local logger = require "utils.logger"

local _M = {
    _VERSION = "0.1.0"
}

local log = logger.new("exception")

-- 常量定义
_M.ERROR_TYPES = {
    BAD_REQUEST = "BAD_REQUEST",                 -- 400
    UNAUTHORIZED = "UNAUTHORIZED",               -- 401
    FORBIDDEN = "FORBIDDEN",                     -- 403
    NOT_FOUND = "NOT_FOUND",                     -- 404
    INTERNAL_ERROR = "INTERNAL_ERROR",           -- 500
    BAD_GATEWAY = "BAD_GATEWAY",                 -- 502
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE", -- 503
    GATEWAY_TIMEOUT = "GATEWAY_TIMEOUT",         -- 504
}

-- 状态码映射表
local STATUS_CODE = {
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    INTERNAL_ERROR = 500,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503,
    GATEWAY_TIMEOUT = 504
}

-- 常用消息
local ERROR_MSG = {
    [400] = "Your request needs a little adjustment",
    [401] = "Please start a new session",
    [403] = "You don't have permission to access this page",
    [404] = "The page you're looking for isn't here",
    [500] = "We're experiencing technical difficulties",
    [502] = "The website you're trying to visit is currently unreachable",
    [503] = "The website you're trying to visit is temporarily unavailable",
    [504] = "The connection to the website timed out"
}

-- 响应模板 - 使用局部变量提高性能
local HTML_TPL =
[[<!DOCTYPE html><html><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>%s</title><style>:root{--bg-light:#f8fafc;--bg-dark:#0f172a;--text-primary-light:#334155;--text-secondary-light:#64748b;--text-primary-dark:#e2e8f0;--text-secondary-dark:#94a3b8;--border-light:#e2e8f0;--border-dark:#1e293b;--accent-light:#3b82f6;--accent-dark:#60a5fa}body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;background:var(--bg-light);color:var(--text-primary-light);margin:0;padding:0;min-height:100vh;display:flex;align-items:flex-start;padding-top:10vh;justify-content:center}.container{width:90%%;max-width:480px;padding:2.5rem;background:#fff;border:1px solid var(--border-light);border-radius:12px}.status-bar{display:flex;align-items:center;gap:.75rem;margin-bottom:1.5rem;padding-bottom:1.5rem;border-bottom:1px solid var(--border-light)}.emoji{font-size:2rem;line-height:1}.status-text{font-size:1.25rem;font-weight:600;color:var(--text-primary-light);margin:0}.message{color:var(--text-secondary-light);line-height:1.6;margin:0 0 1.5rem}.request-id{display:inline-flex;align-items:center;gap:.5rem;padding:.5rem .75rem;background:var(--bg-light);border-radius:6px;font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,monospace;font-size:.875rem;color:var(--text-secondary-light)}.request-id::before{content:'🔍';font-size:1rem}@media (max-width:480px){.container{padding:1.5rem}.status-text{font-size:1.125rem}}@media (prefers-color-scheme:dark){body{background:var(--bg-dark)}.container{background:#1e293b;border-color:var(--border-dark)}.status-bar{border-color:var(--border-dark)}.status-text{color:var(--text-primary-dark)}.message{color:var(--text-secondary-dark)}.request-id{background:var(--bg-dark);color:var(--text-secondary-dark)}}</style></head><body><div class="container"><div class="status-bar"><span class="emoji">%s</span><h1 class="status-text">%s</h1></div><p class="message">%s</p><div class="request-id">ID: %s</div></div></body></html>]]

-- 导出常量供外部使用
_M.STATUS_MAPPING = {}
for type, code in pairs(STATUS_CODE) do
    _M.STATUS_MAPPING[_M.ERROR_TYPES[type]] = code
end
_M.ERROR_MESSAGES = ERROR_MSG
_M.HTML_TEMPLATE = HTML_TPL

-- 创建异常对象
function _M.new(error_type, message, data)
    error_type = error_type or _M.ERROR_TYPES.INTERNAL_ERROR
    local status = _M.STATUS_MAPPING[error_type] or 500
    local default_msg = ERROR_MSG[status] or "The service is temporarily unavailable"

    return {
        type = error_type,
        message = message and (default_msg .. " - " .. message) or default_msg,
        status = status,
        data = data
    }
end

-- 记录错误日志
function _M.log_error_details(err)
    if not err then return end

    log.error("type=%s, status=%d, message=%s",
        err.type or "UNKNOWN",
        err.status or 500,
        err.message or "Unknown error")

    -- 记录更多上下文信息
    local phase = ngx.get_phase()
    local method = ngx.req.get_method()
    local accepts_html = _M.client_accepts_html() and "yes" or "no"
    local request_url = ngx.var.scheme .. "://" .. ngx.var.host .. ngx.var.request_uri

    -- 为连接错误添加更多诊断信息
    if err.status == 502 or err.status == 503 or err.status == 504 then
        log.error("Upstream connection error: status=%d, request_url=%s",
            err.status, request_url)
    end

    log.error("[%s] %s %s, accepts_html=%s",
        phase, method, request_url, accepts_html)

    -- 记录请求头部信息
    local headers = ngx.req.get_headers()
    if headers then
        local header_log = {}
        for k, v in pairs(headers) do
            if k:lower() ~= "cookie" then -- 避免记录敏感信息
                table.insert(header_log, k .. "=" .. tostring(v))
            end
        end
        if #header_log > 0 then
            log.debug("Request headers: %s", table.concat(header_log, ", "))
        end
    end

    if err.data then
        log.error("Error data: %s", cjson.encode(err.data))
    end
end

-- 异常响应处理
function _M.handle_response(exception)
    if not exception then return false end

    -- 记录异常详情
    _M.log_error_details(exception)

    local status = exception.status or 500
    local message = exception.message or ERROR_MSG[status] or "The service is temporarily unavailable"
    local request_id = ngx.ctx.po_request_id or "-"

    -- 设置响应状态和头信息
    ngx.status = status
    ngx.header["X-Request-ID"] = request_id
    ngx.header["Server"] = "cloudflare"
    ngx.header["X-Powered-By"] = nil
    ngx.header["X-Power-By"] = nil

    -- 检查Accept头，判断客户端是否接受HTML内容
    if not _M.client_accepts_html() then
        ngx.header.content_type = "text/plain; charset=utf-8"
        ngx.say("400 - bad request")
        ngx.exit(status)
        return true
    end

    -- 客户端接受HTML，设置内容类型并返回自定义页面
    ngx.header.content_type = "text/html; charset=utf-8"

    -- 处理消息格式
    local detail_message = message
    local default_msg = ERROR_MSG[status]
    if message and default_msg and string.sub(message, 1, #default_msg) == default_msg then
        local rest = string.sub(message, #default_msg + 1)
        if string.sub(rest, 1, 3) == " - " then
            rest = string.sub(rest, 4)
        end
        detail_message = default_msg .. (rest ~= "" and " - " .. rest or "")
    end

    -- 根据错误类型选择合适的表情符号
    local emoji = "⚠️"
    local title = "Request Failed"

    -- 上游连接错误使用特殊的表情符号和标题
    if status == 502 then
        emoji = "🔌"
        title = "Connection Failed"
    elseif status == 503 then
        emoji = "🚧"
        title = "Service Unavailable"
    elseif status == 504 then
        emoji = "⌛"
        title = "Connection Timeout"
    end

    -- 输出HTML响应
    ngx.say(string.format(HTML_TPL,
        title,          -- 标题
        emoji,          -- 表情符号
        title,          -- 状态文本
        detail_message, -- 详细消息
        request_id      -- 请求ID
    ))

    ngx.exit(status)
    return true
end

-- 检查客户端是否接受HTML内容
function _M.client_accepts_html()
    -- 1. 基于文件扩展名快速判断 - 高效直接
    local uri = ngx.var.uri
    local ext = string.match(uri or "", "%.(%w+)$")
    if ext then
        ext = string.lower(ext)
        local static_types = {
            -- 脚本和样式
            js = true,
            css = true,
            -- 图片
            jpg = true,
            jpeg = true,
            png = true,
            gif = true,
            ico = true,
            svg = true,
            webp = true,
            -- 字体
            ttf = true,
            woff = true,
            woff2 = true,
            eot = true,
            -- 媒体
            mp3 = true,
            mp4 = true,
            -- 数据
            json = true,
            xml = true,
            pdf = true
        }
        if static_types[ext] then
            return false
        end
    end

    -- 2. 基于路径前缀判断 - 处理无扩展名的静态资源
    local static_prefixes = { "/static/", "/assets/", "/images/", "/js/", "/css/", "/fonts/", "/media/" }
    for _, prefix in ipairs(static_prefixes) do
        if string.sub(uri, 1, #prefix) == prefix then
            return false
        end
    end

    -- 3. 基于请求头判断 - 处理API和XHR请求
    local headers = ngx.req.get_headers()

    -- XHR请求总是返回false
    local xhr = headers["x-requested-with"]
    if xhr and type(xhr) == "table" then xhr = xhr[1] end
    if xhr == "XMLHttpRequest" then
        return false
    end

    -- 获取并标准化Accept头
    local accept = headers["accept"]
    if accept and type(accept) == "table" then accept = accept[1] end
    accept = string.lower(accept or "")

    -- 如果明确要求HTML，返回true
    if string.find(accept, "text/html", 1, true) then
        return true
    end

    -- 如果明确要求非HTML静态资源，返回false
    if accept ~= "" and accept ~= "*/*" and (
            string.find(accept, "application/json", 1, true) or
            string.find(accept, "image/", 1, true) or
            string.find(accept, "application/javascript", 1, true) or
            string.find(accept, "text/css", 1, true)) then
        return false
    end


    -- 对于浏览器请求，当Accept为空或*/*时返回true
    if (accept == "" or accept == "*/*") then
        return true
    end

    -- 默认对于空Accept或*/*，考虑为API请求，返回false
    return false
end

-- 抛出异常
function _M.throw(error_type, message, data)
    local err = _M.new(error_type, message, data)
    ngx.ctx.exception = err
    _M.handle_response(err)
    -- ngx.exit(err.status or ngx.HTTP_INTERNAL_SERVER_ERROR)
end

-- 检查异常
function _M.check()
    return ngx.ctx.exception and _M.handle_response(ngx.ctx.exception) or true
end

-- 包装函数执行
function _M.wrap(func)
    if not func or type(func) ~= "function" then
        return function() return true end
    end

    return function(...)
        local ok, result = pcall(func, ...)
        if ok then return result end

        -- 处理错误
        log.error("Uncaught error: %s", result)
        local ex = _M.new(
            _M.ERROR_TYPES.INTERNAL_ERROR,
            "Internal server error",
            { original_error = result }
        )
        ngx.ctx.exception = ex
        return _M.handle_response(ex)
    end
end

-- 便捷异常函数 - 使用闭包优化代码结构
local function create_thrower(error_type)
    return function(message, data)
        _M.throw(error_type, message, data)
    end
end

-- 上游错误处理函数
function _M.handle_upstream_error()
    -- 如果没有异常信息，创建一个默认异常
    if not ngx.ctx.exception then
        -- 获取当前状态码
        local status = tonumber(ngx.var.status) or 500

        -- 上游连接错误映射,（DNS失败、TLS握手等错误时，nginx返回的状态为0）
        local error_type
        if status == 502 or status == 0 then
            error_type = _M.ERROR_TYPES.BAD_GATEWAY
        elseif status == 503 then
            error_type = _M.ERROR_TYPES.SERVICE_UNAVAILABLE
        elseif status == 504 then
            error_type = _M.ERROR_TYPES.GATEWAY_TIMEOUT
        end

        log.error("Upstream error: status=%d, error_type=%s", status, error_type)
        -- 创建对应的异常
        ngx.ctx.exception = _M.new(error_type)
    end

    -- 处理异常响应
    _M.handle_response(ngx.ctx.exception)
end

-- 定义常用异常抛出函数
_M.throw_bad_request = create_thrower(_M.ERROR_TYPES.BAD_REQUEST)
_M.throw_unauthorized = create_thrower(_M.ERROR_TYPES.UNAUTHORIZED)
_M.throw_forbidden = create_thrower(_M.ERROR_TYPES.FORBIDDEN)
_M.throw_not_found = create_thrower(_M.ERROR_TYPES.NOT_FOUND)
_M.throw_internal_error = create_thrower(_M.ERROR_TYPES.INTERNAL_ERROR)
_M.throw_bad_gateway = create_thrower(_M.ERROR_TYPES.BAD_GATEWAY)
_M.throw_service_unavailable = create_thrower(_M.ERROR_TYPES.SERVICE_UNAVAILABLE)
_M.throw_gateway_timeout = create_thrower(_M.ERROR_TYPES.GATEWAY_TIMEOUT)

return _M
