local cookie = require "resty.cookie"
local logger = require "utils.logger"
local jwt_utils = require "utils.jwt"
local cjson = require "cjson.safe"
local config = require "core.config"
local _M = {}

-- 常量定义
local SESSION_KEY = config.base.session_key

-- 日志实例
local log = logger.new("session")


-- 从请求中获取session值，优先从URL参数获取，其次从Cookie获取
function _M.get_from_request()
    -- 检查URL参数
    local args = ngx.req.get_uri_args()
    local session = args[SESSION_KEY]
    if session then
        return session, "URL"
    end

    -- 检查Cookie
    local cookie_obj = cookie:new()
    if cookie_obj then
        session = cookie_obj:get(SESSION_KEY)
        if session then
            return session, "Cookie"
        end
    end

    log.info("No session found in request")
    return nil, nil
end

-- 验证 session 的有效性
function _M.verify(session)
    if not session or session == "" then
        log.error("Missing or empty session")
        return false, nil
    end

    -- 验证 JWT token
    local payload = jwt_utils.verify_token(session)
    log.info("Parsed session payload: %s", cjson.encode(payload))
    if not payload then
        log.error("Invalid or expired session")
        return false, nil
    end

    -- 验证 session 是否过期
    local current_time = ngx.time()
    log.info("Session current_time=%d, exp=%d", current_time, payload.exp or 0)
    if not payload.exp or current_time > payload.exp then
        log.error("Session expired: current_time=%d, exp=%d", current_time, payload.exp or 0)
        return false, nil
    end

    return true, payload
end

-- 获取并验证 session
function _M.handle()
    -- 获取session并初始化结果对象
    local session, source = _M.get_from_request()
    local result = { authed = false, source = source, value = session, payload = nil }

    -- 验证session并更新结果
    if session then
        local is_valid, payload = _M.verify(session)
        log.info("Session is_valid: %s, payload: %s", is_valid, cjson.encode(payload))
        if is_valid then
            result.authed = true
            result.payload = payload
        end
    end

    log.info("Session result: %s", cjson.encode(result))

    -- 将结果保存到ngx.ctx中以便其他模块访问
    ngx.ctx.session = result

    return result
end

return _M
