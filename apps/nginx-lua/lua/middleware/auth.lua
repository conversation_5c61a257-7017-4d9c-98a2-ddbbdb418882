local config = require "core.config"
local logger = require "utils.logger"
local exception = require "middleware.exception"
local constants = require "core.constants"
local cookie = require "resty.cookie"

local _M = {}

-- 缓存常用的 ngx API 和常量
local ngx_exit = ngx.exit
local ngx_say = ngx.say
local ngx_header = ngx.header
local ngx_phase = ngx.get_phase

-- 常量定义
local POT_KEY = config.base.pot_key
local SESSION_KEY = config.base.session_key
local log = logger.new("auth")
local CACHE_CONTROL_HEADER = "no-store, no-cache, must-revalidate"

-- 预定义常量
local VALID_PHASES = {
    access = true,
    content = true,
    rewrite = true,
    header_filter = true
}


-- 处理 loading 页面返回
local function serve_loading_page()
    log.info("Serving enhanced loading page")

    -- 设置响应头
    ngx_header.content_type = "text/html; charset=utf-8"
    ngx_header["X-PO-Page"] = "1"
    ngx_header["Cache-Control"] = CACHE_CONTROL_HEADER
    ngx_header["Pragma"] = "no-cache"

    -- 只在 access 阶段返回 loading 页面
    if ngx_phase() == "access" then
        ngx_say(constants.TEMPLATES.LOADING)
        return ngx_exit(ngx.HTTP_OK)
    end
    return false
end

-- 通用的设置cookie方法
local function set_cookie_general(key, value, options)
    if not key or not value then
        log.error("Cannot set cookie: key or value is nil")
        return false
    end

    -- 创建cookie对象
    local cookie_obj, err = cookie:new()
    if not cookie_obj then
        log.error("Failed to create cookie object: %s", err)
        return false
    end

    -- 默认配置
    local config = {
        key = key,
        value = value,
        path = "/",
        httponly = true,
        secure = ngx.var.scheme == "https",
        samesite = "None",
        max_age = 3600 -- 默认1小时
    }

    -- 合并用户传入的配置
    if options then
        for k, v in pairs(options) do
            config[k] = v
        end
    end

    -- 设置cookie
    local ok, err = cookie_obj:set(config)
    if not ok then
        log.error("Failed to set cookie %s: %s", key, err)
        return false
    end

    return true
end

-- 验证API处理函数
function _M.handle_auth_api()
    -- 检查session是否已验证
    if not ngx.ctx.session or not ngx.ctx.session.authed then
        log.error("Session verification failed")
        return ngx_exit(ngx.HTTP_BAD_REQUEST)
    end

    -- 设置响应头
    ngx_header["Cache-Control"] = CACHE_CONTROL_HEADER
    -- 设置当前时间戳
    ngx_header["X-PO-TS"] = tostring(ngx.time())
    -- 设置会话过期时间
    local exp_value = ngx.ctx.session and ngx.ctx.session.payload and ngx.ctx.session.payload.exp or 2
    ngx_header["X-PO-EX"] = tostring(exp_value)

    -- 设置认证cookie
    local success = set_cookie_general(SESSION_KEY, ngx.ctx.session.value, {
        httponly = true,
        max_age = 28800 -- 8小时
    })
    if not success then
        return ngx_exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
    end

    -- 成功时仅返回200状态码，不返回内容
    log.info("session verification successful, cookie set")
    return ngx_exit(ngx.HTTP_OK)
end

function _M.check()
    -- 检查请求阶段
    local phase = ngx_phase()
    if not VALID_PHASES[phase] then
        log.warn("Auth check called in invalid phase: %s", phase)
        return false
    end

    -- 如果是对session验证API的请求，跳过普通鉴权流程
    if ngx.var.uri == "/__poa" then
        return true
    end

    -- 如果session验证失败，抛出错误
    if not ngx.ctx.session or not ngx.ctx.session.authed then
        exception.throw_unauthorized("Session not found", {
            request_id = ngx.ctx.po_request_id or "-"
        })
        return false
    end

    -- 获取URL参数
    local args = ngx.req.get_uri_args()
    local pot_value = args[POT_KEY]

    -- 如果没有pot_value参数，抛出错误
    if not pot_value then
        local request_id = ngx.ctx.po_request_id or "-"
        exception.throw_bad_request("Please specify a destination URL", {
            param = POT_KEY,
            request_id = request_id
        })
        return false
    end

    -- 使用预先设置的 authed 状态
    if not ngx.ctx.session or not ngx.ctx.session.authed then
        -- 请求没有有效的 session
        return false
    end

    -- 如果 session 来自 URL 参数，返回 loading 页面
    if ngx.ctx.session.source == "URL" then
        return serve_loading_page()
    end

    -- session 验证通过
    return true
end

return _M
