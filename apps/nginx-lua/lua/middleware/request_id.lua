local logger = require "utils.logger"

local _M = {}
local log = logger.new("request_id")

-- 生成新的请求ID
function _M.generate_request_id()
    return string.format("%x", math.floor(ngx.now() * 1000))
end

function _M.handle()
    -- 优先使用已有的X-Request-ID头
    local headers = ngx.req.get_headers()
    local request_id = headers["X-Request-ID"]
    if type(request_id) == "table" then
        request_id = request_id[1] -- 使用第一个值
    end

    -- 最后生成新的
    if not request_id then
        request_id = _M.generate_request_id()
        log.debug("Generated new request_id: %s", request_id)
    end

    -- 设置到上下文和响应头中
    ngx.req.set_header("X-Request-ID", request_id)
    ngx.header["X-Request-ID"] = request_id

    -- 设置到上下文中, 因为ngx.var某些处理阶段被重置或无法访问
    ngx.ctx.po_request_id = request_id

    log.info("Request ID set: %s", request_id)
    return request_id
end

return _M
