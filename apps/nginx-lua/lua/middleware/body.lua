local logger = require "utils.logger"
local _M = {}
local log = logger.new("body")

-- 检查是否是需要处理的 Service Worker 请求
function _M.is_service_worker()
    local status = ngx.status
    -- 使用Nginx变量检查是否是带__posw=1参数的JS文件，同时仍检查状态码
    return ngx.var.is_posw_js == "1" and status == 200
end

function _M.handle()
    -- 检查是否需要包装
    if _M.is_service_worker() then
        log.info("Detected Service Worker script with __posw=1, applying wrapper")
        _M.apply_sw_wrapper()
        return true
    end

    -- 检查其他内容处理条件
    -- ... 未来可以在这里添加其他内容处理逻辑

    return false
end

-- 应用 Service Worker 包装逻辑
function _M.apply_sw_wrapper()
    -- 获取当前块和块状态
    local chunk = ngx.arg[1]
    local is_last_chunk = ngx.arg[2]

    -- 初始化缓存
    if not ngx.ctx.sw_chunks then
        ngx.ctx.sw_chunks = {}
    end

    -- 缓存当前块
    if chunk and chunk ~= "" then
        table.insert(ngx.ctx.sw_chunks, chunk)
    end

    -- 如果是最后一块，处理并发送完整内容
    if is_last_chunk then
        -- 合并所有块
        local content = table.concat(ngx.ctx.sw_chunks, "")

        -- 构建代理服务器 URL
        local original_origin = ngx.var.original_origin

        -- 创建包装后的内容
        local wrapped_content = string.format([[
importScripts('%s/__po.sw.js');

try {
  %s
} catch (e) {
    console.warn('PO Worker Error: ' + e.message);
}]], original_origin, content)

        -- 设置响应
        ngx.arg[1] = wrapped_content
        ngx.arg[2] = true

        -- 清除缓存
        ngx.ctx.sw_chunks = nil
    else
        -- 不是最后一块，先不发送任何内容
        ngx.arg[1] = ""
        ngx.arg[2] = false
    end
end

return _M
