local _M = {}
local log = require("utils.logger").new("cookie")

-- 重写请求 cookie
function _M.rewrite_request_cookie(headers)
    local cookie_header = headers["cookie"]
    log.info("Rewritten request start: %s", cookie_header)
    if not cookie_header then return end

    local current_host = ngx.var.original_host
    if not current_host then
        log.warn("Missing original_host in ngx.ctx, cannot filter cookies by domain")
        return
    end

    local cookies = {}
    for cookie_str in string.gmatch(cookie_header .. ";", "([^;]*);") do
        local name, value = string.match(cookie_str, "%s*(.-)%s*=%s*(.-)%s*$")
        if name and value and not string.match(name, "^__po") then
            -- 检查 cookie 是否属于当前域名
            local domain, clean_name = string.match(name, "^([^#]+)#(.+)$")

            if domain and domain == current_host then
                -- 提取实际值（新格式: 时间戳#值）
                local real_value = value
                if string.match(value, "^%d+#") then
                    local pos = string.find(value, "#")
                    if pos then
                        real_value = string.sub(value, pos + 1)
                    end
                end

                cookies[#cookies + 1] = string.format("%s=%s", clean_name, real_value)
            end
        end
    end

    log.info("Rewritten request cookies: %s", #cookies > 0 and table.concat(cookies, "; ") or "none")
    ngx.req.set_header("Cookie", #cookies > 0 and table.concat(cookies, "; ") or nil)
end

-- 重写响应 cookie
function _M.rewrite_response_cookies(headers, target_host)
    local set_cookie = headers["set-cookie"]
    if not set_cookie then return end

    if type(set_cookie) == "string" then
        set_cookie = { set_cookie }
    end

    -- 获取当前时间戳（使用会话过期时间作为参考）
    local exp_value = ngx.ctx.session and ngx.ctx.session.payload and ngx.ctx.session.payload.exp or 3

    local new_cookies = {}
    for _, cookie_str in ipairs(set_cookie) do
        -- 1. 提取 cookie 名称和值部分
        local name_value_part, attrs_part = string.match(cookie_str, "^([^;]+)(.*)")
        if name_value_part then
            local name, value = string.match(name_value_part, "^%s*([^=]+)%s*=%s*(.*)")
            if name then

                -- 3. 构建新名称和值（按照指定格式：域名#名称=时间戳#值）
                local new_name = string.format("%s#%s", target_host, name)
                local new_value = string.format("%d#%s", exp_value, value)

                -- 4. 处理属性部分
                -- 4.1 移除原始 domain 属性，让浏览器使用默认域（当前代理服务器域名）
                attrs_part = string.gsub(attrs_part or "", ";%s*[dD][oO][mM][aA][iI][nN]%s*=[^;]*", "")

                -- 4.2 确保有 Path 属性
                if not string.match(attrs_part, ";%s*[pP][aA][tT][hH]%s*=") then
                    attrs_part = attrs_part .. "; Path=/"
                end

                -- 5. 处理 SameSite
                if string.match(attrs_part, ";%s*[sS][aA][mM][eE][sS][iI][tT][eE]%s*=%s*[sS][tT][rR][iI][cC][tT]") then
                    attrs_part = string.gsub(attrs_part,
                        ";%s*[sS][aA][mM][eE][sS][iI][tT][eE]%s*=%s*[sS][tT][rR][iI][cC][tT]", "; SameSite=None")
                    -- 当 SameSite=None 时，需要设置 Secure
                    if not string.match(attrs_part, ";%s*[sS][eE][cC][uU][rR][eE]") then
                        attrs_part = attrs_part .. "; Secure"
                    end
                end

                -- 6. 组合新 cookie
                local new_cookie = string.format("%s=%s%s", new_name, new_value, attrs_part)
                new_cookies[#new_cookies + 1] = new_cookie
                log.info("Rewritten old cookie: %s, new cookie: %s", cookie_str, new_cookie)
            end
        end
    end
    ngx.header["Set-Cookie"] = new_cookies
end

return _M
