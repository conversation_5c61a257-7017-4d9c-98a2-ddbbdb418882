local logger = require "utils.logger"
local url_util = require "utils.url"
local config = require "core.config"
local base64 = require "utils.base64"
local exception = require "middleware.exception"

local _M = {}
local log = logger.new("proxy_utils")

-- 常量定义
local pot_key = config.base.pot_key
local WS_PROTOCOL_MAP = {
    ws = "http",
    wss = "https"
}

-- 处理 WebSocket 请求
function _M.setup_websocket(ctx, headers, target)
    -- 仅在必要时进行 scheme 转换 (ws->http, wss->https)
    local http_scheme = WS_PROTOCOL_MAP[target.scheme]
    if http_scheme then
        target.scheme = http_scheme
    end

    -- 设置必要的 WebSocket 头部
    headers["Upgrade"] = "websocket"
    headers["Connection"] = "upgrade"

    -- 标记上下文为 WebSocket
    ctx.ws_upgrade = true
    ctx.ws_protocol = target.scheme == "http" and "ws" or "wss"
end

-- 从请求中获取并解析目标URL
function _M.parse_target_info()
    local request_id = ngx.ctx.po_request_id or "-"

    -- 获取并解码目标URL
    local args = ngx.req.get_uri_args()
    local pot = args[pot_key]
    if not pot then
        log.error("Missing target URL parameter (__pot), request_id: %s", request_id)
        return nil, "Please provide a valid destination URL"
    end

    local target_url, err = base64.decode_pot(pot)
    if not target_url then
        log.error("Failed to decode target URL: %s, request_id: %s", err, request_id)
        return nil, "The provided URL appears to be invalid"
    end

    -- 解析目标URL
    local target, err = url_util.parse(target_url)
    if not target then
        log.error("Failed to parse target URL: %s, request_id: %s, error: %s", target_url, request_id, err)
        exception.throw_bad_request(err or "Unable to process the provided URL")
        return nil, "Unable to process the provided URL"
    end

    return {
        pot = pot,
        target_url = target_url,
        target = target,
        args = args
    }
end

return _M
