--[[
  Base64 工具函数
  使用 lua-resty-core 的 ngx.base64 模块实现
  提供标准 Base64 和 URL 安全的 Base64 编解码功能
]]

local base64 = require "ngx.base64"

local _M = {}

-- Base64 解码函数 (URL 安全版本)
-- @param str 要解码的字符串
-- @return 解码后的字符串
function _M.decode_pot(str)
    if not str then
        return nil, "The URL parameter is missing"
    end

    local processedStr = str

    -- 处理padding：解码前自动添加必要的等号
    local paddingLength = 4 - (string.len(str) % 4)
    if paddingLength < 4 then
        processedStr = str .. string.rep("=", paddingLength)
    end

    -- 将URL安全的Base64字符替换为标准Base64字符
    processedStr = processedStr:gsub("-", "+"):gsub("_", "/")

    -- 使用 ngx.base64 提供的 Base64 解码函数
    local ok, result = pcall(base64.decode_base64url, processedStr)
    if not ok then
        return nil, "The URL format is incorrect"
    end

    return result
end

-- Base64 编码函数 (URL 安全版本)
-- @param str 要编码的字符串
-- @return 编码后的字符串（已移除padding）
function _M.encode_pot(str)
    if not str then
        return nil, "Cannot encode empty URL"
    end

    -- 使用 ngx.base64 提供的 Base64 编码函数
    local ok, encoded = pcall(base64.encode_base64url, str)
    if not ok then
        return nil, "Failed to encode URL"
    end

    -- 将标准Base64字符替换为URL安全的字符
    encoded = encoded:gsub("+", "-"):gsub("/", "_")

    -- 移除所有padding字符（等号）
    return encoded:gsub("=+$", "")
end

return _M
