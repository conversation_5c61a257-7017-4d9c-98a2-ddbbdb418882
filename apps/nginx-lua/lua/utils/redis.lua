local redis_c = require "resty.redis"
local cjson = require "cjson.safe"
local config = require "core.config"

local _M = {
    _VERSION = '0.1.0'
}

-- 默认连接超时时间（毫秒）
local DEFAULT_TIMEOUT = 1000
-- 默认连接池大小
local DEFAULT_POOL_SIZE = 10
-- 默认连接空闲超时时间（毫秒）
local DEFAULT_IDLE_TIMEOUT = 60000

-- 生成统一的连接池名称
-- 格式: redis:{host}:{port}:{db_index}
function _M.get_pool_name(host, port, db_index)
    host = host or "127.0.0.1"
    port = port or 6379
    db_index = db_index or 0

    return string.format("redis:%s:%d:%d", host, port, db_index)
end

-- 检查当前是否在允许使用cosocket的阶段
local function check_phase()
    local phase = ngx.get_phase()
    local cosocket_phases = {
        rewrite = true,
        access = true,
        content = true,
        timer = true,
        ssl_cert = true,
        ssl_session_fetch = true,
        ssl_session_store = true,
        balancer = true,
        set = true,
        header_filter = true,
        body_filter = true,
        log = true
    }

    if not cosocket_phases[phase] then
        return false, "cannot use cosocket in " .. phase .. " phase"
    end

    return true
end

-- 创建Redis连接
function _M.connect()
    -- 检查当前阶段
    local ok, err = check_phase()
    if not ok then
        ngx.log(ngx.ERR, "Failed to create Redis connection: ", err)
        return nil, err
    end

    local redis = redis_c:new()

    -- 设置超时时间
    redis:set_timeout(config.redis and config.redis.timeout or DEFAULT_TIMEOUT)

    -- 获取Redis配置
    local redis_config = config.redis or {}
    local host = redis_config.host or "127.0.0.1"
    local port = redis_config.port or 6379
    local username = redis_config.username
    local password = redis_config.password
    local db_index = redis_config.db_index or 0

    -- 使用统一的连接池命名方法
    local pool_name = redis_config.pool_name or _M.get_pool_name(host, port, db_index)

    -- 连接参数
    local connect_params = {
        pool_size = redis_config.pool_size or DEFAULT_POOL_SIZE,
        pool = pool_name
    }

    ngx.log(ngx.INFO, "Connecting to Redis at ", host, ":", port, " using pool: ", pool_name)

    -- 连接Redis
    local ok, err = redis:connect(host, port, connect_params)
    if not ok then
        ngx.log(ngx.ERR, "Failed to connect to Redis: ", err)
        return nil, err
    end

    -- 获取连接复用次数
    local times, err = redis:get_reused_times()
    if not times then
        ngx.log(ngx.WARN, "Failed to get connection reused times: ", err)
        times = 0
    end

    -- 更新连接池统计信息
    _M.update_pool_stats(times > 0, pool_name)

    if times > 0 then
        ngx.log(ngx.INFO, "Reusing Redis connection from pool, reused times: ", times)
    else
        ngx.log(ngx.INFO, "Successfully connected to Redis server (new connection)")
    end

    -- 如果设置了用户名和密码，使用AUTH命令进行认证
    if username and username ~= "" and password and password ~= "" then
        -- Redis 6.0+ 支持用户名和密码认证
        -- 只有新连接才需要认证
        if times == 0 then
            ngx.log(ngx.INFO, "Authenticating with username: ", username)
            local ok, err = redis:auth(username, password)
            if not ok then
                ngx.log(ngx.ERR, "Failed to authenticate with Redis using username and password: ", err)
                return nil, err
            end
            ngx.log(ngx.INFO, "Redis authenticated with username: ", username)
        end
        -- 如果只设置了密码，使用传统的密码认证
    elseif password and password ~= "" then
        -- 只有新连接才需要认证
        if times == 0 then
            ngx.log(ngx.INFO, "Authenticating with password only")
            local ok, err = redis:auth(password)
            if not ok then
                ngx.log(ngx.ERR, "Failed to authenticate with Redis using password: ", err)
                return nil, err
            end
            ngx.log(ngx.INFO, "Redis authenticated with password only")
        end
    end

    -- 选择数据库
    if db_index > 0 then
        -- 注意：在使用连接池时，应该避免使用select命令
        -- 或者确保在归还连接前重置为默认数据库
        local ok, err = redis:select(db_index)
        if not ok then
            ngx.log(ngx.ERR, "Failed to select Redis DB: ", err)
            return nil, err
        end
        ngx.log(ngx.DEBUG, "Selected Redis DB: ", db_index)
    end

    return redis
end

-- 关闭Redis连接（放回连接池）
function _M.close(redis)
    if not redis then
        return
    end

    -- 检查当前阶段
    local ok, err = check_phase()
    if not ok then
        redis:close()
        return
    end

    -- 获取连接池配置
    local pool_size = config.redis and config.redis.pool_size or DEFAULT_POOL_SIZE
    local idle_timeout = config.redis and config.redis.idle_timeout or DEFAULT_IDLE_TIMEOUT

    -- 获取连接复用次数，用于日志记录
    local times, err = redis:get_reused_times()
    if not times then
        ngx.log(ngx.WARN, "Failed to get connection reused times: ", err)
        times = 0
    end

    -- 放回连接池
    local ok, err = redis:set_keepalive(idle_timeout, pool_size)
    if not ok then
        ngx.log(ngx.ERR, "Failed to put Redis connection back to pool: ", err)
        redis:close()
    else
        ngx.log(ngx.INFO, "Redis connection returned to pool, reused times: ", times)
    end
end

-- 执行Redis命令并自动管理连接
function _M.exec(func)
    local redis, err = _M.connect()
    if not redis then
        return nil, err
    end

    -- 执行回调函数
    local result, err = func(redis)

    -- 关闭连接
    _M.close(redis)

    return result, err
end

-- 设置键值对（支持过期时间）
function _M.set(key, value, exptime)
    return _M.exec(function(redis)
        -- 如果值是表，则转换为JSON
        if type(value) == "table" then
            value = cjson.encode(value)
        end

        local result, err
        if exptime then
            result, err = redis:setex(key, exptime, value)
        else
            result, err = redis:set(key, value)
        end

        return result, err
    end)
end

-- 获取键值
function _M.get(key)
    return _M.exec(function(redis)
        local value, err = redis:get(key)
        if not value or value == ngx.null then
            return nil, err
        end

        -- 尝试解析JSON
        local decoded = cjson.decode(value)
        if decoded then
            return decoded, nil
        end

        return value, nil
    end)
end

-- 设置键的过期时间
function _M.expire(key, seconds)
    return _M.exec(function(redis)
        return redis:expire(key, seconds)
    end)
end

-- 删除键
function _M.del(key)
    return _M.exec(function(redis)
        return redis:del(key)
    end)
end

-- 设置哈希表字段
function _M.hset(key, field, value)
    return _M.exec(function(redis)
        -- 如果值是表，则转换为JSON
        if type(value) == "table" then
            value = cjson.encode(value)
        end

        ngx.log(ngx.DEBUG, "Redis HSET: key=", key, ", field=", field, ", value=", value)
        local result, err = redis:hset(key, field, value)
        if not result then
            ngx.log(ngx.ERR, "Redis HSET失败: key=", key, ", field=", field, ", 错误=", err)
        else
            ngx.log(ngx.DEBUG, "Redis HSET成功: key=", key, ", field=", field)
        end
        return result, err
    end)
end

-- 获取哈希表字段
function _M.hget(key, field)
    return _M.exec(function(redis)
        local value, err = redis:hget(key, field)
        if not value or value == ngx.null then
            return nil, err
        end

        -- 尝试解析JSON
        local decoded = cjson.decode(value)
        if decoded then
            return decoded, nil
        end

        return value, nil
    end)
end

-- 获取哈希表所有字段
function _M.hgetall(key)
    return _M.exec(function(redis)
        local result, err = redis:hgetall(key)
        if not result or #result == 0 then
            return {}, err
        end

        -- 将数组转换为哈希表
        local hash = {}
        for i = 1, #result, 2 do
            local field = result[i]
            local value = result[i + 1]

            -- 尝试解析JSON
            local decoded = cjson.decode(value)
            if decoded then
                hash[field] = decoded
            else
                hash[field] = value
            end
        end

        return hash, nil
    end)
end

-- 添加到有序集合
function _M.zadd(key, score, member)
    return _M.exec(function(redis)
        -- 如果成员是表，则转换为JSON
        if type(member) == "table" then
            member = cjson.encode(member)
        end

        ngx.log(ngx.DEBUG, "Redis ZADD: key=", key, ", score=", score, ", member=", member)
        local result, err = redis:zadd(key, score, member)
        if not result then
            ngx.log(ngx.ERR, "Redis ZADD失败: key=", key, ", score=", score, ", 错误=", err)
        else
            ngx.log(ngx.DEBUG, "Redis ZADD成功: key=", key, ", score=", score)
        end
        return result, err
    end)
end

-- 获取有序集合成员分数
function _M.zscore(key, member)
    return _M.exec(function(redis)
        -- 如果成员是表，则转换为JSON
        if type(member) == "table" then
            member = cjson.encode(member)
        end

        local score, err = redis:zscore(key, member)
        if not score or score == ngx.null then
            return nil, err
        end

        return tonumber(score), nil
    end)
end

-- 获取有序集合范围内的成员
function _M.zrange(key, start, stop, with_scores)
    return _M.exec(function(redis)
        local result, err

        if with_scores then
            result, err = redis:zrange(key, start, stop, "WITHSCORES")
            if not result or #result == 0 then
                return {}, err
            end

            -- 将数组转换为带分数的表
            local members = {}
            for i = 1, #result, 2 do
                local member = result[i]
                local score = tonumber(result[i + 1])

                -- 尝试解析JSON
                local decoded = cjson.decode(member)
                if decoded then
                    members[i] = { member = decoded, score = score }
                else
                    members[i] = { member = member, score = score }
                end
            end

            return members, nil
        else
            result, err = redis:zrange(key, start, stop)
            if not result or #result == 0 then
                return {}, err
            end

            -- 尝试解析每个成员的JSON
            for i = 1, #result do
                local decoded = cjson.decode(result[i])
                if decoded then
                    result[i] = decoded
                end
            end

            return result, nil
        end
    end)
end

-- 移除有序集合中的过期成员
function _M.zremrangebyscore(key, min, max)
    return _M.exec(function(redis)
        return redis:zremrangebyscore(key, min, max)
    end)
end

-- 测试连接并返回Redis信息
function _M.info()
    return _M.exec(function(redis)
        return redis:info()
    end)
end

-- 测试连接是否正常
function _M.ping()
    return _M.exec(function(redis)
        return redis:ping()
    end)
end

-- 更新连接池统计信息
function _M.update_pool_stats(reused, pool_name)
    local redis_config = config.redis or {}

    -- 如果没有提供连接池名称，则使用配置中的名称或生成一个默认名称
    if not pool_name then
        local host = redis_config.host or "127.0.0.1"
        local port = redis_config.port or 6379
        local db_index = redis_config.db_index or 0
        pool_name = redis_config.pool_name or _M.get_pool_name(host, port, db_index)
    end

    local key = "redis_pool_stats:" .. pool_name

    local dict = ngx.shared.metrics
    if not dict then
        return false
    end

    -- 获取当前统计信息
    local stats, err = dict:get(key)
    local pool_stats

    if not stats then
        pool_stats = {
            name = pool_name,
            total_connections = 1,
            reused_connections = reused and 1 or 0,
            created_at = ngx.now()
        }
    else
        pool_stats = cjson.decode(stats)
        pool_stats.total_connections = pool_stats.total_connections + 1
        if reused then
            pool_stats.reused_connections = pool_stats.reused_connections + 1
        end
    end

    -- 更新统计信息
    local ok, err = dict:set(key, cjson.encode(pool_stats))
    if not ok then
        ngx.log(ngx.ERR, "Failed to update Redis pool stats: ", err)
        return false
    end

    return true
end

return _M
