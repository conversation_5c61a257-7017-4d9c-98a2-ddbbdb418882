local url_util = require "utils.url"
local logger = require "utils.logger"

local _M = {}
local log = logger.new("referer")

-- 处理 referer
function _M.rewrite_request_referer(headers)
    local custom_referer = headers["X-PO-RF"]
    if not custom_referer then return end

    local original_referer, unused_host, err = url_util.to_original_url(custom_referer)
    if not original_referer then
        log.warn("Failed to convert referer URL: %s", err)
        return
    end

    ngx.req.set_header("Referer", original_referer)
    ngx.req.set_header("X-PO-RF", nil)
    log.info("Set referer to: %s", original_referer)
end

return _M
