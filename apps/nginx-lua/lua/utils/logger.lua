local _M = {
    _VERSION = '0.1.0'
}

-- 日志级别定义
_M.LEVEL = {
    DEBUG = 1,
    INFO = 2,
    NOTICE = 3,
    WARN = 4,
    ERROR = 5,
    FATAL = 6
}

-- 日志格式定义
_M.FORMAT = {
    STANDARD = 1, -- 标准格式：时间 | 级别 | 请求ID | 模块 | 消息
    COMPACT = 2,  -- 紧凑格式：时间 级别 请求ID 模块: 消息
    JSON = 3      -- JSON格式：{"time":"...","level":"...","request_id":"...","module":"...","message":"..."}
}

-- 日志级别名称映射
local LEVEL_NAMES = {
    [_M.LEVEL.DEBUG]  = "DEBUG",
    [_M.LEVEL.INFO]   = "INFO",
    [_M.LEVEL.NOTICE] = "NOTICE",
    [_M.LEVEL.WARN]   = "WARN",
    [_M.LEVEL.ERROR]  = "ERROR",
    [_M.LEVEL.FATAL]  = "FATAL"
}

-- ANSI 颜色代码
local COLORS = {
    RESET = "\27[0m",
    BOLD = "\27[1m",
    DIM = "\27[2m",
    UNDERLINE = "\27[4m",
    BLINK = "\27[5m",
    REVERSE = "\27[7m",
    HIDDEN = "\27[8m",

    -- 前景色
    BLACK = "\27[30m",
    RED = "\27[31m",
    GREEN = "\27[32m",
    YELLOW = "\27[33m",
    BLUE = "\27[34m",
    MAGENTA = "\27[35m",
    CYAN = "\27[36m",
    WHITE = "\27[37m",

    -- 亮色前景
    BRIGHT_BLACK = "\27[90m",
    BRIGHT_RED = "\27[91m",
    BRIGHT_GREEN = "\27[92m",
    BRIGHT_YELLOW = "\27[93m",
    BRIGHT_BLUE = "\27[94m",
    BRIGHT_MAGENTA = "\27[95m",
    BRIGHT_CYAN = "\27[96m",
    BRIGHT_WHITE = "\27[97m",

    -- 背景色
    BG_BLACK = "\27[40m",
    BG_RED = "\27[41m",
    BG_GREEN = "\27[42m",
    BG_YELLOW = "\27[43m",
    BG_BLUE = "\27[44m",
    BG_MAGENTA = "\27[45m",
    BG_CYAN = "\27[46m",
    BG_WHITE = "\27[47m",

    -- 亮色背景
    BG_BRIGHT_BLACK = "\27[100m",
    BG_BRIGHT_RED = "\27[101m",
    BG_BRIGHT_GREEN = "\27[102m",
    BG_BRIGHT_YELLOW = "\27[103m",
    BG_BRIGHT_BLUE = "\27[104m",
    BG_BRIGHT_MAGENTA = "\27[105m",
    BG_BRIGHT_CYAN = "\27[106m",
    BG_BRIGHT_WHITE = "\27[107m"
}

-- 日志级别对应的颜色
local LEVEL_COLORS = {
    [_M.LEVEL.DEBUG] = COLORS.BRIGHT_BLACK,
    [_M.LEVEL.INFO] = COLORS.BRIGHT_GREEN,
    [_M.LEVEL.NOTICE] = COLORS.BRIGHT_CYAN,
    [_M.LEVEL.WARN] = COLORS.BRIGHT_YELLOW,
    [_M.LEVEL.ERROR] = COLORS.BRIGHT_RED,
    [_M.LEVEL.FATAL] = COLORS.BOLD .. COLORS.BG_RED .. COLORS.WHITE
}

-- 消息内容颜色（仅用于高亮错误和警告消息）
local MSG_COLORS = {
    [_M.LEVEL.DEBUG] = COLORS.RESET,
    [_M.LEVEL.INFO] = COLORS.RESET,
    [_M.LEVEL.NOTICE] = COLORS.RESET,
    [_M.LEVEL.WARN] = COLORS.YELLOW,
    [_M.LEVEL.ERROR] = COLORS.RED,
    [_M.LEVEL.FATAL] = COLORS.BOLD .. COLORS.RED
}

-- 日志级别字符串到数字的映射
local LEVEL_MAP = {
    debug = _M.LEVEL.DEBUG,
    info = _M.LEVEL.INFO,
    notice = _M.LEVEL.NOTICE,
    warn = _M.LEVEL.WARN,
    error = _M.LEVEL.ERROR,
    fatal = _M.LEVEL.FATAL,
    -- 添加 Nginx 特有的日志级别
    emerg = _M.LEVEL.FATAL,
    alert = _M.LEVEL.ERROR,
    crit = _M.LEVEL.ERROR
}

-- 获取当前日志级别
local function get_current_level()
    -- 优先从共享内存中获取
    local log_dict = ngx.shared.log_dict
    if log_dict then
        local level = log_dict:get("current_level")
        if level then
            local numeric_level = LEVEL_MAP[level]
            if numeric_level then
                return numeric_level
            end
        end
    end

    -- 回退到环境变量
    local env_level = os.getenv("LOG_LEVEL")
    if env_level then
        env_level = string.lower(env_level)
        local numeric_level = LEVEL_MAP[env_level]
        if numeric_level then
            return numeric_level
        end
    end

    -- 默认使用 INFO 级别
    return _M.LEVEL.INFO
end

-- 检查是否启用颜色
local function is_color_enabled()
    local color_enabled = os.getenv("LOG_COLOR")
    if color_enabled == nil then
        -- 默认启用颜色
        return true
    end

    color_enabled = string.lower(color_enabled)
    return color_enabled == "1" or color_enabled == "true" or color_enabled == "yes" or color_enabled == "on"
end

-- 为文本添加颜色
local function colorize(text, color)
    if not is_color_enabled() then
        return text
    end
    return color .. text .. COLORS.RESET
end

-- 日志目录
local LOG_DIR = os.getenv("LOG_DIR") or "/usr/local/openresty/nginx/logs"

-- 文件操作相关的 FFI 定义
local ffi = require "ffi"
local bit = require "bit"
local C = ffi.C

ffi.cdef [[
    int write(int fd, const char *buf, size_t count);
    int open(const char *pathname, int flags, int mode);
    int close(int fd);
    char *strerror(int errnum);
    int rename(const char *oldpath, const char *newpath);
    int dup(int oldfd);
]]

-- 文件打开模式
local O_WRONLY = 0x0001
local O_APPEND = 0x0400
local O_CREAT = 0x0040
local FILE_MODE = tonumber("644", 8)

-- 文件描述符缓存
local file_descriptors = {}

-- 获取日志目录
function _M.get_log_dir()
    return LOG_DIR
end

-- 获取日志文件路径
local function get_log_file_path()
    return string.format("%s/proxy.log", LOG_DIR)
end

-- 获取或创建文件描述符
local function get_file_descriptor(file_path)
    local key = "proxy.log"
    if file_descriptors[key] then
        return file_descriptors[key]
    end

    local fd = C.open(file_path, bit.bor(O_WRONLY, O_APPEND, O_CREAT), FILE_MODE)
    if fd < 0 then
        return nil, ffi.string(C.strerror(ffi.errno()))
    end

    -- 复制文件描述符到 stdout (fd 1)
    local stdout_fd = C.dup(1) -- 复制 stdout
    if stdout_fd < 0 then
        C.close(fd)
        return nil, ffi.string(C.strerror(ffi.errno()))
    end

    file_descriptors[key] = fd
    file_descriptors["stdout"] = stdout_fd
    return fd
end

-- 写入日志到文件和stdout
local function write_to_file(level, module_name, msg, colored_msg)
    -- 获取当前日志级别
    local current_level = get_current_level()

    -- 检查是否需要记录该级别的日志
    if level < current_level then
        return
    end

    local file_path = get_log_file_path()
    local fd, err = get_file_descriptor(file_path)
    if not fd then
        return
    end

    -- 写入到文件 (无颜色版本)
    local bytes = C.write(fd, msg .. "\n", #msg + 1)
    if bytes < 0 then
        -- 关闭并重新打开文件描述符
        C.close(fd)
        file_descriptors["proxy.log"] = nil
        return
    end

    -- 写入到 stdout (有颜色版本)
    local stdout_fd = file_descriptors["stdout"]
    if stdout_fd then
        C.write(stdout_fd, colored_msg .. "\n", #colored_msg + 1)
    end
end

-- 获取当前日志格式
local function get_current_format()
    -- 优先从共享内存中获取
    local log_dict = ngx.shared.log_dict
    if log_dict then
        local format = log_dict:get("log_format")
        if format then
            return tonumber(format) or _M.FORMAT.STANDARD -- 默认使用标准格式
        end
    end

    -- 回退到环境变量
    local env_format = os.getenv("LOG_FORMAT")
    if env_format then
        if env_format == "compact" then
            return _M.FORMAT.COMPACT
        elseif env_format == "json" then
            return _M.FORMAT.JSON
        elseif env_format == "standard" then
            return _M.FORMAT.STANDARD
        end
    end

    -- 默认使用紧凑格式
    return _M.FORMAT.COMPACT
end

-- 基础日志格式化
local function format_log_message(level, module_name, msg, ...)
    -- 简单安全的格式化处理
    local formatted_msg
    if select('#', ...) > 0 then
        -- 有参数时，使用pcall确保格式化不会失败
        local ok, result = pcall(string.format, msg, ...)
        formatted_msg = ok and result or tostring(msg)
    else
        -- 没有参数时直接转为字符串
        formatted_msg = tostring(msg)
    end

    local request_id = "-"

    -- 只在请求阶段尝试获取request_id
    local phase = ngx.get_phase and ngx.get_phase() or "unknown"
    if phase ~= "init" and phase ~= "init_worker" then
        request_id = ngx.ctx and ngx.ctx.po_request_id or "-"
    end

    -- 获取当前日志格式
    local format = get_current_format()

    -- 获取带毫秒的时间戳
    local timestamp = os.date("%Y-%m-%d %H:%M:%S") .. string.format(".%03d", ngx.now() % 1 * 1000)

    -- 创建无颜色版本
    local plain_msg
    local colored_msg

    -- 根据格式生成日志
    if format == _M.FORMAT.COMPACT then
        -- 紧凑格式
        plain_msg = string.format("%s %s %s [%s]: %s",
            timestamp,
            LEVEL_NAMES[level],
            request_id,
            module_name,
            formatted_msg
        )

        -- 彩色版本
        local level_color = LEVEL_COLORS[level] or COLORS.RESET
        local msg_color = MSG_COLORS[level] or COLORS.RESET

        -- 对于警告和错误级别，给消息内容也添加颜色
        local colored_content = formatted_msg
        if level >= _M.LEVEL.WARN then
            colored_content = colorize(formatted_msg, msg_color)
        end

        colored_msg = string.format("%s %s %s %s: %s",
            colorize(timestamp, COLORS.BRIGHT_BLUE),
            colorize(LEVEL_NAMES[level], level_color),
            colorize(request_id, COLORS.BRIGHT_CYAN),
            colorize("[" .. module_name .. "]", COLORS.BRIGHT_MAGENTA),
            colored_content
        )
    elseif format == _M.FORMAT.JSON then
        -- JSON格式
        local json_data = {
            time = timestamp,
            level = LEVEL_NAMES[level],
            request_id = request_id,
            module = module_name,
            message = formatted_msg
        }

        -- 简单的JSON序列化
        local json_str = '{'
        for k, v in pairs(json_data) do
            -- 确保值是字符串，并进行基本转义
            local v_str = tostring(v):gsub('"', '\\"')
            json_str = json_str .. string.format('"%s":"%s",', k, v_str)
        end
        json_str = json_str:sub(1, -2) .. '}' -- 移除最后的逗号并添加结束括号

        plain_msg = json_str
        colored_msg = json_str -- JSON格式不使用颜色
    else
        -- 标准格式
        plain_msg = string.format("%s %-5s | %s | %-8s | %s",
            timestamp,
            LEVEL_NAMES[level],
            request_id,
            module_name,
            formatted_msg
        )

        -- 彩色版本
        local level_color = LEVEL_COLORS[level] or COLORS.RESET
        local msg_color = MSG_COLORS[level] or COLORS.RESET

        -- 对于警告和错误级别，给消息内容也添加颜色
        local colored_content = formatted_msg
        if level >= _M.LEVEL.WARN then
            colored_content = colorize(formatted_msg, msg_color)
        end

        colored_msg = string.format("%s %s %s %s %s %s %s %s",
            colorize(timestamp, COLORS.BRIGHT_BLUE),
            colorize("|", COLORS.DIM),
            colorize(string.format("%-5s", LEVEL_NAMES[level]), level_color),
            colorize("|", COLORS.DIM),
            colorize(" " .. request_id .. " ", COLORS.BRIGHT_CYAN),
            colorize("|", COLORS.DIM),
            colorize(string.format("%-8s", module_name), COLORS.BRIGHT_MAGENTA),
            colorize("| ", COLORS.DIM) .. colored_content
        )
    end

    return plain_msg, colored_msg
end

-- 创建日志记录器
function _M.new(module_name)
    local logger = {}

    for level_name, level in pairs(_M.LEVEL) do
        logger[string.lower(level_name)] = function(msg, ...)
            local current_level = get_current_level()
            if level >= current_level then
                local log_msg, colored_log_msg = format_log_message(level, module_name, msg, ...)
                write_to_file(level, module_name, log_msg, colored_log_msg)

                -- FATAL 级别自动终止请求，但仅在请求阶段
                local phase = ngx.get_phase and ngx.get_phase() or "unknown"
                if level == _M.LEVEL.FATAL and phase ~= "init" and phase ~= "init_worker" then
                    ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
                end
            end
        end
    end

    return logger
end

-- 设置日志级别
function _M.set_level(level)
    if type(level) == "string" then
        level = string.lower(level)
        level = LEVEL_MAP[level] or _M.LEVEL.INFO
    end

    -- 更新共享内存中的日志级别
    local log_dict = ngx.shared.log_dict
    if log_dict then
        for name, num in pairs(LEVEL_MAP) do
            if num == level then
                log_dict:set("current_level", name)
                break
            end
        end
    end
end

-- 启用/禁用颜色
function _M.set_color(enabled)
    local log_dict = ngx.shared.log_dict
    if log_dict then
        log_dict:set("color_enabled", enabled and "1" or "0")
    end
end

-- 清理文件描述符
function _M.cleanup_files()
    for _, fd in pairs(file_descriptors) do
        C.close(fd)
    end
    file_descriptors = {}
end



-- 设置日志格式
function _M.set_format(format)
    if type(format) == "string" then
        if format == "standard" then
            format = _M.FORMAT.STANDARD
        elseif format == "compact" then
            format = _M.FORMAT.COMPACT
        elseif format == "json" then
            format = _M.FORMAT.JSON
        else
            format = _M.FORMAT.STANDARD
        end
    end

    -- 更新共享内存中的日志格式
    local log_dict = ngx.shared.log_dict
    if log_dict then
        log_dict:set("log_format", format)
    end
end

return _M
