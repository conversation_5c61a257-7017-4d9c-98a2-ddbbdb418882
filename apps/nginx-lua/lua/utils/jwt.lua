local jwt = require "resty.jwt"
local ngx = ngx
local base64 = require "ngx.base64"
local config = require "core.config"
local logger = require "utils.logger"

local _M = {}
local log = logger.new("jwt")

-- 计算简单的校验和
local function calculate_checksum(str)
    local sum = 0
    for i = 1, #str do
        sum = sum + string.byte(str, i)
    end
    -- 返回两位的校验和（00-99）
    return string.format("%02d", sum % 100)
end

-- 还原被混淆的 token
local function deobfuscate_token(obfuscated_token)
    if not obfuscated_token or obfuscated_token == "" then
        return nil
    end

    -- 开发环境下的调试输出
    if ngx.get_phase() == "init" then
        log.info("接收到的混淆token: %s", obfuscated_token)
    end

    -- 1. 提取校验和（最后两位）
    if #obfuscated_token < 2 then
        return nil
    end

    local checksum = string.sub(obfuscated_token, -2)
    local token_without_checksum = string.sub(obfuscated_token, 1, -3)

    -- 2. 验证校验和
    local calculated_checksum = calculate_checksum(token_without_checksum)
    if checksum ~= calculated_checksum then
        log.warn("校验和不匹配，可能是token被篡改")
    end

    -- 3. 反转字符串
    local reversed = string.reverse(token_without_checksum)

    -- 4. 添加可能缺失的 base64 填充
    local padding_length = (4 - (#reversed % 4)) % 4
    local base64_token = reversed .. string.rep("=", padding_length)

    -- 开发环境下的调试输出
    if ngx.get_phase() == "init" then
        log.info("校验和: %s, 计算的校验和: %s", checksum, calculated_checksum)
        log.info("移除校验和后: %s, 反转后: %s, 添加填充后: %s",
            token_without_checksum, reversed, base64_token)
    end

    -- 5. base64 解码得到原始 JWT
    local ok, original_token = pcall(base64.decode_base64url, base64_token)
    if not ok then
        log.error("Base64解码失败")
        return nil
    end

    -- 验证解码后的 token 是否看起来像 JWT (包含两个点)
    local parts = {}
    for part in string.gmatch(original_token, "[^.]+") do
        table.insert(parts, part)
    end

    if #parts ~= 3 then
        -- 如果解码后的字符串不像 JWT，尝试检查原始 token
        parts = {}
        for part in string.gmatch(obfuscated_token, "[^.]+") do
            table.insert(parts, part)
        end

        if #parts == 3 then
            log.info("Token appears to be an unmixed JWT, returning as is")
            return obfuscated_token
        end

        return nil
    end

    return original_token
end

-- 验证 JWT token
function _M.verify_token(token)
    if not token then
        log.error("Empty token provided")
        return nil
    end

    -- 先还原 token
    local original_token = deobfuscate_token(token)
    if not original_token then
        log.error("Failed to deobfuscate token")
        return nil
    end

    -- 开发环境下的调试输出
    if ngx.get_phase() == "init" then
        log.info("准备验证的JWT: %s", original_token)
    end

    local verified = jwt:verify(config.jwt.secret, original_token)
    if not verified.verified then
        log.error("JWT verification failed: %s", verified.reason)
        return nil
    end

    return verified.payload
end

return _M
