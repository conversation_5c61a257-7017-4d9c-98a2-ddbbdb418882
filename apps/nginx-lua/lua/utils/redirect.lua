local logger = require "utils.logger"
local url_util = require "utils.url"
local net_url = require "lib.net_url"

local _M = {}
local log = logger.new("redirect")
-- 处理重定向URL
function _M.process_redirect_url(location, original_origin)
    if not location then return nil end

    log.info("Processing redirect location: %s, original_origin: %s", location, original_origin)

    -- 处理相对路径URL
    if location:sub(1, 1) == "/" and original_origin then
        -- 直接构建完整URL
        location = original_origin .. location
        log.info("Relative path converted to: %s", location)
    end

    -- 解析并处理URL
    local parsed_url = net_url.parse(location)
    if not parsed_url then
        log.error("Invalid Location format")
        return location
    end

    -- 如果没有host，使用original_origin
    if not parsed_url.host then
        log.error("No host in location: %s", location)
        return location
    end

    -- 转换为代理URL
    local proxy_host = ngx.var.scheme .. "://" .. ngx.var.http_host
    local proxy_url = url_util.to_proxy_url(parsed_url:build(), proxy_host)

    if not proxy_url then
        log.error("Failed to convert proxy URL, returning original URL")
        return location
    end

    log.info("Handle Redirect: " .. location .. " -> " .. proxy_url)
    return proxy_url
end

-- 判断是否为重定向状态码
function _M.is_redirect_status(status)
    return status == 301 or status == 302 or status == 303 or status == 307 or status == 308
end

return _M
