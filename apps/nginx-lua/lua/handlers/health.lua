local cjson = require "cjson.safe"
local config = require "core.config"
local heartbeat = require "core.heartbeat"

local _M = {}

-- 时间格式化函数（与heartbeat.lua保持一致）
local function format_time(timestamp)
    return os.date("%Y-%m-%d %H:%M:%S", timestamp)
end

-- 获取外部IP地址
local function get_external_ip()
    local external_ip = os.getenv("EXTERNAL_IP")
    if external_ip and external_ip ~= "" then
        return external_ip
    end
    return ngx.var.server_addr or "127.0.0.1"
end

-- 获取系统负载（与heartbeat.lua保持一致）
local function get_system_load()
    local file = io.open("/proc/loadavg", "r")
    if not file then
        return 0.0
    end

    local content = file:read("*line")
    file:close()

    if content then
        local load = content:match("^([%d%.]+)")
        return tonumber(load) or 0.0
    end

    return 0.0
end

-- 获取内存使用率（与heartbeat.lua保持一致）
local function get_memory_usage()
    local file = io.open("/proc/meminfo", "r")
    if not file then
        return 0.0
    end

    local mem_total, mem_available = 0, 0

    for line in file:lines() do
        local key, value = line:match("^(%w+):%s+(%d+)")
        if key == "MemTotal" then
            mem_total = tonumber(value) or 0
        elseif key == "MemAvailable" then
            mem_available = tonumber(value) or 0
        end

        if mem_total > 0 and mem_available > 0 then
            break
        end
    end

    file:close()

    if mem_total > 0 then
        return (mem_total - mem_available) / mem_total
    end

    return 0.0
end

-- 健康检查处理函数
function _M.check()
    -- 创建简化的健康检查信息（与心跳格式保持一致）
    local worker_info = {
        region = os.getenv("REGION") or "unknown",
        load = get_system_load(),
        memory_usage = get_memory_usage(),
        started_at = format_time(ngx.config.nginx_configure_time or ngx.now()),
        timestamp = format_time(ngx.now()),
        id = get_external_ip(),
        version = "0.1.0"
    }

    -- 设置响应头
    ngx.header.content_type = "application/json"

    -- 返回JSON响应
    ngx.say(cjson.encode(worker_info))
    return ngx.exit(ngx.HTTP_OK)
end

return _M
