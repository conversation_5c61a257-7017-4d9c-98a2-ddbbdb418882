local logger = require "utils.logger"
local cookie_util = require "utils.cookie"
local referer_util = require "utils.referer"
local url_util = require "utils.url"
local exception = require "middleware.exception"

local _M = {}
local log = logger.new("http")


function _M.handle()
    -- 检查认证状态
    log.info("Session authed: %s", ngx.ctx.session and ngx.ctx.session.authed)
    if not (ngx.ctx.session and ngx.ctx.session.authed) then
        log.info("Auth check failed, skipping HTTP handling")
        return false
    end

    -- 如果头部已经发送，不再处理
    if ngx.headers_sent then
        log.debug("Headers already sent, skip HTTP handling")
        return false
    end

    -- 获取基本请求信息
    local headers = ngx.req.get_headers()
    local method = ngx.req.get_method()

    local proxy_url = ngx.var.scheme .. "://" .. ngx.var.host .. ngx.var.request_uri
    log.info("Proxy URL: %s", proxy_url)
    log.info("Request uri: %s", ngx.var.request_uri)

    -- 直接使用to_original_url获取原始URL作为代理目标
    local original_url, original_host, err = url_util.to_original_url(proxy_url)
    if not original_url then
        log.error("Failed to get original URL: %s", err)
        exception.throw_bad_request(err or "Failed to process the URL")
        return ngx.exit(400)
    end

    log.info("%s request to original URL: %s", method, original_url)

    -- 转发所有请求头（排除host）
    for name, value in pairs(headers) do
        if name ~= "host" then
            ngx.req.set_header(name, value)
        end
    end

    -- 设置代理目标
    ngx.var.original_url = original_url
    ngx.var.original_host = original_host
    ngx.var.original_origin = ngx.var.scheme .. "://" .. original_host

    cookie_util.rewrite_request_cookie(headers)
    referer_util.rewrite_request_referer(headers)

    -- 如果请求的是带__posw=1参数的JS文件，则清除Accept-Encoding头部
    if ngx.var.is_posw_js == "1" then
        ngx.req.set_header("Accept-Encoding", "")
    end

    return true
end

return _M
