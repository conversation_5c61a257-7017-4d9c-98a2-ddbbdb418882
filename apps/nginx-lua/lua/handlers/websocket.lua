local logger = require "utils.logger"
local config = require "core.config"
local proxy_utils = require "utils.proxy_utils"
local referer_util = require "utils.referer"
local cookie_util = require "utils.cookie"
local exception = require "middleware.exception"

local _M = {}
local log = logger.new("websocket")

-- 协议映射表
local PROTOCOL_MAP = {
    http = "ws",
    https = "wss",
    ws = "ws",
    wss = "wss"
}

function _M.handle()
    local request_id = ngx.ctx.po_request_id

    -- 检查认证状态
    if not ngx.ctx.session.authed then
        log.info("Auth check failed, skipping WebSocket handling, request_id: %s", request_id)
        return false
    end

    -- 获取基本请求信息
    local headers = ngx.req.get_headers()
    local original_uri = ngx.var.uri
    local final_uri = original_uri:gsub("^/__pows", "") or "/"
    if final_uri == "" then
        final_uri = "/"
    end

    -- 获取目标信息
    local result, err = proxy_utils.parse_target_info()
    if not result then
        log.error("Failed to parse target info: %s, request_id: %s", err, request_id)
        exception.throw_bad_request(err or "Failed to process WebSocket request")
        return ngx.exit(400)
    end

    -- 处理特殊headers
    referer_util.rewrite_request_referer(headers)
    cookie_util.rewrite_request_cookie(headers)

    -- 转换协议
    local target_scheme = PROTOCOL_MAP[result.target.scheme] or (result.target.scheme == "https" and "wss" or "ws")
    local proxy_scheme = target_scheme == "wss" and "https" or "http"

    -- 强制设置为WebSocket请求
    ngx.ctx.is_websocket = true
    proxy_utils.setup_websocket(ngx.ctx, headers, result.target)

    -- 构建代理 URL
    local proxy_url = string.format("%s://%s%s",
        proxy_scheme,
        result.target.host,
        final_uri
    )

    -- 处理查询参数
    local query_args = {}
    for k, v in pairs(result.args) do
        if k ~= config.base.pot_key then
            query_args[k] = v
        end
    end
    if next(query_args) then
        proxy_url = proxy_url .. "?" .. ngx.encode_args(query_args)
    end

    log.info("WebSocket proxy: %s -> %s (scheme: %s), request_id: %s",
        original_uri, proxy_url, target_scheme, request_id)

    -- 转发所有请求头
    for name, value in pairs(headers) do
        if name ~= "host" then
            ngx.req.set_header(name, value)
        end
    end

    -- 设置代理目标
    ngx.var.original_url = proxy_url
    ngx.var.original_host = result.target.host
    ngx.var.original_origin = ngx.var.scheme .. "://" .. result.target.host

    -- 设置上下文
    ngx.ctx.is_websocket = true
    return true
end

return _M
