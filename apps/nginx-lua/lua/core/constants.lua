local _M = {}

-- HTTP 相关常量
_M.HTTP = {
    METHODS = {
        GET = "GET",
        POST = "POST",
        PUT = "PUT",
        DELETE = "DELETE",
        OPTIONS = "OPTIONS"
    },
    HEADERS = {
        UPGRADE = "upgrade",
        CONNECTION = "connection",
        HOST = "host",
        ORIGIN = "origin",
        X_REAL_IP = "x-real-ip",
        X_FORWARDED_FOR = "x-forwarded-for",
        X_REQUEST_ID = "x-request-id",
        CONTENT_TYPE = "content-type",
        CONTENT_LENGTH = "content-length"
    }
}

-- WebSocket 相关常量
_M.WEBSOCKET = {
    HEADERS = {
        SEC_WEBSOCKET_KEY = "sec-websocket-key",
        SEC_WEBSOCKET_VERSION = "sec-websocket-version",
        SEC_WEBSOCKET_PROTOCOL = "sec-websocket-protocol",
        SEC_WEBSOCKET_ACCEPT = "sec-websocket-accept"
    }
}

-- 日志级别常量
_M.LOG_LEVELS = {
    DEBUG = 1,
    INFO = 2,
    NOTICE = 3,
    WARN = 4,
    ERROR = 5,
    FATAL = 6
}

-- 错误码常量
_M.ERROR_CODES = {
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    INTERNAL_ERROR = 500,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503,
    GATEWAY_TIMEOUT = 504
}

-- HTML 模板
_M.TEMPLATES = {
    LOADING = [[
<!doctype html>
<html>
    <head>
        <title>Loading...</title>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <script src="/__po.loading.js"></script>
        <link rel="preload" href="/__po.intcp.js" as="script">
    </head>
    <body></body>
</html>
]]
}

-- 正则表达式模式
_M.PATTERNS = {
    BASE64 = "^[A-Za-z0-9+/]*=?=?=?$",
    SPECIAL_PATH = "^/__po%.",
    URL_SCHEME = "^%w+://"
}

return _M
