local config = require "core.config"
local logger = require "utils.logger"

-- 初始化共享内存
local function init_shared_dict()
    for name, size in pairs(config.shared_dict) do
        local dict = ngx.shared[name]
        if not dict then
            ngx.log(ngx.WARN, string.format("[init] Shared dict %s not found", name))
        else
            ngx.log(ngx.NOTICE, string.format("[init] Initialized shared dict %s with size %s", name, size))
        end
    end
end

-- 初始化日志
local function init_logger()
    -- 获取环境变量中的日志级别
    local log_level = os.getenv("LOG_LEVEL")
    if not log_level then
        ngx.log(ngx.WARN, "[init] LOG_LEVEL environment variable not set, defaulting to NOTICE")
        log_level = "NOTICE"
    end
    log_level = string.lower(log_level)

    -- 设置 Lua 日志级别
    logger.set_level(log_level)


    -- 将日志级别存储到共享内存中
    local log_dict = ngx.shared.log_dict
    if log_dict then
        log_dict:set("current_level", log_level)
    end
end

-- 初始化共享内存
init_shared_dict()

-- 初始化日志
init_logger()

-- 输出初始化信息
ngx.log(ngx.NOTICE, string.format("[init] Application initialized in %s mode", config.ENV))
