local config = require "core.config"
local redis_util = require "utils.redis"
local cjson = require "cjson.safe"

local _M = {
    _VERSION = '0.1.0'
}

-- 全局变量：记录worker启动时间
local worker_start_time = ngx.now()

-- 共享内存字典，用于在Redis不可用时存储worker信息
local function get_shared_dict()
    return ngx.shared[config.metrics.dict_name]
end

-- 安全地获取ngx.var变量，如果不可用则返回默认值
local function safe_get_var(name, default)
    local ok, value = pcall(function() return ngx.var[name] end)
    return (ok and value ~= nil) and value or default
end

-- 将时间戳格式化为可读的时间字符串 (YYYY-MM-DD HH:MM:SS)
local function format_time(timestamp)
    return os.date("%Y-%m-%d %H:%M:%S", timestamp)
end

-- 获取外部IP地址（优先使用环境变量中的EXTERNAL_IP）
local function get_external_ip()
    local external_ip = os.getenv("EXTERNAL_IP")

    if external_ip and external_ip ~= "" then
        return external_ip
    end

    -- 如果环境变量未设置，则使用server_addr
    return safe_get_var("server_addr", "127.0.0.1")
end

-- 获取系统负载（1分钟平均负载）
local function get_system_load()
    local file = io.open("/proc/loadavg", "r")
    if not file then
        return 0.00
    end

    local content = file:read("*line")
    file:close()

    if content then
        local load = content:match("^([%d%.]+)")
        if load then
            return tonumber(string.format("%.2f", tonumber(load) or 0))
        end
    end

    return 0.00
end

-- 获取内存使用率
local function get_memory_usage()
    local file = io.open("/proc/meminfo", "r")
    if not file then
        return 0.00
    end

    local mem_total, mem_available = 0, 0

    for line in file:lines() do
        local key, value = line:match("^(%w+):%s+(%d+)")
        if key == "MemTotal" then
            mem_total = tonumber(value) or 0
        elseif key == "MemAvailable" then
            mem_available = tonumber(value) or 0
        end

        -- 找到需要的两个值就退出
        if mem_total > 0 and mem_available > 0 then
            break
        end
    end

    file:close()

    if mem_total > 0 then
        local usage = (mem_total - mem_available) / mem_total
        return tonumber(string.format("%.2f", usage))
    end

    return 0.00
end



-- 检查是否是主心跳worker
function _M.is_primary_heartbeat_worker()
    return ngx.worker.id() == 0
end

-- 构建worker键前缀（用于批量操作）
local function build_worker_key_prefix()
    return config.redis.key_prefix .. config.heartbeat.worker_key .. ":"
end

-- 构建worker键
local function build_worker_key(worker_id)
    return build_worker_key_prefix() .. worker_id
end

-- 构建索引键
local function build_index_key()
    return config.redis.key_prefix .. config.heartbeat.active_workers_key
end

-- 获取worker信息
local function get_worker_info()
    local worker_info = {
        id = get_external_ip(),
        version = _M._VERSION,
        region = os.getenv("REGION") or "default",
        load = get_system_load(),
        memory_usage = get_memory_usage(),
        started_at = format_time(worker_start_time),
        timestamp = format_time(ngx.now())
    }
    return worker_info
end

-- 将worker信息存储到共享内存中
local function store_to_shared_dict(worker_info)
    local dict = get_shared_dict()
    if not dict then
        return false
    end

    -- 存储worker信息
    local worker_key = config.metrics.worker_info_prefix .. worker_info.id
    local heartbeat_key = config.metrics.worker_heartbeat_prefix .. worker_info.id

    local ok, err = dict:set(worker_key, cjson.encode(worker_info))
    if not ok then
        return false
    end

    ok, err = dict:set(heartbeat_key, ngx.now())
    if not ok then
        return false
    end

    return true
end

-- 通过Redis注册worker信息
local function register_to_redis(worker_info)
    local worker_id = worker_info.id
    local worker_key = build_worker_key(worker_id)
    local index_key = build_index_key()
    local current_time = ngx.now()
    local ttl = config.heartbeat.worker_ttl
    local worker_json = cjson.encode(worker_info)

    -- 使用Lua脚本原子性执行多个操作
    local script = [[
        -- 1. 设置worker详细信息，带TTL
        redis.call('SETEX', KEYS[1], ARGV[1], ARGV[2])

        -- 2. 添加到活跃worker索引
        redis.call('ZADD', KEYS[2], ARGV[3], ARGV[4])

        -- 3. 获取当前活跃worker数量
        local count = redis.call('ZCARD', KEYS[2])

        return count
    ]]

    return redis_util.exec(function(redis)
        -- 执行Lua脚本
        local result, err = redis:eval(
            script,
            2,                      -- 2个键
            worker_key,             -- KEYS[1]
            index_key,              -- KEYS[2]
            tostring(ttl),          -- ARGV[1] - worker TTL
            worker_json,            -- ARGV[2] - worker信息JSON
            tostring(current_time), -- ARGV[3] - 当前时间戳
            worker_id               -- ARGV[4] - worker ID
        )

        if not result then
            return false
        end

        return true
    end)
end

-- 清理过期worker
function _M.cleanup_expired_workers()
    local index_key = build_index_key()
    local expire_time = ngx.now() - config.heartbeat.worker_ttl
    local worker_key_prefix = build_worker_key_prefix()

    -- 使用Lua脚本原子性执行清理操作
    local script = [[
        -- 1. 使用ZRANGEBYSCORE获取所有可能过期的worker
        local expired_workers = redis.call('ZRANGEBYSCORE', KEYS[1], 0, ARGV[1])
        local removed = 0

        -- 2. 检查每个worker的键是否存在
        for i, worker_id in ipairs(expired_workers) do
            local worker_key = ARGV[2] .. worker_id
            local exists = redis.call('EXISTS', worker_key)

            -- 如果worker键不存在，从索引中删除
            if exists == 0 then
                redis.call('ZREM', KEYS[1], worker_id)
                removed = removed + 1
            end
        end

        -- 3. 获取当前活跃worker数量
        local active_count = redis.call('ZCARD', KEYS[1])

        return {removed, active_count}
    ]]

    -- 尝试清理Redis中的过期worker
    local success, result = pcall(function()
        return redis_util.exec(function(redis)
            local result, err = redis:eval(
                script,
                1,                     -- 1个键
                index_key,             -- KEYS[1]
                tostring(expire_time), -- ARGV[1] - 过期时间
                worker_key_prefix      -- ARGV[2] - worker键前缀
            )

            if result and type(result) == "table" then
                local removed, active_count = result[1], result[2]
                return removed
            end

            return 0
        end)
    end)

    -- 清理共享内存中的过期worker
    local dict = get_shared_dict()
    if dict then
        local keys = dict:get_keys(0)
        local now = ngx.now()
        local expire_time = now - config.heartbeat.worker_ttl
        local removed = 0

        for _, key in ipairs(keys) do
            if key:find(config.metrics.worker_heartbeat_prefix) == 1 then
                local last_heartbeat = dict:get(key)
                if last_heartbeat and last_heartbeat < expire_time then
                    -- 提取worker ID
                    local worker_id = key:sub(#config.metrics.worker_heartbeat_prefix + 1)

                    -- 删除心跳和信息
                    dict:delete(key)
                    dict:delete(config.metrics.worker_info_prefix .. worker_id)
                    removed = removed + 1
                end
            end
        end
    end

    return success and result or 0
end

-- 发送心跳
function _M.send_heartbeat()
    -- 检查worker是否正在退出
    if ngx.worker.exiting() then
        _M.setup_exit_handler()
        return false
    end

    -- 获取worker信息
    local worker_info = get_worker_info()

    -- 尝试通过Redis注册
    local success = register_to_redis(worker_info)

    if not success then
        -- Redis失败时，存储到共享内存
        store_to_shared_dict(worker_info)
        return false
    end

    return true
end

-- 初始化心跳服务
function _M.init()
    -- 只有主心跳worker才进行心跳初始化
    if not _M.is_primary_heartbeat_worker() then
        return true
    end

    -- 创建定时器执行实际初始化
    local ok, err = ngx.timer.at(0, function(premature)
        if premature then return false end

        -- 获取worker信息并注册
        local worker_info = get_worker_info()
        local success = register_to_redis(worker_info)

        if not success then
            store_to_shared_dict(worker_info)
            return false
        end

        -- 执行一次清理，确保索引一致性
        pcall(_M.cleanup_expired_workers)
        return true
    end)

    if not ok then
        return false
    end

    return true
end

-- 设置退出处理函数，确保worker退出时从索引中移除
function _M.setup_exit_handler()
    local worker_id = get_external_ip()
    local index_key = build_index_key()

    -- 尝试从Redis索引中移除
    pcall(function()
        return redis_util.exec(function(redis)
            redis:zrem(index_key, worker_id)

            -- 同时删除worker详细信息
            local worker_key = build_worker_key(worker_id)
            redis:del(worker_key)

            return true
        end)
    end)
end

return _M
