local _M = {
    _VERSION = '0.1.0'
}

-- 环境配置
_M.ENV = os.getenv("NGINX_ENV") or "development"

-- 基础配置
_M.base = {
    session_key = "__poss",
    pot_key = "__pot",
    service_worker_key= "__posw",
    web_worker_key = "__poww",
    special_paths = {
        favicon = "/favicon.ico",
        static_prefix = "/__po."
    }
}

-- JWT配置
_M.jwt = {
    secret = os.getenv("JWT_SECRET")
}

-- 日志配置
_M.log = {
    level = os.getenv("LOG_LEVEL") or "INFO"
}

-- 共享内存配置
_M.shared_dict = {
    limit_req = "50m",
    proxy_cache = "100m",
    metrics = "20m",
    log_dict = "10m"
}

-- 指标配置
_M.metrics = {
    dict_name = "metrics",                        -- 共享内存字典名
    total_requests = "total_requests",            -- 总请求数指标名
    current_connections = "current_connections",  -- 当前连接数指标名
    worker_info_prefix = "worker_info:",          -- worker信息前缀
    worker_heartbeat_prefix = "worker_heartbeat:" -- worker心跳前缀
}

-- 定时任务配置
_M.timer = {
    heartbeat_interval = tonumber(os.getenv("HEARTBEAT_INTERVAL"))        -- 心跳间隔3秒
}

-- Redis配置
_M.redis = {
    host = os.getenv("REDIS_HOST"),           -- 连接到本地Nginx Stream代理
    port = tonumber(os.getenv("REDIS_PORT")), -- 使用Stream模块代理的端口
    username = os.getenv("REDIS_USERNAME"),
    password = os.getenv("REDIS_PASSWORD"),
    db_index = tonumber(os.getenv("REDIS_DB")),
    timeout = 1000,                             -- 连接超时时间（毫秒）
    pool_size = 5,                              -- 连接池大小（只是心跳用，不用太多）
    idle_timeout = 600000,                      -- 空闲连接超时时间（10分钟，单位：毫秒）
    key_prefix = os.getenv("REDIS_KEY_PREFIX"), -- Redis键前缀
}

-- 心跳配置
_M.heartbeat = {
    enabled = os.getenv("HEARTBEAT_ENABLED") == "true",            -- 是否启用心跳功能
    worker_ttl = tonumber(os.getenv("HEARTBEAT_WORKER_TTL")),      -- worker过期时间（秒）
    worker_key = os.getenv("HEARTBEAT_WORKER_KEY"),                -- worker键前缀
    active_workers_key = os.getenv("HEARTBEAT_ACTIVE_WORKERS_KEY") -- 活跃worker索引键名
}

-- 获取当前环境的配置
function _M.get_env_config()
    return _M[_M.ENV] or _M.development
end

return _M
