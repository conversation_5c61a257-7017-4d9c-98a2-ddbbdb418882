local logger = require "utils.logger"
local config = require "core.config"
local heartbeat = require "core.heartbeat"
local metrics = require "middleware.metrics"

-- 常量定义
local WORKER_EXIT_CHECK_INTERVAL = 1 -- 秒

-- 安全执行函数，捕获所有错误
local function safe_execute(func, ...)
    local ok, result = pcall(func, ...)
    if not ok then
        ngx.log(ngx.ERR, string.format("[init_worker] Error executing function: %s", result))
        return nil
    end
    return result
end

-- 输出关键环境变量信息
local function log_environment_variables()
    ngx.log(ngx.NOTICE, "环境变量信息:")
    ngx.log(ngx.NOTICE, "NGINX_ENV: ", os.getenv("NGINX_ENV") or "未设置")
    ngx.log(ngx.NOTICE, "REDIS_HOST: ", os.getenv("REDIS_HOST") or "未设置")
    ngx.log(ngx.NOTICE, "HEARTBEAT_ENABLED: ", os.getenv("HEARTBEAT_ENABLED") or "未设置")
end

-- 执行进程退出清理
local function cleanup()
    ngx.log(ngx.NOTICE, "[init_worker] Worker process cleanup started")
    safe_execute(logger.cleanup_files)
    ngx.log(ngx.NOTICE, "[init_worker] Worker process cleanup completed")
end



-- 心跳任务处理器
local function heartbeat_handler(premature)
    if premature or ngx.worker.exiting() then return end

    -- 只有主心跳worker才发送心跳
    if heartbeat.is_primary_heartbeat_worker() then
        safe_execute(heartbeat.send_heartbeat)
    end
end

-- worker退出状态检查处理器
local function check_worker_exiting()
    if ngx.worker.exiting() then
        -- 调用心跳服务的退出处理函数
        if config.heartbeat.enabled then
            safe_execute(heartbeat.setup_exit_handler)
        end

        -- 执行清理
        cleanup()
    end
end

-- 初始化心跳服务
local function init_heartbeat()
    -- 检查心跳功能是否启用
    if not config.heartbeat.enabled then
        return true
    end

    -- 只在主心跳worker上初始化
    if heartbeat.is_primary_heartbeat_worker() then
        return safe_execute(heartbeat.init)
    end

    return true
end

-- 初始化指标统计
local function init_metrics()
    return safe_execute(metrics.init)
end

-- 主初始化逻辑
-- 1. 输出环境变量信息
log_environment_variables()

-- 2. 初始化心跳服务
init_heartbeat()

-- 3. 初始化指标统计
init_metrics()

-- 4. 设置定时任务
-- 4.1 创建退出检查定时器
ngx.timer.every(WORKER_EXIT_CHECK_INTERVAL, check_worker_exiting)

-- 4.2 创建心跳定时器
if config.heartbeat.enabled then
    ngx.timer.every(config.timer.heartbeat_interval, heartbeat_handler)
end

-- 5. 输出初始化完成日志
ngx.log(ngx.NOTICE, string.format("[init_worker] Worker process %d initialized, environment: %s",
    ngx.worker.id(),
    config.ENV
))
