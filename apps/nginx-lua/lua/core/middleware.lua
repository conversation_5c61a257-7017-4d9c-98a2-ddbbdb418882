-- apps/nginx-lua/lua/core/compose.lua

local exception = require "middleware.exception"
local logger = require "utils.logger"

local _M = {
    _VERSION = "0.1.0"
}

local log = logger.new("compose")

-- 错误处理包装
function _M.with_error_handling(handler)
    return function(...)
        local ok, result = pcall(handler, ...)
        -- 只处理 Lua 运行时错误，业务异常由 exception.throw 直接处理
        if not ok then
            log.error("Uncaught Lua error: %s", result)
            exception.throw(
                exception.ERROR_TYPES.INTERNAL_ERROR,
                "Internal server error",
                { original_error = result }
            )
        end
        return result
    end
end

-- 中间件包装器
function _M.compose(handlers)
    -- 如果已经处理过异常，跳过后续中间件
    if ngx.ctx.exception then
        return true
    end

    -- 处理中间件
    for _, handler in ipairs(handlers) do
        local fn = handler
        if type(handler) == "table" and handler.handle then
            fn = handler.handle
        end

        if type(fn) == "function" then
            local wrapped_handler = _M.with_error_handling(fn)
            -- 如果处理器返回 false，说明需要中断执行
            if wrapped_handler() == false then
                return
            end
        else
            log.error("Invalid handler type: %s", type(handler))
        end
    end
end

return _M
