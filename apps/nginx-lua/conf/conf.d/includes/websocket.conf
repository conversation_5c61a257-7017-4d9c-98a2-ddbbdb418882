# WebSocket 代理配置
location /__pows {
    # 上游连接错误处理
    proxy_intercept_errors on;
    error_page 502 503 504 = @upstream_error_handler;

    set_by_lua_block $session {
        return require("middleware.session").handle()
    }

    access_by_lua_block {
        require("core.middleware").compose({
            require("middleware.auth").check,
            require("middleware.metrics").handle_request
        })
    }

    rewrite_by_lua_block {
        require("core.middleware").compose({
            require("handlers.websocket")
        })
    }

    # 代理设置
    proxy_http_version 1.1;
    proxy_set_header Host $proxy_host;

    # WebSocket 特定设置
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;

    # WebSocket 优化
    proxy_read_timeout 3600s;
    proxy_send_timeout 3600s;
    proxy_buffering off;

    # SSL 设置
    proxy_ssl_server_name on;
    proxy_ssl_protocols TLSv1.2 TLSv1.3;
    proxy_ssl_verify off;

    proxy_pass $original_url;

    # 处理响应
    header_filter_by_lua_block {
        require("core.middleware").compose({
            require("middleware.response").handle
        })
    }

    # 记录请求完成
    log_by_lua_block {
        require("core.middleware").compose({
            require("middleware.metrics").handle_response
        })
    }
}
