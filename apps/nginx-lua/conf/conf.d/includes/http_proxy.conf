# HTTP 代理配置
location / {
    # 上游连接错误处理
    proxy_intercept_errors on;
    error_page 502 503 504 = @upstream_error_handler;

    set_by_lua_block $session {
        return require("middleware.session").handle()
    }

    access_by_lua_block {
        require("core.middleware").compose({
            require("middleware.auth").check,
            require("middleware.metrics").handle_request
        })
    }

    # 设置代理目标变量
    rewrite_by_lua_block {
        require("core.middleware").compose({
            require("handlers.http").handle
        })
    }

    # 必要的代理头部
    proxy_set_header Host $proxy_host;
    proxy_set_header User-Agent $http_user_agent;
    proxy_set_header Accept $http_accept;
    # 跳过cookie和referer，不然这里会覆盖, 因为已在referer_util.rewrite_request_referer中处理,
    # proxy_set_header Cookie $http_cookie;
    # proxy_set_header Referer $http_referer;

    # SSL 设置
    proxy_ssl_server_name on;
    proxy_ssl_protocols TLSv1.2 TLSv1.3;
    proxy_ssl_verify off;

    # 性能优化
    proxy_buffering off;
    chunked_transfer_encoding on;

    proxy_pass $original_url;

    # 处理响应
    header_filter_by_lua_block {
        require("core.middleware").compose({
            require("middleware.response").handle
        })
    }

    # 处理响应体
    body_filter_by_lua_block {
        require("core.middleware").compose({
            require("middleware.body").handle
        })
    }

    # 记录请求完成
    log_by_lua_block {
        require("core.middleware").compose({
            require("middleware.metrics").handle_response
        })
    }
}
