# 静态文件处理
location ~ /__po\.(sw|intcp|loading|fp|pm)\.js$ {
    access_by_lua_block {
        ngx.header["X-PO-Page"] = "1"
    }

    add_header Pragma "no-cache";
    add_header Cache-Control "no-cache";
    add_header X-Content-Type-Options "nosniff";

    root /usr/local/openresty/nginx/html;

    # 确保错误状态码被正确传递
    proxy_intercept_errors on;
    error_page 404 @error_handler;
    try_files $uri =404;
}

# 认证API端点
location = /__poa {
    # 设置安全相关头部
    add_header X-Content-Type-Options "nosniff";
    add_header Cache-Control "no-store, no-cache, must-revalidate";

    set_by_lua_block $session {
        return require("middleware.session").handle()
    }

    # 调用认证处理函数
    content_by_lua_block {
        require("middleware.auth").handle_auth_api()
    }
}

# favicon.ico 处理规则 - 使用 map 指令简化
location = /favicon.ico {
    # 使用 map 指令设置的变量处理
    if ($favicon_action = "not_found") {
        return 404;
    }

    # 带参数的请求，走代理处理
    rewrite ^ / last;
}
