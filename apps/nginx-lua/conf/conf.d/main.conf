server {
    listen 80;
    listen [::]:80;

    server_name _;
    server_tokens off;

    # 在请求开始时设置唯一请求ID
    set_by_lua_block $po_request_id {
        return require("middleware.request_id").handle()
    }

    # 健康检查配置 - 在HTTP上也提供健康检查
    include conf.d/includes/health.conf;

    # 证书验证文件访问配置
    location /.well-known/pki-validation/ {
        alias /usr/local/openresty/nginx/html/.well-known/pki-validation/;
        try_files $uri =404;
    }

    # HTTP重定向到HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
    include conf.d/includes/vars.conf;
}

# HTTPS服务器配置 - 注意：实际的HTTPS连接由stream模块处理
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    # http2 on;
    server_name _;
    server_tokens off;

    # 在HTTPS请求中也设置请求ID
    set_by_lua_block $po_request_id {
        return require("middleware.request_id").handle()
    }

    # SSL证书配置
    ssl_certificate /usr/local/openresty/nginx/ssl/https/fullchain.crt;
    ssl_certificate_key /usr/local/openresty/nginx/ssl/https/private.key;

    # SSL优化配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;

    # HSTS (可选，建议在生产环境启用)
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # 包含其他配置文件
    include conf.d/includes/vars.conf;
    include conf.d/includes/health.conf;
    include conf.d/includes/static.conf;
    include conf.d/includes/websocket.conf;
    include conf.d/includes/http_proxy.conf;
    include conf.d/includes/upstream_error.conf;
}
