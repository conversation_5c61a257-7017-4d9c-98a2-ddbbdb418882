# favicon.ico 处理配置
map $uri$is_args$args $favicon_action {
    "~/favicon.ico$" "not_found"; # 不带参数的 favicon.ico 请求
    "~/favicon.ico\?.+" "proxy"; # 带参数的 favicon.ico 请求
    default ""; # 其他请求
}

# 检测URL是否包含__posw=1参数
map $args $has_posw_param {
    "~(^|&)__posw=1(&|$)" "1"; # URL包含__posw=1参数
    default "0"; # 不包含该参数
}

# 检测是否为JS文件
map $uri $is_js_file {
    "~\.js$" "1"; # 是JS文件
    default "0"; # 不是JS文件
}

# 合并检测：是否为带__posw=1参数的JS文件
map "$is_js_file:$has_posw_param" $is_posw_js {
    "1:1" "1"; # 同时满足两个条件
    default "0"; # 不满足条件
}

# WebSocket 配置
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}
