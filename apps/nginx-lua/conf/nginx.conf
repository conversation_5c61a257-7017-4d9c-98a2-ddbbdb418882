# 设置 worker 进程数
worker_processes auto;

# 优化设置
worker_rlimit_nofile 65535;

# 允许访问环境变量
env LOG_LEVEL;
env EXTERNAL_IP;
env REGION;
env HOSTNAME;
env NGINX_ENV;
env REDIS_HOST;
env REDIS_PORT;
env REDIS_USERNAME;
env REDIS_PASSWORD;
env REDIS_DB;
env REDIS_KEY_PREFIX;
env JWT_SECRET;
env HEARTBEAT_INTERVAL;
env HEARTBEAT_ENABLED;
env HEARTBEAT_WORKER_TTL;
env HEARTBEAT_WORKER_KEY;
env HEARTBEAT_ACTIVE_WORKERS_KEY;

# 设置默认日志级别为 info
error_log /usr/local/openresty/nginx/logs/error.log info;

pid /var/run/nginx.pid;

# 引入Stream模块配置
include /usr/local/openresty/nginx/conf/stream.conf;

events {
    # 每个 worker 进程的最大连接数
    worker_connections 65535;

    # 使用 epoll 事件模型
    use epoll;

    # 启用多个接受连接的系统调用
    multi_accept on;
}

http {
    include mime.types;
    default_type application/octet-stream;

    # Lua 配置
    lua_package_path "/usr/local/openresty/nginx/lua/?.lua;;";
    lua_code_cache off; # 开发环境默认关闭缓存
    lua_max_pending_timers 1024;
    lua_max_running_timers 256;

    # 共享内存配置
    lua_shared_dict limit_req 50m;
    lua_shared_dict proxy_cache 100m;
    lua_shared_dict metrics 20m;
    lua_shared_dict log_dict 10m;

    # 初始化配置
    init_by_lua_file lua/core/init.lua;
    init_worker_by_lua_file lua/core/init_worker.lua;

    # 优化基础配置
    server_tokens off; # 关闭服务器标记
    more_set_headers "Server: cloudflare"; # 替换服务器标识为cloudflare

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 300; # 增加到300s以支持长连接
    keepalive_requests 10000; # 增加到10000以提高性能
    keepalive_disable none; # 禁用keepalive_disable

    # 优化缓冲区配置
    client_body_buffer_size 256k; # 增加到256k以处理较大的请求体
    client_max_body_size 200m; # 从100m增加到200m
    client_header_buffer_size 32k; # 大幅增加到32k以处理更长的请求头
    large_client_header_buffers 64 128k; # 大幅增加到64 128k以处理大量的cookie
    proxy_buffers 64 256k; # 从32 128k增加到64 256k
    proxy_buffer_size 256k; # 从128k增加到256k
    proxy_busy_buffers_size 512k; # 从256k增加到512k
    proxy_temp_file_write_size 256k; # 增加到256k以提高写入性能

    # Docker entrypoint.sh 设置的 DNS 解析器
    resolver DNS_PLACEHOLDER valid=30s ipv6=off;
    resolver_timeout 15s;

    # 设置真实 IP
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;
    real_ip_recursive on;

    # 代理配置优化
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    proxy_ssl_server_name on;
    proxy_ssl_protocols TLSv1.2 TLSv1.3;
    proxy_ssl_verify off;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    proxy_next_upstream_tries 3;

    # 代理头部配置
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host;

    # 日志配置优化 - 使用标准格式而不是 JSON
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for" '
    'request_id="$po_request_id" '
    'upstream_status="$upstream_status" '
    'upstream_addr="$upstream_addr" '
    'upstream_response_time="$upstream_response_time" '
    'request_time="$request_time"';

    # 同时输出到 stdout 和文件
    access_log /usr/local/openresty/nginx/logs/access.log main;

    include /usr/local/openresty/nginx/conf/conf.d/*.conf;
}
