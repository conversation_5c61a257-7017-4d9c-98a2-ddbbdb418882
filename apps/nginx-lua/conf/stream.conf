stream {
    # 错误日志设置
    error_log /usr/local/openresty/nginx/logs/stream_error.log info;

    # 本地Redis代理服务器
    server {
        listen 127.0.0.1:8380; # 修改为只监听本地地址
        proxy_connect_timeout 10s;
        proxy_timeout 300s; # 与Redis的长连接超时时间
        proxy_pass 167.234.209.161:6380;

        # SSL配置
        proxy_ssl on;
        proxy_ssl_certificate /usr/local/openresty/nginx/ssl/redis/client.crt;
        proxy_ssl_certificate_key /usr/local/openresty/nginx/ssl/redis/client.key;
        proxy_ssl_trusted_certificate /usr/local/openresty/nginx/ssl/redis/ca.crt;
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_ssl_protocols TLSv1.2 TLSv1.3;
    }
}