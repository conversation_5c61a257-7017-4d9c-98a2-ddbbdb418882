# Build stage
FROM openresty/openresty:alpine-fat AS builder

# Install required packages
RUN apk add --no-cache git

# Install Lua dependencies
COPY ./nginx-lua-0.1.0-1.rockspec /tmp/
RUN cd /tmp && luarocks install --only-deps nginx-lua-0.1.0-1.rockspec

# Runtime stage
FROM openresty/openresty:alpine

# 只复制必要的 Lua 依赖
COPY --from=builder /usr/local/openresty/luajit/share/lua/5.1 /usr/local/openresty/luajit/share/lua/5.1
COPY --from=builder /usr/local/openresty/luajit/lib/lua/5.1 /usr/local/openresty/luajit/lib/lua/5.1
COPY --from=builder /usr/local/openresty/lualib /usr/local/openresty/lualib

# 添加启动脚本和管理脚本
COPY ./scripts/*.sh /
RUN chmod +x /*.sh

# 使用脚本作为入口点
ENTRYPOINT ["/entrypoint.sh"]
