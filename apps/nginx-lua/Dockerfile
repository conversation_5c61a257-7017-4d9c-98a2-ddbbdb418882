###################
# Builder Stage
###################
FROM openresty/openresty:alpine-fat AS builder

WORKDIR /build

# 安装依赖
COPY ./nginx-lua-0.1.0-1.rockspec ./
RUN luarocks install --only-deps nginx-lua-0.1.0-1.rockspec

# 准备所有文件
COPY ./lua ./src
COPY ./conf ./dist/conf
COPY ./html ./dist/html
COPY ./ssl ./dist/ssl

# 修改 nginx 配置
RUN sed -i 's/lua_code_cache off;/lua_code_cache on;/' ./dist/conf/nginx.conf

# 编译 Lua 代码为字节码
RUN cd src && \
    find . -name "*.lua" -type f -exec sh -c '\
    mkdir -p ../dist/lua/$(dirname "{}") && \
    luajit -b "{}" "../dist/lua/{}"' \;

###################
# Runtime Stage
###################
FROM openresty/openresty:alpine AS runtime

# 设置环境变量简化路径
ENV NGINX_ROOT=/usr/local/openresty/nginx \
    LUA_ROOT=/usr/local/openresty/luajit \
    DNS_SERVERS=""

# 只复制 luarocks 安装的依赖
COPY --from=builder $LUA_ROOT/share/lua/5.1 $LUA_ROOT/share/lua/5.1
COPY --from=builder $LUA_ROOT/lib/lua/5.1 $LUA_ROOT/lib/lua/5.1
COPY --from=builder /build/dist $NGINX_ROOT

WORKDIR $NGINX_ROOT

# 添加启动脚本和管理脚本
COPY ./scripts/*.sh /
RUN chmod +x /*.sh

# 使用脚本作为入口点
ENTRYPOINT ["/entrypoint.sh"]
