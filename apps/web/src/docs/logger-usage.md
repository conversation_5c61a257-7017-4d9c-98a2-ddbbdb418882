# Next.js 日志系统使用指南

本项目使用 Winston 和 Winston Daily Rotate File 实现了强大的日志系统。日志文件按天轮转，并保留最近 30 天的日志记录。

## 日志文件

日志文件存储在项目根目录的 `logs` 文件夹下：

- `application-YYYY-MM-DD.log` - 包含所有级别的日志
- `error-YYYY-MM-DD.log` - 仅包含错误级别的日志

## 日志级别

系统使用以下日志级别（优先级从高到低）：

1. **error** - 错误信息
2. **warn** - 警告信息
3. **info** - 一般信息
4. **http** - HTTP 请求信息
5. **debug** - 调试信息

在生产环境中，默认只记录 `info` 及以上级别的日志。在开发环境中，记录所有级别的日志。

## 在 API 路由中使用

### 基本使用

在 API 路由中使用日志系统最简单的方法是直接导入 logger：

```typescript
import logger from '@/lib/logger'

export async function GET(req: NextRequest) {
  logger.info('处理 GET 请求')

  // 业务逻辑...

  return NextResponse.json({ message: '操作成功' })
}
```

### 使用 API 日志中间件

我们提供了 `apiLogger` 中间件，可以自动记录 API 请求和响应：

```typescript
import { apiLogger } from '@/lib/api-logger'

export async function GET(req: NextRequest) {
  return apiLogger(req, async () => {
    // 处理请求...
    return NextResponse.json({ message: '操作成功' })
  })
}
```

### 全局错误处理

使用 `withErrorLogging` 高阶函数可以捕获并记录未处理的错误：

```typescript
import { withErrorLogging } from '@/lib/error-logger'

async function handler(req: NextRequest) {
  // 处理请求...
  return NextResponse.json({ message: '操作成功' })
}

export const GET = withErrorLogging(handler)
```

## 使用日志工具函数

对于常规组件或服务，推荐使用 `log-utils.ts` 中提供的工具函数：

```typescript
import logUtils from '@/utils/log-utils'

// 简单日志
logUtils.info('这是一条信息日志')
logUtils.warn('这是一条警告日志')
logUtils.error('这是一条错误日志')
logUtils.debug('这是一条调试日志')

// 带上下文的日志
logUtils.info('用户登录', { userId: '123', username: 'admin' })

// 错误日志
try {
  // 业务逻辑...
} catch (error) {
  logUtils.error('操作失败', error, { userId: '123' })
}
```

## 最佳实践

1. **分级记录**：根据信息的重要性选择合适的日志级别
2. **结构化日志**：尽量使用对象作为上下文，而不是拼接字符串
3. **敏感信息过滤**：永远不要记录密码、令牌等敏感信息
4. **包含关键信息**：记录足够的上下文信息，以便于排查问题
5. **性能考虑**：在循环中避免过多的日志记录
