import { NextRequest, NextResponse } from 'next/server'

import { createLogger } from './logger'

// 创建专用的错误日志记录器
const logger = createLogger('error-logger')

/**
 * 全局错误处理中间件
 * 用于捕获和记录 API 路由中未处理的错误
 */
export async function errorLogger(
  req: NextRequest,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const response = await handler()
    return response
  } catch (error) {
    // 记录未处理的错误
    const errorMessage = error instanceof Error ? error.message : String(error)

    logger.error(`未捕获的API错误: ${errorMessage}`, {
      url: req.url,
      method: req.method,
      stack: error instanceof Error ? error.stack : undefined
    })

    // 返回适当的错误响应
    return NextResponse.json(
      {
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'production' ? undefined : errorMessage
      },
      { status: 500 }
    )
  }
}

/**
 * 创建一个高阶函数，用于包装 API 路由处理函数
 * 同时处理日志记录和错误捕获
 */
export function withErrorLogging(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest) => {
    return errorLogger(req, () => handler(req))
  }
}
