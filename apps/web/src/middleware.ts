import { i18nMiddleware } from '@momo/i18n-web/middleware'
import { type NextRequest } from 'next/server'

const middleware = (request: NextRequest) => {
  const csp = `
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' *;
    style-src 'self' 'unsafe-inline' *;
    img-src *;
    font-src 'self' data: *;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    connect-src *;
    media-src *;
    frame-ancestors 'none';
    frame-src *;
    worker-src blob: 'self';
    manifest-src 'self';
  `

  const response = i18nMiddleware(request)

  // 添加 DNS 预取指令
  response.headers.set('X-DNS-Prefetch-Control', 'on')
  response.headers.append('Link', '<https://app.chatwoot.com>; rel=dns-prefetch')

  // 设置 CSP
  response.headers.set('Content-Security-Policy', csp.replaceAll('\n', '').replaceAll(/\s+/g, ' '))

  return response
}

export const config = {
  /*
   * Match all request paths except for the ones starting with:
   * - api (API routes)
   * - _next/static (static files)
   * - _next/image (image optimization files)
   * - _vercel (Vercel internal)
   * - favicon.ico (favicon file)
   * - folders in public (which resolve to /foldername)
   * - sitemap.xml
   * - robots.txt
   * - ads.txt
   */
  matcher: [
    '/((?!api|_next/static|_next/image|_vercel|og|favicon|fonts|images|videos|favicon.ico|sitemap.xml|robots.txt|rss.xml|ads.txt).*)'
  ]
}

export default middleware
