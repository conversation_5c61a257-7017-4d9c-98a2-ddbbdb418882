import { toProxyUrl } from '@momo/utils'
import { NextRequest, NextResponse } from 'next/server'

import { selectProxyServer } from '@/config/proxy-servers'
import { createLogger } from '@/lib/logger'
import { verifyToken } from '@/utils/jwt'

// 创建专用的API Logger实例
const logger = createLogger('servers')

/**
 * 检查字符串是否可能是一个网站URL
 * 简化判断：只要包含点号，且不以点号开头或结尾，就认为可能是域名
 */
function isWebsite(input: string): boolean {
  // 移除可能的协议前缀和www前缀进行检查
  const cleanInput = input.replace(/^(https?:\/\/)?(www\.)?/, '')

  // 移除可能的端口号部分
  const withoutPort = cleanInput.replace(/:\d+$/, '')

  // 检查是否包含点号，且不是以点号开头或结尾
  // 同时排除空格，如果包含空格可能是搜索查询
  return (
    withoutPort.includes('.') &&
    !withoutPort.startsWith('.') &&
    !withoutPort.endsWith('.') &&
    !withoutPort.includes(' ')
  )
}

/**
 * 清理用户输入
 * - 移除首尾空格、换行符和制表符
 * - 移除危险字符
 * - 规范化URL格式
 */
function cleanUserInput(input: string): string {
  if (!input) return ''

  // trim() 会移除字符串两端的空格、制表符和换行符
  let cleaned = input.trim()

  // 对于URL，移除URL中间可能存在的不必要空格
  // 注意：真实URL通常不会包含空格，如果有，可能是用户错误输入
  cleaned = cleaned.replace(/\s+/g, '')

  // 移除可能的危险字符，保留URL必要的特殊字符
  cleaned = cleaned.replace(/[<>"`'\\]/g, '')

  return cleaned
}

/**
 * 处理用户输入
 * 如果是网站，确保有https协议
 * 如果不是网站，转为DuckDuckGo搜索
 */
function processUserInput(input: string): string {
  if (!input) return ''

  // 首先清理用户输入
  const cleanedInput = cleanUserInput(input)

  if (!cleanedInput) return ''

  // 检查是否可能是网站
  if (isWebsite(cleanedInput)) {
    // 如果是网站但不包含协议，添加https协议
    return cleanedInput.includes('://') ? cleanedInput : `https://${cleanedInput}`
  } else {
    // // 如果不是网站，转为DuckDuckGo搜索
    // return `https://duckduckgo.com/?q=${encodeURIComponent(cleanedInput)}`
    // 如果不是网站，转为必应搜索
    return `https://bing.com/search?q=${encodeURIComponent(cleanedInput)}`
  }
}

/**
 * POST request handler - Verify JWT token and process proxy request
 */
export async function POST(request: NextRequest) {
  try {
    let input: string | null = null
    let region: string | null = null
    // 从请求头中获取token
    const token = request.headers.get('x-stats-id')

    // Check Content-Type, support different request formats
    const contentType = request.headers.get('content-type') || ''

    if (contentType.includes('application/x-www-form-urlencoded')) {
      // Handle URL encoded form data
      const formData = await request.text()
      const params = new URLSearchParams(formData)
      input = params.get('input')
      region = params.get('r')
      // 不再从表单中获取token

      logger.debug(
        `URL encoded form data: input=${input}, token=${token ? '(provided in header)' : '(missing)'}`
      )
    } else {
      // Handle multipart/form-data or other formats
      const formData = await request.formData()
      input = formData.get('input') as string
      region = formData.get('r') as string
      // 不再从表单中获取token

      logger.debug(
        `Form data: input=${input}, token=${token ? '(provided in header)' : '(missing)'}`
      )
    }

    // If URL is empty, return error
    if (!input) {
      logger.warn(`URL is missing in request`)
      return NextResponse.json({ error: 'URL is required' }, { status: 400 })
    }

    // Verify JWT token
    if (!token) {
      logger.error(`Token missing in request`)
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 })
    }

    // 打印接收到的混淆token（仅开发环境）
    if (process.env.NODE_ENV === 'development') {
      logger.debug(`接收到的混淆token: ${token}`)
    }

    try {
      const payload = await verifyToken(token)
      if (!payload) {
        logger.error(`Token verification returned null`)
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 403 })
      }

      // 打印解析后的payload（仅开发环境）
      if (process.env.NODE_ENV === 'development') {
        logger.debug(`解析后的payload: ${JSON.stringify(payload)}`)
      }

      // 处理用户输入
      const processedInput = processUserInput(input)
      logger.info(`处理后的用户输入: ${processedInput}`)

      // Get proxy server URL
      const proxyServer = await selectProxyServer(region || undefined)
      logger.debug(`选择的代理服务器: ${proxyServer}${region ? ` (region: ${region})` : ''}`)

      // Convert to proxy URL (不再需要addHttpsPrefix，因为processUserInput已经处理)
      const proxyUrl = toProxyUrl(processedInput, addHttpsPrefix(proxyServer))

      // 将token作为__poss参数添加到proxyUrl中
      const finalProxyUrl = addTokenToUrl(proxyUrl, token)
      logger.info(`重定向到代理URL: ${finalProxyUrl}`)

      // Return minimal HTML for instant redirect
      const html = `<!DOCTYPE html><html><head><meta charset="UTF-8"><meta http-equiv="x-refresh" content="${finalProxyUrl}"></head></html>`

      const response = new NextResponse(html, {
        headers: {
          'Content-Type': 'text/html',
          'Cache-Control': 'no-store, no-cache, must-revalidate'
        }
      })

      return response
    } catch (error) {
      logger.error(`Error during token verification or proxy generation: ${error}`)

      return NextResponse.json({ error: 'Authentication failed' }, { status: 403 })
    }
  } catch (error) {
    logger.error(`Error processing proxy request: ${error}`)

    return NextResponse.json({ error: 'Failed to process request' }, { status: 500 })
  }
}

/**
 * 添加URL协议前缀
 */
function addHttpsPrefix(url: string): string {
  if (!url) return ''
  return url.includes('://') ? url : `https://${url}`
}

/**
 * 将token作为__poss参数添加到URL中
 */
function addTokenToUrl(url: string, token: string): string {
  try {
    const urlObj = new URL(url)
    urlObj.searchParams.set('__poss', token)
    return urlObj.toString()
  } catch (error) {
    logger.error(`Error adding token to URL: ${error}`)
    return url
  }
}
