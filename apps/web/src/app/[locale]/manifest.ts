import { getTranslations } from '@momo/i18n-web/server'
import type { MetadataRoute } from 'next'

type ManifestProps = {
  params: Promise<{
    locale: string
  }>
}

export default async function manifest(props: ManifestProps): Promise<MetadataRoute.Manifest> {
  const { locale } = await props.params
  const t = await getTranslations({ locale, namespace: 'metadata' })

  return {
    name: t('site-title'),
    short_name: t('site-title'),
    description: t('site-description'),
    start_url: `/${locale}`,
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    icons: [
      {
        src: '/favicon/android-chrome-192x192.png',
        sizes: '192x192',
        type: 'image/png'
      },
      {
        src: '/favicon/android-chrome-512x512.png',
        sizes: '512x512',
        type: 'image/png'
      }
    ],
    categories: ['productivity', 'utilities'],
    lang: locale,
    dir: locale === 'ar' || locale === 'he' ? 'rtl' : 'ltr'
  }
}
