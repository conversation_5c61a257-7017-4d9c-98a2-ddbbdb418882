'use client'

import { useLocale } from '@momo/i18n-web/client'
import { i18n } from '@momo/i18n-web/config'
import { usePathname } from 'next/navigation'

import Footer from '@/components/layout/footer'
import Header from '@/components/layout/header'

type LayoutProps = {
  children: React.ReactNode
}

const Layout = (props: LayoutProps) => {
  const { children } = props
  const pathname = usePathname()
  const locale = useLocale()

  // 判断是否为首页：
  // 1. 路径为根路径 '/' (默认英文)
  // 2. 非默认语言时匹配 /{locale} 或 /{locale}/
  const isHomePage =
    pathname === '/' ||
    (locale !== i18n.defaultLocale && new RegExp(`^/${locale}/?$`).test(pathname))

  return (
    <>
      <Header />
      <main id='skip-nav' className='mx-auto mb-16 max-w-5xl px-4 pt-12 sm:px-8'>
        {children}
      </main>
      <Footer />

      {/* 顶部光晕效果 */}
      <div
        className={`absolute left-0 right-0 top-0 -z-10 overflow-hidden ${isHomePage ? 'h-[550px]' : 'h-[350px]'}`}
      >
        <div className='absolute inset-0'>
          {isHomePage ? (
            // 首页光晕效果
            <>
              {/* 基础光晕背景 */}
              <div className='absolute inset-x-0 top-0 h-full bg-gradient-to-r from-transparent via-pink-100/50 to-transparent dark:via-pink-500/20' />
              <div className='absolute inset-x-0 top-0 h-full bg-gradient-to-r from-blue-100/50 via-violet-100/50 to-blue-100/50 blur-[64px] dark:from-blue-500/20 dark:via-violet-500/20 dark:to-blue-500/20' />

              {/* 中心增强光晕 - 多层叠加 */}
              <div className='absolute left-1/2 top-0 h-[500px] w-[800px] -translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.violet.200/60%),transparent_70%)] blur-[48px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.violet.500/25%),transparent_70%)]' />
              <div className='absolute left-[40%] top-0 h-[450px] w-[700px] -translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.blue.200/50%),transparent_65%)] blur-[40px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.blue.500/20%),transparent_65%)]' />
              <div className='absolute left-[60%] top-0 h-[400px] w-[600px] -translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.fuchsia.200/50%),transparent_60%)] blur-[32px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.fuchsia.500/20%),transparent_60%)]' />
              <div className='absolute left-1/2 top-0 h-[350px] w-[500px] -translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.pink.200/40%),transparent_55%)] blur-[24px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.pink.500/15%),transparent_55%)]' />
            </>
          ) : (
            // 其他页面光晕效果
            <>
              {/* 中心主光晕 */}
              <div className='absolute left-1/2 mx-auto w-full max-w-7xl -translate-x-1/2'>
                <div className='absolute top-0 h-[300px] w-full bg-[radial-gradient(ellipse_at_center_top,theme(colors.violet.200/60%),transparent_70%)] blur-[32px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.violet.500/25%),transparent_70%)]' />
                <div className='absolute left-[40%] top-0 h-[250px] w-[500px] -translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.blue.200/50%),transparent_70%)] blur-[24px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.blue.500/20%),transparent_70%)]' />
                <div className='absolute right-[40%] top-0 h-[250px] w-[500px] translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.fuchsia.200/50%),transparent_70%)] blur-[24px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.fuchsia.500/20%),transparent_70%)]' />
                <div className='absolute left-1/2 top-[50px] h-[200px] w-[400px] -translate-x-1/2 bg-[radial-gradient(ellipse_at_center_top,theme(colors.pink.200/40%),transparent_70%)] blur-[20px] dark:bg-[radial-gradient(ellipse_at_center_top,theme(colors.pink.500/15%),transparent_70%)]' />
              </div>
            </>
          )}
        </div>
      </div>

      {/* 底部光晕效果 */}
      <div className='absolute -bottom-6 left-0 right-0 -z-10 h-[447px] overflow-hidden'>
        <div className='absolute inset-0'>
          {/* 主要上升光晕 - 更深的底部 */}
          <div className='absolute inset-x-0 -bottom-20 h-[120%] bg-gradient-to-t from-violet-300/70 via-violet-200/15 to-transparent blur-[96px] dark:from-violet-500/40 dark:via-violet-500/5 dark:to-transparent' />

          {/* 右侧偏蓝色光晕 */}
          <div className='absolute -bottom-10 right-[20%] h-[110%] w-[30%] bg-gradient-to-t from-blue-300/60 via-blue-200/10 to-transparent blur-[80px] dark:from-blue-500/35 dark:via-blue-500/5 dark:to-transparent' />

          {/* 左侧偏紫色光晕 */}
          <div className='absolute -bottom-10 left-[20%] h-[110%] w-[30%] bg-gradient-to-t from-fuchsia-300/60 via-fuchsia-200/10 to-transparent blur-[80px] dark:from-fuchsia-500/35 dark:via-fuchsia-500/5 dark:to-transparent' />
        </div>
      </div>
    </>
  )
}

export default Layout
