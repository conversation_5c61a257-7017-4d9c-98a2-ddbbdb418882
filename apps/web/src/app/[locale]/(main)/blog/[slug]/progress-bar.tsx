'use client'

import { useEffect, useState } from 'react'

const ProgressBar = () => {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const handleScroll = () => {
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight - windowHeight
      const scrolled = window.scrollY
      const progress = scrolled / documentHeight
      setProgress(progress)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div
      className='bg-foreground fixed inset-x-0 top-0 z-50 h-1 origin-[0%]'
      style={{
        transform: `scaleX(${progress})`
      }}
    />
  )
}

export default ProgressBar
