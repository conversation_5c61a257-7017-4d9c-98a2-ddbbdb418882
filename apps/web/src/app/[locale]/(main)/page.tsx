import { i18n } from '@momo/i18n-web/config'
import { getTranslations, setRequestLocale } from '@momo/i18n-web/server'
import type { Metadata } from 'next'

import Advantages from '@/components/home/<USER>'
import FAQ from '@/components/home/<USER>'
import Hero from '@/components/home/<USER>'
import HowToUse from '@/components/home/<USER>'
import InputBoxWrapper from '@/components/home/<USER>'
import LatestArticles from '@/components/home/<USER>'
import StatsSection from '@/components/home/<USER>'
import TrustedUsers from '@/components/home/<USER>'
import { OrganizationSchema, ServiceSchema, WebSiteSchema } from '@/components/schema'
import {
  //   SITE_FACEBOOK_URL,
  SITE_GITHUB_URL,
  //   SITE_INSTAGRAM_URL,
  SITE_KEYWORDS,
  SITE_NAME,
  SITE_URL,
  SITE_X_URL
  //   SITE_YOUTUBE_URL
} from '@/lib/constants'
import { generateCanonicalUrl, generateHreflangAlternates } from '@/utils/generate-hreflang'
import { getLocalizedPath } from '@/utils/get-localized-path'

type PageProps = {
  params: Promise<{
    locale: string
  }>
  searchParams: Promise<Record<string, string | string[] | undefined>>
}

export const generateStaticParams = (): Array<{ locale: string }> => {
  return i18n.locales.map((locale) => ({ locale }))
}

export const generateMetadata = async (props: PageProps): Promise<Metadata> => {
  const { locale } = await props.params

  // Generate canonical URL and hreflang alternates
  const canonicalUrl = generateCanonicalUrl('', locale)
  const hreflangAlternates = generateHreflangAlternates('')

  return {
    alternates: {
      canonical: canonicalUrl,
      ...hreflangAlternates
    }
  }
}

// 设置动态渲染模式，禁用缓存
export const dynamic = 'force-dynamic'
// 确保页面不会被永久缓存
export const revalidate = 0

const Page = async (props: PageProps) => {
  try {
    const { locale } = await props.params
    setRequestLocale(locale)
    const t = await getTranslations('metadata')

    const url = `${SITE_URL}${getLocalizedPath({ slug: '', locale })}`
    const searchUrl = `${url}?q={search_term_string}`

    return (
      <>
        {/* Organization Schema */}
        <OrganizationSchema
          name={SITE_NAME}
          description={t('site-description')}
          url={SITE_URL}
          logo={`${SITE_URL}/images/og.png`}
          contactPoint={{
            contactType: 'Customer Service',
            email: '<EMAIL>'
          }}
          sameAs={[
            // SITE_FACEBOOK_URL,
            // SITE_INSTAGRAM_URL,
            SITE_X_URL,
            SITE_GITHUB_URL
            // SITE_YOUTUBE_URL
          ]}
        />

        {/* Enhanced WebSite Schema with Search */}
        <WebSiteSchema
          name={t('site-title')}
          description={t('site-description')}
          url={url}
          searchUrl={searchUrl}
          queryInput='required name=search_term_string'
          inLanguage={locale}
          author={{
            name: SITE_NAME,
            url: SITE_URL,
            sameAs: [
              //   SITE_FACEBOOK_URL,
              //   SITE_INSTAGRAM_URL,
              SITE_X_URL,
              SITE_GITHUB_URL
              //   SITE_YOUTUBE_URL
            ]
          }}
          keywords={SITE_KEYWORDS}
          dateCreated='2025-03-05'
          dateModified='2025-06-27T00:00:00.000Z'
        />

        {/* Service Schema */}
        <ServiceSchema
          name='Free Web Proxy Service'
          description='Professional site proxy and web proxy service for anonymous browsing and unblocking websites'
          provider={{
            name: SITE_NAME,
            url: SITE_URL
          }}
          serviceType='Web Proxy Service'
          areaServed='Worldwide'
          url={url}
          offers={{
            price: '0',
            priceCurrency: 'USD',
            availability: 'https://schema.org/InStock'
          }}
        />

        <div className='relative'>
          <Hero />
          <div className='relative z-10 -mt-24'>
            <InputBoxWrapper />
          </div>
          <div className='mt-12'>
            <Advantages />
            <HowToUse />
            <TrustedUsers />
            <LatestArticles />
            <FAQ />
            <StatsSection />
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error in Page component:', error)
    throw error // 让 Next.js 的错误边界处理这个错误
  }
}

export default Page
