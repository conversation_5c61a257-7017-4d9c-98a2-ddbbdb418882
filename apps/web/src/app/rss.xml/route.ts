import { i18n } from '@momo/i18n-web/config'
import { getTranslations } from '@momo/i18n-web/server'
import { allBlogPosts } from 'mdx/generated'
import { NextResponse } from 'next/server'
import RSS from 'rss'

import { SITE_NAME, SITE_URL } from '@/lib/constants'

export const GET = async () => {
  const t = await getTranslations({ locale: i18n.defaultLocale })

  const feed = new RSS({
    title: t('metadata.site-title'),
    description: t('metadata.site-description'),
    site_url: SITE_URL,
    feed_url: `${SITE_URL}/rss.xml`,
    language: 'en-US',
    image_url: `${SITE_URL}/images/og.png?v=8211`
  })

  const posts = allBlogPosts.filter((p) => p.language === i18n.defaultLocale)

  for (const post of posts) {
    const { title, summary, date, slug } = post

    feed.item({
      title,
      url: `${SITE_URL}/blog/${slug}`,
      date,
      description: summary,
      author: SITE_NAME
    })
  }

  return new NextResponse(feed.xml({ indent: true }), {
    headers: {
      'Content-Type': 'application/xml'
    }
  })
}
