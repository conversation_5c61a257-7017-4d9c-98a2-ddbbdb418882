import { env } from '@momo/env'
import { JWTPayload, jwtVerify, SignJWT } from 'jose'

import { createLogger } from '@/lib/logger'

const logger = createLogger('jwt')

// Get JWT secret from environment variable, use default value if not exists
const JWT_SECRET = env.JWT_SECRET

/**
 * 计算简单的校验和
 * @param str 要计算校验和的字符串
 * @returns 两位的校验和
 */
function calculateChecksum(str: string): string {
  let sum = 0
  for (let i = 0; i < str.length; i++) {
    sum += str.charCodeAt(i)
  }
  // 返回两位的校验和（00-99）
  return (sum % 100).toString().padStart(2, '0')
}

/**
 * 对JWT进行简单混淆，使其看起来不像标准JWT
 * @param token 原始JWT
 * @returns 混淆后的token
 */
function obfuscateToken(token: string): string {
  // 1. 将JWT进行base64编码
  const encoded = Buffer.from(token).toString('base64')

  // 2. 反转字符串
  const reversed = encoded.split('').reverse().join('')

  // 3. 移除填充字符
  const withoutPadding = reversed.replace(/=/g, '')

  // 4. 计算校验和并添加到末尾
  const checksum = calculateChecksum(withoutPadding)
  const result = withoutPadding + checksum

  logger.debug(`原始JWT格式: ${token}`)
  logger.debug(`Base64编码: ${encoded}`)
  logger.debug(`反转后: ${reversed}`)
  logger.debug(`移除填充后: ${withoutPadding}`)
  logger.debug(`校验和: ${checksum}`)
  logger.debug(`最终混淆格式: ${result}`)

  return result
}

/**
 * 还原被混淆的token
 * @param obfuscatedToken 混淆后的token
 * @returns 原始JWT
 */
function deobfuscateToken(obfuscatedToken: string): string {
  try {
    // 检查token是否为空
    if (!obfuscatedToken) {
      throw new Error('Empty token provided')
    }

    // 打印接收到的token
    logger.debug(`接收到的混淆token: ${obfuscatedToken}`)

    // 1. 提取校验和（最后两位）
    if (obfuscatedToken.length < 2) {
      throw new Error('Token too short')
    }

    const checksum = obfuscatedToken.slice(-2)
    const tokenWithoutChecksum = obfuscatedToken.slice(0, -2)

    // 2. 验证校验和
    const calculatedChecksum = calculateChecksum(tokenWithoutChecksum)
    if (checksum !== calculatedChecksum) {
      logger.warn(
        `校验和不匹配，可能是token被篡改 - 收到: ${checksum}, 计算: ${calculatedChecksum}`
      )
      // 继续尝试解码，但记录警告
    }

    // 3. 反转字符串
    const reversed = tokenWithoutChecksum.split('').reverse().join('')

    // 4. 添加可能缺失的base64填充
    const padding = '='.repeat((4 - (reversed.length % 4)) % 4)
    const base64Token = reversed + padding

    logger.debug(`校验和: ${checksum}`)
    logger.debug(`计算的校验和: ${calculatedChecksum}`)
    logger.debug(`移除校验和后: ${tokenWithoutChecksum}`)
    logger.debug(`反转后: ${reversed}`)
    logger.debug(`添加填充后: ${base64Token}`)

    // 5. base64解码得到原始JWT
    let originalToken = ''
    try {
      originalToken = Buffer.from(base64Token, 'base64').toString()
    } catch (e) {
      logger.error(`Base64解码失败: ${e}`)
      throw new Error('Failed to decode base64 token')
    }

    logger.debug(`解码后: ${originalToken}`)

    // 验证解码后的token是否看起来像JWT (包含两个点)
    if (originalToken.split('.').length !== 3) {
      // 如果解码后的字符串不像JWT，尝试检查原始token
      if (obfuscatedToken.split('.').length === 3) {
        logger.info(
          `Token appears to be an unmixed JWT, returning as is: ${obfuscatedToken.substring(0, 15)}...`
        )
        return obfuscatedToken
      }

      throw new Error('Decoded token does not appear to be a valid JWT')
    }

    logger.debug(`还原后的JWT: ${originalToken}`)

    return originalToken
  } catch (error) {
    logger.error('Token deobfuscation failed', error)
    return ''
  }
}

/**
 * Generate JWT token
 * @returns Generated JWT token
 */
export async function generateToken(): Promise<string> {
  // Create a new JWT
  const token = await new SignJWT({
    // Add custom claims
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt() // Set issued time
    .setExpirationTime('8h') // Set expiration time to 8 hour
    .sign(new TextEncoder().encode(JWT_SECRET))

  // 对生成的JWT进行混淆
  return obfuscateToken(token)
}

/**
 * Verify JWT token
 * @param token JWT token
 * @returns Verification result, returns decoded payload if successful, null if failed
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    // 检查token是否为空
    if (!token) {
      logger.error('Empty token provided')
      return null
    }

    // 先还原token
    const originalToken = deobfuscateToken(token)
    if (!originalToken) {
      logger.error('Failed to deobfuscate token')
      return null
    }

    // 打印原始token用于调试
    logger.debug(`准备验证的JWT: ${originalToken}`)

    const { payload } = await jwtVerify(originalToken, new TextEncoder().encode(JWT_SECRET))
    return payload
  } catch (error) {
    logger.error('JWT verification failed', error)
    return null
  }
}
