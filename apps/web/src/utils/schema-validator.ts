/**
 * Schema.org Validation Utilities
 *
 * This file contains utilities for validating Schema.org markup
 * and ensuring compliance with Google's structured data guidelines.
 */

import type { WithContext } from 'schema-dts'

/**
 * Validates a Schema.org object for required properties
 */
export const validateSchema = (
  schema: WithContext<any>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check for required @context and @type
  if (!schema['@context']) {
    errors.push('Missing required @context property')
  }

  if (!schema['@type']) {
    errors.push('Missing required @type property')
  }

  // Validate @context value
  if (schema['@context'] !== 'https://schema.org') {
    errors.push('Invalid @context value. Should be "https://schema.org"')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates Organization Schema
 */
export const validateOrganizationSchema = (schema: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const baseValidation = validateSchema(schema)
  errors.push(...baseValidation.errors)

  if (schema['@type'] !== 'Organization') {
    errors.push('Schema type should be "Organization"')
  }

  // Required properties for Organization
  const requiredProps = ['name', 'url']
  requiredProps.forEach((prop) => {
    if (!schema[prop]) {
      errors.push(`Missing required property: ${prop}`)
    }
  })

  // Validate logo if present
  if (schema.logo && typeof schema.logo === 'object') {
    if (!schema.logo['@type'] || schema.logo['@type'] !== 'ImageObject') {
      errors.push('Logo should be an ImageObject')
    }
    if (!schema.logo.url) {
      errors.push('Logo ImageObject missing url property')
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates WebSite Schema
 */
export const validateWebSiteSchema = (schema: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const baseValidation = validateSchema(schema)
  errors.push(...baseValidation.errors)

  if (schema['@type'] !== 'WebSite') {
    errors.push('Schema type should be "WebSite"')
  }

  // Required properties for WebSite
  const requiredProps = ['name', 'url']
  requiredProps.forEach((prop) => {
    if (!schema[prop]) {
      errors.push(`Missing required property: ${prop}`)
    }
  })

  // Validate SearchAction if present
  if (schema.potentialAction) {
    if (schema.potentialAction['@type'] !== 'SearchAction') {
      errors.push('potentialAction should be a SearchAction')
    }
    if (!schema.potentialAction.target) {
      errors.push('SearchAction missing target property')
    }
    if (!schema.potentialAction['query-input']) {
      errors.push('SearchAction missing query-input property')
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates FAQPage Schema
 */
export const validateFAQPageSchema = (schema: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const baseValidation = validateSchema(schema)
  errors.push(...baseValidation.errors)

  if (schema['@type'] !== 'FAQPage') {
    errors.push('Schema type should be "FAQPage"')
  }

  if (!schema.mainEntity || !Array.isArray(schema.mainEntity)) {
    errors.push('FAQPage must have mainEntity array')
  } else {
    schema.mainEntity.forEach((question: any, index: number) => {
      if (question['@type'] !== 'Question') {
        errors.push(`Question ${index + 1} should have @type "Question"`)
      }
      if (!question.name) {
        errors.push(`Question ${index + 1} missing name property`)
      }
      if (!question.acceptedAnswer) {
        errors.push(`Question ${index + 1} missing acceptedAnswer`)
      } else if (question.acceptedAnswer['@type'] !== 'Answer') {
        errors.push(`Question ${index + 1} acceptedAnswer should have @type "Answer"`)
      } else if (!question.acceptedAnswer.text) {
        errors.push(`Question ${index + 1} acceptedAnswer missing text`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates HowTo Schema
 */
export const validateHowToSchema = (schema: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const baseValidation = validateSchema(schema)
  errors.push(...baseValidation.errors)

  if (schema['@type'] !== 'HowTo') {
    errors.push('Schema type should be "HowTo"')
  }

  // Required properties for HowTo
  const requiredProps = ['name', 'step']
  requiredProps.forEach((prop) => {
    if (!schema[prop]) {
      errors.push(`Missing required property: ${prop}`)
    }
  })

  if (schema.step && Array.isArray(schema.step)) {
    schema.step.forEach((step: any, index: number) => {
      if (step['@type'] !== 'HowToStep') {
        errors.push(`Step ${index + 1} should have @type "HowToStep"`)
      }
      if (!step.text) {
        errors.push(`Step ${index + 1} missing text property`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates BreadcrumbList Schema
 */
export const validateBreadcrumbListSchema = (
  schema: any
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const baseValidation = validateSchema(schema)
  errors.push(...baseValidation.errors)

  if (schema['@type'] !== 'BreadcrumbList') {
    errors.push('Schema type should be "BreadcrumbList"')
  }

  if (!schema.itemListElement || !Array.isArray(schema.itemListElement)) {
    errors.push('BreadcrumbList must have itemListElement array')
  } else {
    schema.itemListElement.forEach((item: any, index: number) => {
      if (item['@type'] !== 'ListItem') {
        errors.push(`Breadcrumb item ${index + 1} should have @type "ListItem"`)
      }
      if (typeof item.position !== 'number') {
        errors.push(`Breadcrumb item ${index + 1} missing or invalid position`)
      }
      if (!item.name) {
        errors.push(`Breadcrumb item ${index + 1} missing name`)
      }
      if (!item.item) {
        errors.push(`Breadcrumb item ${index + 1} missing item URL`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validates Service Schema
 */
export const validateServiceSchema = (schema: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const baseValidation = validateSchema(schema)
  errors.push(...baseValidation.errors)

  if (schema['@type'] !== 'Service') {
    errors.push('Schema type should be "Service"')
  }

  // Required properties for Service
  const requiredProps = ['name', 'provider']
  requiredProps.forEach((prop) => {
    if (!schema[prop]) {
      errors.push(`Missing required property: ${prop}`)
    }
  })

  // Validate provider
  if (schema.provider && schema.provider['@type'] !== 'Organization') {
    errors.push('Service provider should be an Organization')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Comprehensive schema validation
 */
export const validateAllSchemas = (
  schemas: Array<{ type: string; schema: any }>
): {
  isValid: boolean
  results: Array<{ type: string; isValid: boolean; errors: string[] }>
} => {
  const results = schemas.map(({ type, schema }) => {
    let validation: { isValid: boolean; errors: string[] }

    switch (type) {
      case 'Organization':
        validation = validateOrganizationSchema(schema)
        break
      case 'WebSite':
        validation = validateWebSiteSchema(schema)
        break
      case 'FAQPage':
        validation = validateFAQPageSchema(schema)
        break
      case 'HowTo':
        validation = validateHowToSchema(schema)
        break
      case 'BreadcrumbList':
        validation = validateBreadcrumbListSchema(schema)
        break
      case 'Service':
        validation = validateServiceSchema(schema)
        break
      default:
        validation = validateSchema(schema)
    }

    return {
      type,
      ...validation
    }
  })

  return {
    isValid: results.every((result) => result.isValid),
    results
  }
}

/**
 * Logs validation results to console in development
 */
export const logValidationResults = (
  results: Array<{ type: string; isValid: boolean; errors: string[] }>
) => {
  if (process.env.NODE_ENV === 'development') {
    console.group('🔍 Schema.org Validation Results')

    results.forEach(({ type, isValid, errors }) => {
      if (isValid) {
        console.log(`✅ ${type} Schema: Valid`)
      } else {
        console.group(`❌ ${type} Schema: Invalid`)
        errors.forEach((error) => console.error(`  • ${error}`))
        console.groupEnd()
      }
    })

    console.groupEnd()
  }
}
