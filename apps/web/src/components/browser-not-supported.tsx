'use client'

import { useTranslations } from '@momo/i18n-web/client'
import { Button } from '@momo/ui'
import { ArrowLeft, ShieldAlert } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

const BrowserNotSupported = () => {
  const t = useTranslations('browser-not-supported')

  // 获取首页路径
  const homePath = '/'

  return (
    <div className='relative mx-auto mb-24 mt-20 max-w-5xl'>
      <div className='relative space-y-12 p-8'>
        {/* Alert Icon */}
        <div className='flex justify-center'>
          <div className='rounded-full bg-amber-100/60 p-8 dark:bg-amber-900/30'>
            <ShieldAlert className='h-16 w-16 text-amber-500 dark:text-amber-400' />
          </div>
        </div>

        {/* Content */}
        <div className='space-y-8 text-center'>
          <h2 className='text-2xl font-semibold tracking-tight sm:text-3xl'>{t('heading')}</h2>

          <p className='text-muted-foreground mx-auto max-w-3xl'>{t('explanation')}</p>

          {/* Browser Options */}
          <div className='bg-background/50 mx-auto max-w-4xl rounded-2xl p-8 backdrop-blur-sm'>
            <h3 className='mb-6 font-medium'>{t('supported-browsers')}</h3>

            <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
              {/* Chrome */}
              <a
                href='https://www.google.com/chrome/'
                target='_blank'
                rel='noopener noreferrer'
                className='hover:bg-background/80 group flex flex-col items-center gap-4 rounded-xl p-6 transition-all duration-300 hover:shadow-md hover:shadow-blue-500/5'
                title='Download Google Chrome Browser'
              >
                <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                  <Image
                    src='/images/icon/chrome.svg'
                    alt='Chrome'
                    fill
                    className='object-contain'
                  />
                </div>
                <div className='text-center'>
                  <div className='font-medium'>{t('chrome')}</div>
                  <div className='text-muted-foreground text-sm'>{t('chrome-version')}</div>
                </div>
              </a>

              {/* Firefox */}
              <a
                href='https://www.mozilla.org/firefox/'
                target='_blank'
                rel='noopener noreferrer'
                className='hover:bg-background/80 group flex flex-col items-center gap-4 rounded-xl p-6 transition-all duration-300 hover:shadow-md hover:shadow-orange-500/5'
                title='Download Mozilla Firefox Browser'
              >
                <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                  <Image
                    src='/images/icon/firefox.svg'
                    alt='Firefox'
                    fill
                    className='object-contain'
                  />
                </div>
                <div className='text-center'>
                  <div className='font-medium'>{t('firefox')}</div>
                  <div className='text-muted-foreground text-sm'>{t('firefox-version')}</div>
                </div>
              </a>

              {/* Safari */}
              <a
                href='https://www.apple.com/safari/'
                target='_blank'
                rel='noopener noreferrer'
                className='hover:bg-background/80 group flex flex-col items-center gap-4 rounded-xl p-6 transition-all duration-300 hover:shadow-md hover:shadow-blue-400/5'
                title='Download Apple Safari Browser'
              >
                <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                  <Image
                    src='/images/icon/safari.svg'
                    alt='Safari'
                    fill
                    className='object-contain'
                  />
                </div>
                <div className='text-center'>
                  <div className='font-medium'>{t('safari')}</div>
                  <div className='text-muted-foreground text-sm'>{t('safari-version')}</div>
                </div>
              </a>

              {/* Edge */}
              <a
                href='https://www.microsoft.com/edge'
                target='_blank'
                rel='noopener noreferrer'
                className='hover:bg-background/80 group flex flex-col items-center gap-4 rounded-xl p-6 transition-all duration-300 hover:shadow-md hover:shadow-teal-500/5'
                title='Download Microsoft Edge Browser'
              >
                <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                  <Image src='/images/icon/edge.svg' alt='Edge' fill className='object-contain' />
                </div>
                <div className='text-center'>
                  <div className='font-medium'>{t('edge')}</div>
                  <div className='text-muted-foreground text-sm'>{t('edge-version')}</div>
                </div>
              </a>
            </div>
          </div>

          {/* Back to Home */}
          <div>
            <Link href={homePath} title={t('back-to-home')}>
              <Button variant='outline' className='gap-2 rounded-xl px-5 py-2 shadow-sm'>
                <ArrowLeft className='h-4 w-4' />
                {t('back-to-home')}
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Background decoration */}
      <div className='absolute inset-0 -z-10'>
        <div className='absolute inset-0 bg-gradient-to-r from-amber-100/20 via-orange-100/15 to-red-100/20 blur-[120px] dark:from-amber-900/15 dark:via-orange-900/10 dark:to-red-900/15' />
      </div>
    </div>
  )
}

export default BrowserNotSupported
