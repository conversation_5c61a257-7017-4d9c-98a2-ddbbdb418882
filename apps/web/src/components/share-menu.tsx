'use client'

import { SiFacebook, SiLinkedin, SiPinterest, SiReddit, SiX } from '@icons-pack/react-simple-icons'
import { useTranslations } from '@momo/i18n-web/client'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@momo/ui'
import { cn } from '@momo/utils'
import { Share2 } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'

import { SITE_URL } from '@/lib/constants'

interface ShareMenuProps {
  className?: string
}

const ShareMenu = ({ className }: ShareMenuProps) => {
  const t = useTranslations()
  const pathname = usePathname()
  const [currentTitle, setCurrentTitle] = useState('')
  const [currentDescription, setCurrentDescription] = useState('')

  // 获取当前页面的 OpenGraph 标题和描述
  useEffect(() => {
    const getMetaContent = () => {
      // 优先获取 og:title，如果没有则使用 document.title
      const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content')
      const pageTitle = ogTitle || document.title || t('metadata.site-title')

      // 优先获取 og:description，如果没有则使用默认描述
      const ogDescription = document
        .querySelector('meta[property="og:description"]')
        ?.getAttribute('content')
      const pageDescription = ogDescription || t('metadata.site-description')

      setCurrentTitle(pageTitle)
      setCurrentDescription(pageDescription)
    }

    // 初始设置
    getMetaContent()

    // 监听页面变化（路由变化时重新获取）
    const observer = new MutationObserver(() => {
      getMetaContent()
    })

    observer.observe(document.head, {
      childList: true,
      subtree: true
    })

    return () => observer.disconnect()
  }, [t, pathname])

  // 获取当前页面完整URL
  const currentUrl = `${SITE_URL}${pathname}`

  // URL编码处理
  const encodedUrl = encodeURIComponent(currentUrl)
  const encodedTitle = encodeURIComponent(currentTitle)
  const encodedDesc = encodeURIComponent(currentDescription)

  // 分享处理函数
  const handleShare = useCallback(async () => {
    if (navigator.share) {
      await navigator.share({
        title: currentTitle,
        text: currentDescription,
        url: currentUrl
      })
    }
  }, [currentUrl, currentDescription, currentTitle])

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          className={cn('size-9 p-0', className)}
          aria-label={t('share.button-label')}
        >
          <Share2 className='h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        {typeof navigator !== 'undefined' && 'share' in navigator && (
          <DropdownMenuItem onClick={handleShare}>
            <Share2 className='mr-2 h-4 w-4' />
            {t('share.native')}
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          className='cursor-pointer'
          onClick={() => {
            window.open(
              `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`,
              '_blank'
            )
          }}
        >
          <SiX className='mr-2 h-4 w-4' />X
        </DropdownMenuItem>
        <DropdownMenuItem
          className='cursor-pointer'
          onClick={() => {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank')
          }}
        >
          <SiFacebook className='mr-2 h-4 w-4' />
          Facebook
        </DropdownMenuItem>
        <DropdownMenuItem
          className='cursor-pointer'
          onClick={() => {
            window.open(
              `https://www.linkedin.com/shareArticle?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDesc}`,
              '_blank'
            )
          }}
        >
          <SiLinkedin className='mr-2 h-4 w-4' />
          LinkedIn
        </DropdownMenuItem>
        <DropdownMenuItem
          className='cursor-pointer'
          onClick={() => {
            window.open(
              `https://reddit.com/submit/?url=${encodedUrl}&resubmit=true&title=${encodedTitle}`,
              '_blank'
            )
          }}
        >
          <SiReddit className='mr-2 h-4 w-4' />
          Reddit
        </DropdownMenuItem>
        <DropdownMenuItem
          className='cursor-pointer'
          onClick={() => {
            window.open(
              `http://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedDesc}`,
              '_blank'
            )
          }}
        >
          <SiPinterest className='mr-2 h-4 w-4' />
          Pinterest
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default ShareMenu
