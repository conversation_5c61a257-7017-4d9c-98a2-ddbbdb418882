'use client'

import { Separator } from '@momo/ui'

type PageTitleProps = {
  title: string
  description: string
}

const PageTitle = (props: PageTitleProps) => {
  const { title, description } = props

  return (
    <div className='mb-16 mt-12 sm:mb-24 sm:mt-28'>
      <h1 className='my-4 text-4xl font-bold md:text-5xl'>{title}</h1>
      <h2 className='text-muted-foreground mb-8'>{description}</h2>
      <Separator className='absolute inset-x-0 translate-y-2 sm:translate-y-6' />
    </div>
  )
}

export default PageTitle
