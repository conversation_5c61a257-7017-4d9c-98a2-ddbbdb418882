'use client'

import { useTranslations } from '@momo/i18n-web/client'

const StatItem = ({ value, label }: { value: string; label: string }) => (
  <div className='flex flex-col items-center'>
    <div className='bg-gradient-to-r from-blue-600 to-blue-500 bg-clip-text text-3xl font-bold text-transparent dark:from-blue-400 dark:to-blue-300'>
      {value}
    </div>
    <div className='mt-2 text-sm text-zinc-600 dark:text-zinc-300'>{label}</div>
  </div>
)

const StatsSection = () => {
  const t = useTranslations('homepage')

  // 统计数据
  const stats = [
    { value: '99.9%', label: t('stats-section.reliability') },
    { value: '5M+', label: t('stats-section.trusted-users') },
    { value: '50M+', label: t('stats-section.requests-monthly') },
    { value: '100+', label: t('stats-section.countries-served') }
  ]

  return (
    <div>
      <div className='container mx-auto px-4'>
        <div className='mb-16 mt-28 grid grid-cols-2 gap-8 md:grid-cols-4'>
          {stats.map((stat, index) => (
            <StatItem key={index} value={stat.value} label={stat.label} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default StatsSection
