'use client'

import { useTranslations } from '@momo/i18n-web/client'
import { BlurImage } from '@momo/ui'
import { Quote } from 'lucide-react'

// 使用字符串路径代替导入
const avatar1 = '/images/avatars/avatar-1.png'
const avatar2 = '/images/avatars/avatar-2.png'
const avatar3 = '/images/avatars/avatar-3.png'

const TestimonialCard = ({
  content,
  author,
  avatar
}: {
  content: string
  author: string
  avatar: any
}) => {
  return (
    <div className='flex flex-col rounded-2xl border border-gray-200/30 bg-gradient-to-b from-white/90 via-white/95 to-white/90 p-6 shadow-md transition-all duration-300 hover:-translate-y-2 hover:border-blue-200/50 hover:shadow-lg dark:border-gray-800/30 dark:from-zinc-800/90 dark:via-zinc-800/80 dark:to-zinc-800/70 dark:shadow-md dark:hover:border-blue-800/30 dark:hover:shadow-lg'>
      <div className='mb-4 text-blue-400/80 transition-transform duration-300 group-hover:scale-110 dark:text-blue-400/70'>
        <Quote className='text-2xl' size={24} />
      </div>
      <p className='mb-4 text-zinc-700 dark:text-zinc-300'>{content}</p>
      <div className='mt-auto flex items-center'>
        <div className='mr-3 overflow-hidden rounded-full border border-gray-100/80 shadow-sm dark:border-gray-700/50'>
          <BlurImage src={avatar} alt={author} width={40} height={40} />
        </div>
        <span className='text-sm font-medium'>{author}</span>
      </div>
    </div>
  )
}

const TrustedUsers = () => {
  const t = useTranslations('homepage')

  // 头像映射
  const avatars = [avatar1, avatar2, avatar3]

  // 外国用户评论 - 硬编码英文，不翻译
  const testimonials = [
    {
      content:
        'ProxyOrb helped me access research materials and academic resources that were region-restricted. The speed is incredible and setup was effortless!',
      author: 'David Wilson, PhD Student'
    },
    {
      content:
        "As a cybersecurity professional, I appreciate ProxyOrb's military-grade encryption. It's my go-to tool for secure browsing when I'm traveling.",
      author: 'Michael Reynolds, IT Security Consultant'
    },
    {
      content:
        "Working remotely requires reliable tools. ProxyOrb helps me connect to my company's resources from anywhere in the world without complicated setups.",
      author: 'Sarah Johnson, Digital Nomad'
    }
  ]

  return (
    <div className='relative bg-gradient-to-b from-zinc-50 to-zinc-100/50 py-16 dark:from-zinc-900/50 dark:to-zinc-900/30'>
      <div className='container mx-auto px-4'>
        <div className='mb-12 text-center'>
          <h2 className='mb-3 bg-gradient-to-r from-black to-black/70 bg-clip-text text-3xl font-medium text-transparent sm:text-4xl dark:from-zinc-100 dark:to-zinc-400'>
            {t('trusted-users.title')}
          </h2>
          <p className='mx-auto max-w-2xl text-lg text-zinc-600 dark:text-zinc-300'>
            {t('trusted-users.subtitle')}
          </p>
        </div>

        {/* 用户评价 */}
        <div className='grid grid-cols-1 gap-8 md:grid-cols-3'>
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              content={testimonial.content}
              author={testimonial.author}
              avatar={avatars[index]}
            />
          ))}
        </div>
      </div>

      {/* 背景装饰 */}
      <div className='absolute inset-0 -z-10 overflow-hidden'>
        <div className='absolute -left-40 top-20 h-64 w-64 rounded-full bg-blue-400/5 blur-3xl dark:bg-blue-700/5'></div>
        <div className='absolute -right-20 bottom-10 h-72 w-72 rounded-full bg-purple-400/5 blur-3xl dark:bg-purple-700/5'></div>
      </div>
    </div>
  )
}

export default TrustedUsers
