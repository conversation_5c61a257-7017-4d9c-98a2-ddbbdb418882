'use client'

import { useTranslations } from '@momo/i18n-web/client'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@momo/ui'

import { FAQPageSchema } from '@/components/schema'

const FAQ = () => {
  const t = useTranslations('homepage.faq')

  const faqs = [
    {
      question: t('what-is-proxy'),
      answer: t('what-is-proxy-answer')
    },
    {
      question: t('how-it-works'),
      answer: t('how-it-works-answer')
    },
    {
      question: t('why-need'),
      answer: t('why-need-answer')
    },
    {
      question: t('is-free'),
      answer: t('is-free-answer')
    },
    {
      question: t('is-safe'),
      answer: t('is-safe-answer')
    },
    {
      question: t('software-required'),
      answer: t('software-required-answer')
    },
    {
      question: t('video-support'),
      answer: t('video-support-answer')
    },
    {
      question: t('using-school'),
      answer: t('using-school-answer')
    },
    {
      question: t('advantages'),
      answer: t('advantages-answer')
    },
    {
      question: t('vpn-difference'),
      answer: t('vpn-difference-answer')
    },
    {
      question: t('netflix-youtube'),
      answer: t('netflix-youtube-answer')
    },
    {
      question: t('geo-access'),
      answer: t('geo-access-answer')
    },
    {
      question: t('foreign-news'),
      answer: t('foreign-news-answer')
    },
    {
      question: t('market-research'),
      answer: t('market-research-answer')
    }
  ]

  return (
    <>
      {/* FAQ Page Schema */}
      <FAQPageSchema faqs={faqs} />

      <div className='w-full px-4 py-16'>
        <div className='mx-auto max-w-4xl'>
          <div className='mb-12 text-center'>
            <h2 className='mb-3 bg-gradient-to-r from-black to-black/70 bg-clip-text text-3xl font-medium text-transparent sm:text-4xl dark:from-zinc-100 dark:to-zinc-400'>
              {t('title')}
            </h2>
          </div>

          <Accordion type='single' collapsible>
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className='text-left'>{faq.question}</AccordionTrigger>
                <AccordionContent className='text-muted-foreground'>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </>
  )
}

export default FAQ
