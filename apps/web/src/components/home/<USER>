'use client'

import { useTranslations } from '@momo/i18n-web/client'
import { Lock, Rocket, Shield, ThumbsUp } from 'lucide-react'

const AdvantageItem = ({
  icon,
  title,
  description
}: {
  icon: React.ReactNode
  title: string
  description: string
}) => {
  return (
    <div className='flex flex-col items-center rounded-2xl border border-gray-200/30 bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md dark:border-gray-800/30 dark:bg-zinc-800/50'>
      <div className='mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-400/90 to-indigo-400/90 text-3xl text-white shadow-sm dark:from-blue-500/90 dark:to-indigo-500/90'>
        {icon}
      </div>
      <h3 className='mb-3 mt-2 text-xl font-semibold'>{title}</h3>
      <p className='text-center text-zinc-600 dark:text-zinc-300'>{description}</p>
    </div>
  )
}

const Advantages = () => {
  const t = useTranslations('homepage.advantages')

  return (
    <div className='py-16'>
      <div className='container mx-auto px-4'>
        <div className='mb-12 text-center'>
          <h2 className='mb-3 bg-gradient-to-r from-black to-black/70 bg-clip-text text-3xl font-medium text-transparent sm:text-4xl dark:from-zinc-100 dark:to-zinc-400'>
            {t('title')}
          </h2>
          <p className='mx-auto max-w-2xl text-lg text-zinc-600 dark:text-zinc-300'>
            {t('subtitle')}
          </p>
        </div>

        <div className='grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4'>
          <AdvantageItem
            icon={<ThumbsUp size={32} />}
            title={t('free.title')}
            description={t('free.description')}
          />
          <AdvantageItem
            icon={<Rocket size={32} />}
            title={t('easy.title')}
            description={t('easy.description')}
          />
          <AdvantageItem
            icon={<Shield size={32} />}
            title={t('secure.title')}
            description={t('secure.description')}
          />
          <AdvantageItem
            icon={<Lock size={32} />}
            title={t('speed.title')}
            description={t('speed.description')}
          />
        </div>
      </div>
    </div>
  )
}

export default Advantages
