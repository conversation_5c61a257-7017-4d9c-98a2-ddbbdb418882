'use client'

import { useTranslations } from '@momo/i18n-web/client'

const Hero = () => {
  const t = useTranslations('homepage.hero')

  return (
    <div className='relative pb-28 pt-20 sm:pb-40 sm:pt-32'>
      <div className='mx-auto max-w-5xl px-6'>
        <div className='relative'>
          {/* 主标题区域 */}
          <div className='relative z-10'>
            <div className='mx-auto text-center'>
              <h1 className='relative inline-block'>
                <span className='block text-[2.5rem] font-light tracking-tight sm:text-6xl'>
                  <span className='inline-block bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent dark:from-white dark:via-gray-100 dark:to-white'>
                    {t('title-top')}&nbsp;
                  </span>
                  <br className='hidden sm:block' />
                  <span className='relative inline-block bg-gradient-to-r from-blue-600 to-violet-600 bg-clip-text font-normal text-transparent dark:from-blue-400 dark:to-violet-400'>
                    {t('title-middle-left')}
                    <div className='absolute -inset-x-4 -bottom-2 h-px bg-gradient-to-r from-blue-500/0 via-blue-500/40 to-blue-500/0' />
                  </span>{' '}
                  <span className='inline-block bg-gradient-to-r from-gray-800 to-gray-900 bg-clip-text font-light text-transparent dark:from-gray-100 dark:to-white'>
                    {t('title-middle-right')}
                  </span>
                </span>
              </h1>

              <p className='mx-auto mt-8 max-w-2xl text-lg leading-relaxed text-gray-600 dark:text-gray-300'>
                {t('subtitle')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Hero
