'use client'

import { useTranslations } from '@momo/i18n-web/client'

import { HowToSchema } from '@/components/schema'
import { SITE_URL } from '@/lib/constants'

const HowToUse = () => {
  const t = useTranslations('homepage.howto')
  const tInput = useTranslations('homepage.input')

  const buttonText = tInput('button')

  const steps = [
    {
      name: t('step1-title'),
      text: t('step1-description')
    },
    {
      name: t('step2-title', { buttonText }),
      text: t('step2-description', { buttonText })
    },
    {
      name: t('step3-title'),
      text: t('step3-description')
    },
    {
      name: t('step4-title'),
      text: t('step4-description')
    }
  ]

  return (
    <>
      {/* HowTo Schema */}
      <HowToSchema
        name={t('title')}
        description={t('description')}
        steps={steps}
        totalTime='PT2M'
        estimatedCost='0'
        supply={[t('supply-browser'), t('supply-internet')]}
        url={SITE_URL}
      />

      <div className='w-full px-4 py-16'>
        <div className='mx-auto max-w-4xl'>
          <div className='mb-12 text-center'>
            <h2 className='mb-3 bg-gradient-to-r from-black to-black/70 bg-clip-text text-3xl font-medium text-transparent sm:text-4xl dark:from-zinc-100 dark:to-zinc-400'>
              {t('title')}
            </h2>
            <p className='text-muted-foreground text-lg'>{t('description')}</p>
          </div>

          <div className='grid gap-8 md:grid-cols-2'>
            {steps.map((step, index) => (
              <div key={index} className='bg-card relative rounded-lg border p-6 shadow-sm'>
                <div className='mb-4 flex items-center gap-3'>
                  <div className='bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full text-sm font-semibold'>
                    {index + 1}
                  </div>
                  <h3 className='text-lg font-semibold'>{step.name}</h3>
                </div>
                <p className='text-muted-foreground'>{step.text}</p>
              </div>
            ))}
          </div>

          <div className='mt-12 text-center'>
            <div className='bg-muted/50 rounded-lg p-6'>
              <h3 className='mb-2 text-lg font-semibold'>{t('requirements-title')}</h3>
              <div className='text-muted-foreground flex flex-wrap justify-center gap-4 text-sm'>
                <span className='flex items-center gap-2'>
                  <span className='h-2 w-2 rounded-full bg-green-500'></span>
                  {t('supply-browser')}
                </span>
                <span className='flex items-center gap-2'>
                  <span className='h-2 w-2 rounded-full bg-green-500'></span>
                  {t('supply-internet')}
                </span>
                <span className='flex items-center gap-2'>
                  <span className='h-2 w-2 rounded-full bg-green-500'></span>
                  {t('no-registration')}
                </span>
                <span className='flex items-center gap-2'>
                  <span className='h-2 w-2 rounded-full bg-green-500'></span>
                  {t('no-download')}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default HowToUse
