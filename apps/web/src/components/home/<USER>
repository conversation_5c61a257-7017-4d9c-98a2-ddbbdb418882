'use client'

import { useTranslations } from '@momo/i18n-web/client'
import { Button, Input } from '@momo/ui'
import { ArrowRight, Globe2, Loader2, ShieldCheck } from 'lucide-react'
import Link from 'next/link'
import { usePathname, useSearchParams } from 'next/navigation'
import { FormEvent, useEffect, useRef, useState } from 'react'

interface InputBoxProps {
  initialToken: string // 确保 token 是必需的
}

const InputBox = ({ initialToken }: InputBoxProps) => {
  const t = useTranslations('homepage.input')
  const [input, setInput] = useState('')
  const [loading, setLoading] = useState(false)
  const [token] = useState(initialToken)
  const formRef = useRef<HTMLFormElement>(null)
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const locale = pathname.split('/')[1] || 'en'

  // 自动获取URL中的q参数并设置到输入框
  useEffect(() => {
    const qParam = searchParams.get('q')
    if (qParam) {
      // 解码URL参数
      const decodedValue = decodeURIComponent(qParam)
      setInput(decodedValue)
    }
  }, [searchParams])

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    // 检查Service Worker支持
    if (!('serviceWorker' in navigator)) {
      // 如果不支持，跳转到不支持页面
      window.location.href = `/${locale}/browser-not-supported`
      return
    }

    // Validate input
    if (!input) {
      console.warn('Please enter a value')
      return
    }

    if (!token) {
      console.warn('Security token missing, please refresh the page')
      return
    }

    setLoading(true)

    try {
      // Submit request
      const formData = new FormData()
      formData.append('input', input)

      // 获取region参数并添加到请求中
      const regionParam = searchParams.get('r')
      if (regionParam) {
        formData.append('r', regionParam)
      }

      const response = await fetch('/api/servers', {
        method: 'POST',
        headers: {
          'x-stats-id': token
        },
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Request failed')
      }

      // 直接获取响应的文本内容并创建一个新的文档来提取URL
      const text = await response.text()
      const parser = new DOMParser()
      const doc = parser.parseFromString(text, 'text/html')
      const metaRefresh = doc.querySelector('meta[http-equiv="x-refresh"]')
      const url = metaRefresh?.getAttribute('content')

      if (url) {
        window.location.replace(url)
      } else {
        throw new Error('Invalid response format')
      }
    } catch (err) {
      console.error('Request failed:', err instanceof Error ? err.message : 'Unknown error')
      setLoading(false)
    }
  }

  return (
    <div className='relative mx-auto w-full max-w-4xl'>
      <div className='relative space-y-8 rounded-3xl border border-gray-200/30 bg-gradient-to-b from-white/90 via-white/80 to-white/70 p-8 shadow-[0_8px_40px_-12px_rgba(0,0,0,0.08)] backdrop-blur-xl dark:border-gray-800/30 dark:from-gray-900/90 dark:via-gray-900/80 dark:to-gray-900/70 dark:shadow-[0_8px_40px_-12px_rgba(0,0,0,0.25)]'>
        <form ref={formRef} onSubmit={handleSubmit} className='flex flex-col gap-4 sm:flex-row'>
          <div className='relative flex-1'>
            <div className='pointer-events-none absolute left-3 top-1/2 -translate-y-1/2'>
              <Globe2 className='h-5 w-5 text-blue-500/90 dark:text-blue-400/90' />
            </div>
            <Input
              name='input'
              className='h-14 w-full rounded-2xl border border-gray-200/90 bg-white/85 pl-10 text-lg tracking-wide shadow-[inset_0_2px_4px_rgba(0,0,0,0.02)] transition-[background,box-shadow,transform] duration-300 placeholder:text-gray-500/90 hover:border-gray-300/90 hover:bg-white/95 hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.03),0_1px_8px_-1px_rgba(0,0,0,0.02)] focus:border-blue-400/40 focus:bg-white focus:shadow-[inset_0_2px_4px_rgba(71,129,241,0.03),0_2px_12px_-2px_rgba(71,129,241,0.08)] focus:ring-2 focus:ring-blue-500/20 dark:border-gray-600/50 dark:bg-gray-800/85 dark:shadow-[inset_0_2px_4px_rgba(0,0,0,0.06)] dark:hover:border-gray-500/50 dark:hover:bg-gray-800/95 dark:hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.08),0_1px_8px_-1px_rgba(0,0,0,0.08)] dark:focus:border-blue-500/40 dark:focus:bg-gray-800 dark:focus:shadow-[inset_0_2px_4px_rgba(71,129,241,0.04),0_2px_12px_-2px_rgba(71,129,241,0.12)] dark:focus:ring-blue-500/20'
              placeholder={t('placeholder')}
              value={input}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
            />
          </div>
          <Button
            type='submit'
            size='lg'
            className='group h-14 rounded-2xl bg-gradient-to-r from-blue-600/95 to-blue-500/95 px-8 text-base font-medium tracking-wide transition-all duration-300 hover:from-blue-500 hover:to-blue-600 hover:opacity-95 hover:shadow-lg hover:shadow-blue-500/30 dark:from-blue-600/95 dark:to-blue-500/95 dark:hover:from-blue-500 dark:hover:to-blue-600 dark:hover:shadow-blue-500/20'
            disabled={loading || !token}
          >
            <div className='relative flex w-full items-center justify-center'>
              {loading && (
                <div className='absolute inset-0 flex items-center justify-center'>
                  <Loader2 className='h-5 w-5 animate-spin' />
                </div>
              )}
              <span className={loading ? 'opacity-0' : 'opacity-100'}>
                {t('button')}
                <ArrowRight className='ml-2 inline-block h-5 w-5 transition-transform duration-300 group-hover:translate-x-1' />
              </span>
            </div>
          </Button>
        </form>

        {/* Security notice */}
        <div className='flex flex-col-reverse gap-3 text-sm text-gray-500/90 sm:flex-row sm:items-center sm:justify-between dark:text-gray-400/90'>
          <div className='order-1 flex items-center gap-2.5 sm:order-none'>
            <ShieldCheck className='h-4 w-4 text-green-500/90' />
            <span className='tracking-wide'>{t('secure-browsing')}</span>
          </div>
          <p className='tracking-wide'>
            {t.rich('terms-notice', {
              link: (chunks) => (
                <Link
                  href='/terms-of-service'
                  className='underline-offset-4 hover:text-gray-900 hover:underline dark:hover:text-gray-100'
                  title='View Terms of Service'
                >
                  {chunks}
                </Link>
              )
            })}
          </p>
        </div>
      </div>

      {/* Background decoration */}
      <div className='absolute inset-0 -z-10'>
        <div className='absolute inset-0 bg-gradient-to-r from-blue-100/30 via-violet-100/30 to-purple-100/30 blur-[120px] dark:from-blue-900/20 dark:via-violet-900/20 dark:to-purple-900/20' />
        <div className='absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/80 dark:to-gray-950/80' />
      </div>
    </div>
  )
}

export default InputBox
