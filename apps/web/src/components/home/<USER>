'use client'

import { useLocale, useTranslations } from '@momo/i18n-web/client'
import { BlurImage, buttonVariants } from '@momo/ui'
import { cn } from '@momo/utils'
import { allBlogPosts, type BlogPost } from 'mdx/generated'

import Link from '../link'

const LatestArticles = () => {
  const t = useTranslations()
  const locale = useLocale()
  const filteredPosts = allBlogPosts
    .toSorted((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })
    .filter((post) => post.language === locale)
    .slice(0, 4)

  return (
    <div className='my-24'>
      <div className='mb-12 text-center'>
        <h2 className='mb-3 bg-gradient-to-r from-black to-black/70 bg-clip-text text-3xl font-medium text-transparent sm:text-4xl dark:from-zinc-100 dark:to-zinc-400'>
          {t('homepage.latest-articles.title')}
        </h2>
      </div>
      <div className='mt-12 grid gap-4 md:grid-cols-2'>
        {filteredPosts.map((post) => (
          <Card key={post.slug} post={post} />
        ))}
      </div>
      <div className='my-8 flex items-center justify-center'>
        <Link
          href='/blog'
          className={cn(
            buttonVariants({
              variant: 'outline'
            }),
            'rounded-xl'
          )}
          title='View All Blog Posts'
        >
          {t('homepage.latest-articles.more')}
        </Link>
      </div>
    </div>
  )
}

type CardProps = {
  post: BlogPost
}

const Card = (props: CardProps) => {
  const { post } = props
  const { slug, title, summary } = post

  return (
    <Link
      href={`/blog/${slug}`}
      className='shadow-feature-card dark:shadow-feature-card-dark group relative rounded-xl p-2'
      title={title}
    >
      <BlurImage
        width={1200}
        height={630}
        src={`/images/blog/${slug}/cover.png`}
        alt={title}
        className='rounded-lg'
      />
      <div className='flex flex-col px-2 py-4 transition-transform ease-out group-hover:translate-x-0.5'>
        <h3 className='text-2xl font-semibold'>{title}</h3>
        <p className='text-muted-foreground mt-2'>{summary}</p>
      </div>
    </Link>
  )
}

export default LatestArticles
