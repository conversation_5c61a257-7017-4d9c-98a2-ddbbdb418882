import { Logo as <PERSON>o<PERSON><PERSON> } from '@momo/ui'

const Logo = () => {
  return (
    <div className='flex flex-col gap-4 md:flex-row'>
      <div className='flex h-52 w-full items-center justify-center rounded-lg bg-white'>
        <MomoLogo className='text-black' width={48} height={48} />
      </div>
      <div className='flex h-52 w-full items-center justify-center rounded-lg bg-black'>
        <MomoLogo className='text-white' width={48} height={48} />
      </div>
    </div>
  )
}

export default Logo
