import { useTranslations } from '@momo/i18n-web/client'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@momo/ui'
import { CheckIcon, MonitorIcon, MoonIcon, SunIcon } from 'lucide-react'
import { useTheme } from 'next-themes'

const ThemeSwitcher = () => {
  const { setTheme, theme } = useTheme()
  const t = useTranslations()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='size-9 p-0'
          aria-label={t('theme-toggle.toggle-theme')}
          data-testid='theme-toggle'
        >
          <SunIcon className='size-4 dark:hidden' />
          <MoonIcon className='hidden size-4 dark:block' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem
          className='flex items-center justify-between gap-2'
          onClick={() => {
            setTheme('light')
          }}
          data-testid='theme-light-button'
        >
          <div className='flex items-center gap-2'>
            <SunIcon className='size-[18px]' />
            <span>{t('theme-toggle.options.light')}</span>
          </div>
          {theme === 'light' && <CheckIcon className='size-4' />}
        </DropdownMenuItem>
        <DropdownMenuItem
          className='flex items-center justify-between gap-2'
          onClick={() => {
            setTheme('dark')
          }}
          data-testid='theme-dark-button'
        >
          <div className='flex items-center gap-2'>
            <MoonIcon className='size-[18px]' />
            <span>{t('theme-toggle.options.dark')}</span>
          </div>
          {theme === 'dark' && <CheckIcon className='size-4' />}
        </DropdownMenuItem>
        <DropdownMenuItem
          className='flex items-center justify-between gap-2'
          onClick={() => {
            setTheme('system')
          }}
          data-testid='theme-system-button'
        >
          <div className='flex items-center gap-2'>
            <MonitorIcon className='size-[18px]' />
            <span>{t('theme-toggle.options.system')}</span>
          </div>
          {theme === 'system' && <CheckIcon className='size-4' />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default ThemeSwitcher
