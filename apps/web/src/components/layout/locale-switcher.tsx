import { useLocale, useTranslations } from '@momo/i18n-web/client'
import { i18n, supportedLanguages } from '@momo/i18n-web/config'
import { usePathname, useRouter } from '@momo/i18n-web/routing'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ScrollArea
} from '@momo/ui'
import { CheckIcon, ChevronDownIcon } from 'lucide-react'
import { useTransition } from 'react'

const LocaleSwitcher = () => {
  const t = useTranslations()
  const locale = useLocale()
  const currentLanguage = supportedLanguages.find((l) => l.code === locale)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='flex size-9 items-center gap-1 p-0'
          aria-label={t('layout.change-language')}
        >
          <span className='text-base'>{currentLanguage?.icon}</span>
          <ChevronDownIcon className='size-3' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='min-w-[150px] p-0'>
        <ScrollArea className='h-[var(--radix-dropdown-menu-content-available-height)] max-h-[300px]'>
          <div className='p-1'>
            {i18n.locales.map((locale) => (
              <Item key={locale} locale={locale} />
            ))}
          </div>
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

type ItemProps = {
  locale: string
}

const Item = (props: ItemProps) => {
  const { locale } = props
  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale()

  const languageSwitchHandler = () => {
    startTransition(() => {
      router.replace(pathname, { locale })
    })
  }

  const language = supportedLanguages.find((l) => l.code === locale)
  const isSelected = currentLocale === locale

  return (
    <DropdownMenuItem
      key={locale}
      disabled={isPending}
      onClick={languageSwitchHandler}
      className='flex items-center justify-between gap-2'
    >
      <div className='flex items-center gap-2'>
        <span className='text-base'>{language?.icon}</span>
        <span>{language?.label}</span>
      </div>
      {isSelected && <CheckIcon className='size-4' />}
    </DropdownMenuItem>
  )
}

export default LocaleSwitcher
