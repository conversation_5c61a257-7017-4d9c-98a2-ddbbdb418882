'use client'

import { useTranslations } from '@momo/i18n-web/client'
import { linkVariants } from '@momo/ui'

// import { StarIcon } from 'lucide-react'
import { FOOTER_LINKS } from '@/config/links'

// import { api } from '@/trpc/react'
import Link from '../link'

const Footer = () => {
  // const { status, data } = api.github.getRepoStars.useQuery(undefined, {
  //   staleTime: 1000 * 60 * 60
  // })
  const t = useTranslations()

  return (
    <footer className='bg-background/30 relative mx-auto mb-6 flex max-w-5xl flex-col rounded-2xl p-8 shadow-sm saturate-100 backdrop-blur-[10px]'>
      <div className='mt-12 grid grid-cols-2 sm:grid-cols-3'>
        {FOOTER_LINKS.map((list) => (
          <div key={list.id} className='mb-10 flex flex-col items-start gap-4 pr-4'>
            {list.links.map((link) => {
              const { href, key } = link
              const linkText = t(`layout.${key}`)

              return (
                <Link
                  key={href}
                  href={href}
                  className={linkVariants({ variant: 'muted' })}
                  title={linkText}
                >
                  {linkText}
                </Link>
              )
            })}
          </div>
        ))}
      </div>
      <div className='mt-20 flex items-center justify-between text-sm'>
        <div>&copy; {new Date().getFullYear()} ProxyOrb</div>
        {/* <Link
          href='https://git.new/proxyorb'
          className='flex items-center justify-center overflow-hidden rounded-md border'
        >
          <div className='bg-muted flex h-8 items-center gap-2 border-r px-2'>
            <StarIcon className='size-4' />
            <span className='font-medium'>Star</span>
          </div>
          <div className='bg-background flex h-8 items-center px-3'>
            {status === 'pending' ? '--' : null}
            {status === 'error' ? t('common.error') : null}
            {status === 'success'
              ? Intl.NumberFormat('en', {
                  notation: 'compact',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 1
                }).format(data)
              : null}
          </div>
        </Link> */}
      </div>
    </footer>
  )
}

export default Footer
