# Schema.org Testing Guide for ProxyOrb

## Overview

This guide provides comprehensive instructions for testing and validating all implemented Schema.org markup on the ProxyOrb website to ensure compliance with Google's structured data guidelines.

## Testing Tools

### 1. Google Rich Results Test

- **URL**: https://search.google.com/test/rich-results
- **Purpose**: Test individual pages for rich results eligibility
- **Usage**: Enter page URLs to validate Schema markup

### 2. Google Structured Data Testing Tool (Legacy)

- **URL**: https://search.google.com/structured-data/testing-tool
- **Purpose**: Detailed Schema validation and debugging
- **Usage**: Paste HTML or enter URL for comprehensive analysis

### 3. Schema.org Validator

- **URL**: https://validator.schema.org/
- **Purpose**: Official Schema.org validation
- **Usage**: Paste JSON-LD markup for validation

### 4. Bing Markup Validator

- **URL**: https://www.bing.com/toolbox/markup-validator
- **Purpose**: Bing-specific validation
- **Usage**: Alternative validation for Bing search results

## Pages to Test

### 1. Homepage (/)

**Expected Schema Types:**

- Organization Schema
- WebSite Schema with SearchAction
- FAQPage Schema
- HowTo Schema
- Service Schema

**Test URLs:**

- `https://proxyorb.com/`
- `https://proxyorb.com/en`
- `https://proxyorb.com/zh-CN` (test internationalization)

### 2. Blog Post Pages (/blog/[slug])

**Expected Schema Types:**

- Article Schema
- BreadcrumbList Schema

**Test URLs:**

- `https://proxyorb.com/blog/top-5-online-web-proxy-websites`
- `https://proxyorb.com/blog/school-unblock-websites-student-guide`

### 3. Blog Listing Page (/blog)

**Expected Schema Types:**

- Blog Schema
- BreadcrumbList Schema

**Test URL:**

- `https://proxyorb.com/blog`

### 4. Static Pages

**Expected Schema Types:**

- WebPage Schema

**Test URLs:**

- `https://proxyorb.com/privacy-policy`
- `https://proxyorb.com/terms-of-service`

## Validation Checklist

### Organization Schema ✅

```json
Required Properties:
- @context: "https://schema.org"
- @type: "Organization"
- name: "ProxyOrb"
- url: "https://proxyorb.com"

Optional Properties:
- logo: ImageObject with url
- contactPoint: ContactPoint with contactType and email
- sameAs: Array of social media URLs
- address: PostalAddress (if applicable)
```

### WebSite Schema ✅

```json
Required Properties:
- @context: "https://schema.org"
- @type: "WebSite"
- name: Site title
- url: Site URL

Enhanced Properties:
- potentialAction: SearchAction with target and query-input
- inLanguage: Language code
- author: Person or Organization
- keywords: Array of keywords
```

### FAQPage Schema ✅

```json
Required Properties:
- @context: "https://schema.org"
- @type: "FAQPage"
- mainEntity: Array of Question objects

Question Requirements:
- @type: "Question"
- name: Question text
- acceptedAnswer: Answer object with @type "Answer" and text
```

### HowTo Schema ✅

```json
Required Properties:
- @context: "https://schema.org"
- @type: "HowTo"
- name: Title of the how-to guide
- step: Array of HowToStep objects

HowToStep Requirements:
- @type: "HowToStep"
- position: Step number
- name: Step title
- text: Step description
```

### Service Schema ✅

```json
Required Properties:
- @context: "https://schema.org"
- @type: "Service"
- name: Service name
- provider: Organization object

Optional Properties:
- serviceType: Type of service
- areaServed: Geographic area
- offers: Offer object with price and availability
```

### BreadcrumbList Schema ✅

```json
Required Properties:
- @context: "https://schema.org"
- @type: "BreadcrumbList"
- itemListElement: Array of ListItem objects

ListItem Requirements:
- @type: "ListItem"
- position: Position in breadcrumb
- name: Display name
- item: URL of the page
```

## Testing Procedures

### 1. Automated Testing

Run the validation script in development:

```bash
npm run dev
# Check browser console for validation results
```

### 2. Manual Testing Steps

#### Step 1: Homepage Validation

1. Navigate to `https://proxyorb.com/`
2. View page source (Ctrl+U)
3. Search for `application/ld+json` scripts
4. Copy each JSON-LD block
5. Paste into Google Rich Results Test
6. Verify all schemas are valid

#### Step 2: Blog Page Validation

1. Navigate to any blog post
2. Repeat source code inspection
3. Verify Article and BreadcrumbList schemas
4. Test with Google Rich Results Test

#### Step 3: Cross-Browser Testing

Test in multiple browsers:

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

#### Step 4: Mobile Testing

- Use mobile devices or browser dev tools
- Verify schemas render correctly on mobile
- Check for mobile-specific rich results

### 3. Performance Testing

Monitor impact on page performance:

- Measure page load times before/after implementation
- Check Core Web Vitals metrics
- Ensure JSON-LD doesn't block rendering

## Common Issues and Solutions

### Issue 1: Missing Required Properties

**Symptoms:** Validation errors for missing properties
**Solution:** Ensure all required properties are included in schema objects

### Issue 2: Invalid URL Formats

**Symptoms:** URL validation errors
**Solution:** Use absolute URLs with proper protocol (https://)

### Issue 3: Incorrect Data Types

**Symptoms:** Type mismatch errors
**Solution:** Verify data types match Schema.org specifications

### Issue 4: Duplicate Schemas

**Symptoms:** Multiple schemas of same type on one page
**Solution:** Combine related schemas or ensure they serve different purposes

## Monitoring and Maintenance

### 1. Regular Validation Schedule

- Weekly: Automated validation checks
- Monthly: Manual testing of all pages
- Quarterly: Comprehensive audit

### 2. Google Search Console Monitoring

- Monitor "Enhancements" section for structured data
- Check for new errors or warnings
- Track rich results performance

### 3. Performance Monitoring

- Monitor page speed impact
- Track organic traffic improvements
- Measure rich results click-through rates

## Success Metrics

### 1. Technical Metrics

- 100% valid Schema markup across all pages
- Zero structured data errors in Google Search Console
- Maintained or improved page performance scores

### 2. SEO Metrics

- Increased rich results appearances
- Improved click-through rates from search
- Higher organic traffic for target keywords

### 3. User Experience Metrics

- Better search result presentation
- Increased user engagement from search
- Improved conversion rates from organic traffic

## Troubleshooting Resources

### Documentation

- [Schema.org Documentation](https://schema.org/)
- [Google Structured Data Guidelines](https://developers.google.com/search/docs/guides/intro-structured-data)
- [JSON-LD Specification](https://json-ld.org/)

### Support Communities

- [Schema.org Community Group](https://www.w3.org/community/schemaorg/)
- [Google Search Central Community](https://support.google.com/webmasters/community)
- [Stack Overflow Schema.org Tag](https://stackoverflow.com/questions/tagged/schema.org)

## Conclusion

Regular testing and validation of Schema.org markup ensures optimal search engine visibility and rich results eligibility. Follow this guide to maintain high-quality structured data implementation and monitor its impact on SEO performance.
