FROM node:20-alpine AS base

####### 安装依赖阶段 #######
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# 直接安装指定版本的 pnpm
RUN npm install -g pnpm@10.9.0

COPY . .
RUN pnpm install --frozen-lockfile

# 删除 packages 目录下除了 node_modules 以外的所有文件
RUN find packages -mindepth 2 -maxdepth 2 \! -name "node_modules" -print | xargs rm -rf

####### 构建阶段 #######
FROM base AS builder
WORKDIR /app

# 安装相同版本的 pnpm
RUN npm install -g pnpm@10.9.0

COPY . .
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/packages ./packages
COPY --from=deps /app/apps/web/node_modules ./apps/web/node_modules
RUN pnpm run build:web

####### 生产运行阶段 #######
FROM alpine:3.20 AS runner
RUN apk add --no-cache nodejs
WORKDIR /app

COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
RUN rm -rf packages package.json

ENV NODE_ENV=production
EXPOSE 3000
CMD ["node", "apps/web/server.js"]
