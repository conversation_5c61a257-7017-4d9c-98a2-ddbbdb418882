import { i18n } from '@momo/i18n-onlinevpn/config'
import arMessages from '@momo/i18n-onlinevpn/messages/ar.json'
import bnMessages from '@momo/i18n-onlinevpn/messages/bn.json'
import deMessages from '@momo/i18n-onlinevpn/messages/de.json'
// 静态导入所有语言文件
import enMessages from '@momo/i18n-onlinevpn/messages/en.json'
import esMessages from '@momo/i18n-onlinevpn/messages/es.json'
import faMessages from '@momo/i18n-onlinevpn/messages/fa.json'
import frMessages from '@momo/i18n-onlinevpn/messages/fr.json'
import hiMessages from '@momo/i18n-onlinevpn/messages/hi.json'
import idMessages from '@momo/i18n-onlinevpn/messages/id.json'
import itMessages from '@momo/i18n-onlinevpn/messages/it.json'
import jaMessages from '@momo/i18n-onlinevpn/messages/ja.json'
import koMessages from '@momo/i18n-onlinevpn/messages/ko.json'
import paMessages from '@momo/i18n-onlinevpn/messages/pa.json'
import ptMessages from '@momo/i18n-onlinevpn/messages/pt.json'
import ruMessages from '@momo/i18n-onlinevpn/messages/ru.json'
import trMessages from '@momo/i18n-onlinevpn/messages/tr.json'
import urMessages from '@momo/i18n-onlinevpn/messages/ur.json'
import viMessages from '@momo/i18n-onlinevpn/messages/vi.json'
import zhHansMessages from '@momo/i18n-onlinevpn/messages/zh-Hans.json'
import zhHantMessages from '@momo/i18n-onlinevpn/messages/zh-Hant.json'
import { getRequestConfig } from '@momo/i18n-onlinevpn/server'
import { notFound } from 'next/navigation'

const messages = {
  en: enMessages,
  'zh-Hans': zhHansMessages,
  'zh-Hant': zhHantMessages,
  es: esMessages,
  fr: frMessages,
  de: deMessages,
  ja: jaMessages,
  ko: koMessages,
  pt: ptMessages,
  ru: ruMessages,
  ar: arMessages,
  hi: hiMessages,
  bn: bnMessages,
  fa: faMessages,
  id: idMessages,
  it: itMessages,
  pa: paMessages,
  tr: trMessages,
  ur: urMessages,
  vi: viMessages
} as const

export default getRequestConfig(async ({ requestLocale }) => {
  const locale = await requestLocale

  if (!locale || !i18n.locales.includes(locale)) {
    notFound()
  }

  return {
    messages: messages[locale as keyof typeof messages],
    locale,
    timeZone: 'UTC'
  }
})
