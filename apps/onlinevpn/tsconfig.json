{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "mdx/generated": ["./.mdx/generated"], "~/*": ["./public/*"]}, "plugins": [{"name": "next"}]}, "exclude": ["node_modules"], "extends": "@momo/tsconfig/nextjs.json", "include": ["next-env.d.ts", "eslint.config.mjs", "postcss.config.mjs", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".mdx/generated"]}