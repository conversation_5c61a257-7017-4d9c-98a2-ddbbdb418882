import { useEffect, useRef } from 'react'

type CounterProps = {
  value: number
} & React.ComponentProps<'span'>

const Counter = (props: CounterProps) => {
  const { value, ...rest } = props
  const ref = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    if (ref.current) {
      ref.current.textContent = value.toString()
    }
  }, [value])

  return (
    <span ref={ref} {...rest}>
      {value}
    </span>
  )
}

export default Counter
