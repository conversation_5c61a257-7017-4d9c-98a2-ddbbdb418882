'use client'

import Script from 'next/script'

import { isProduction } from '@/lib/constants'

const Analytics = () => {
  if (!isProduction) return null

  const gaId = 'G-3NV2BEZZDX'
  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
        strategy='afterInteractive'
      />
      <Script id='google-analytics' strategy='afterInteractive'>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${gaId}');
        `}
      </Script>
    </>
  )
}

export default Analytics
