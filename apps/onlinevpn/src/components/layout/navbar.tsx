'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { cn } from '@momo/utils'
import { usePathname } from 'next/navigation'

import { HEADER_LINKS } from '@/config/links'

import Link from '../link'

const Navbar = () => {
  const pathname = usePathname()
  const t = useTranslations()

  return (
    <nav>
      <ul className='flex items-baseline space-x-8'>
        {HEADER_LINKS.map((link) => {
          const isActive = pathname.includes(link.href.replace('/#', '#'))

          return (
            <li key={link.key}>
              <Link
                className={cn(
                  'px-3 py-2 text-sm font-medium transition-colors hover:text-pink-600',
                  isActive ? 'text-pink-600' : 'text-gray-700'
                )}
                href={link.href}
                title={t(`layout.${link.key}`)}
              >
                {t(`layout.${link.key}`)}
              </Link>
            </li>
          )
        })}
      </ul>
    </nav>
  )
}

export default Navbar
