'use client'

import { SiFacebook, <PERSON>L<PERSON>edin, <PERSON><PERSON>interest, SiReddit, SiX } from '@icons-pack/react-simple-icons'
import { useLocale, useTranslations } from '@momo/i18n-onlinevpn/client'
import { i18n, supportedLanguages } from '@momo/i18n-onlinevpn/config'
import { usePathname, useRouter } from '@momo/i18n-onlinevpn/routing'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@momo/ui'
import { ChevronDownIcon, MenuIcon, Send } from 'lucide-react'
import { useCallback, useEffect, useState, useTransition } from 'react'

import { HEADER_LINKS } from '@/config/links'
import { SITE_URL } from '@/lib/constants'

import Link from '../link'

const MobileNav = () => {
  const t = useTranslations()
  const pathname = usePathname()
  const [currentTitle, setCurrentTitle] = useState('')
  const [currentDescription, setCurrentDescription] = useState('')

  // 获取当前页面的 OpenGraph 标题和描述
  useEffect(() => {
    const getMetaContent = () => {
      const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content')
      const pageTitle = ogTitle || document.title || t('metadata.site-title')
      const ogDescription = document
        .querySelector('meta[property="og:description"]')
        ?.getAttribute('content')
      const pageDescription = ogDescription || t('metadata.site-description')
      setCurrentTitle(pageTitle)
      setCurrentDescription(pageDescription)
    }
    getMetaContent()
    const observer = new MutationObserver(() => {
      getMetaContent()
    })
    observer.observe(document.head, {
      childList: true,
      subtree: true
    })
    return () => observer.disconnect()
  }, [t, pathname])

  const currentUrl = `${SITE_URL}${pathname}`
  const encodedUrl = encodeURIComponent(currentUrl)
  const encodedTitle = encodeURIComponent(currentTitle)
  const encodedDesc = encodeURIComponent(currentDescription)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className='flex size-9 items-center justify-center p-0 md:hidden'
          aria-label={t('layout.toggle-menu')}
          variant='ghost'
        >
          <MenuIcon className='size-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-auto'>
        {/* Navigation Links */}
        {HEADER_LINKS.map((link) => (
          <DropdownMenuItem key={link.key} asChild>
            <Link
              href={link.href}
              className='flex w-full items-center px-2 py-1.5 text-left'
              title={t(`layout.${link.key}`)}
            >
              <div>{t(`layout.${link.key}`)}</div>
            </Link>
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        {/* Language Switcher */}
        <LanguageSwitcherMobile />

        <DropdownMenuSeparator />

        {/* Share Menu */}
        <ShareMenuMobile
          encodedUrl={encodedUrl}
          encodedTitle={encodedTitle}
          encodedDesc={encodedDesc}
          currentUrl={currentUrl}
          currentTitle={currentTitle}
          currentDescription={currentDescription}
          t={t}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Language Switcher for Mobile
const LanguageSwitcherMobile = () => {
  const locale = useLocale()
  const currentLanguage = supportedLanguages.find((l) => l.code === locale)
  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const languageSwitchHandler = (newLocale: string) => {
    startTransition(() => {
      router.replace(pathname, { locale: newLocale })
      setIsOpen(false)
    })
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <div className='flex w-full cursor-pointer items-center justify-between px-2 py-1.5 text-left hover:bg-gray-50'>
          <div className='flex items-center gap-2'>
            <span className='text-base'>{currentLanguage?.icon}</span>
            <span className='text-sm'>{currentLanguage?.label}</span>
          </div>
          <ChevronDownIcon className='h-3 w-3' />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent side='left' className='w-auto'>
        {i18n.locales.map((localeCode) => {
          const language = supportedLanguages.find((l) => l.code === localeCode)
          const isSelected = locale === localeCode
          return (
            <DropdownMenuItem
              key={localeCode}
              onClick={() => languageSwitchHandler(localeCode)}
              disabled={isPending}
              className={`flex items-center gap-2 ${isSelected ? 'bg-pink-50 text-pink-600' : ''}`}
            >
              <span className='text-base'>{language?.icon}</span>
              <span className='text-sm'>{language?.label}</span>
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Share Menu for Mobile
interface ShareMenuMobileProps {
  encodedUrl: string
  encodedTitle: string
  encodedDesc: string
  currentUrl: string
  currentTitle: string
  currentDescription: string
  t: any
}

const ShareMenuMobile = ({
  encodedUrl,
  encodedTitle,
  encodedDesc,
  currentUrl,
  currentTitle,
  currentDescription,
  t
}: ShareMenuMobileProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleShare = useCallback(async () => {
    if (navigator.share) {
      await navigator.share({
        title: currentTitle,
        text: currentDescription,
        url: currentUrl
      })
    }
    setIsOpen(false)
  }, [currentUrl, currentDescription, currentTitle])

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <div className='flex w-full cursor-pointer items-center justify-between px-2 py-1.5 text-left hover:bg-gray-50'>
          <div className='flex items-center gap-2'>
            <Send className='h-4 w-4' />
            <span className='text-sm'>{t('share.button-label')}</span>
          </div>
          <ChevronDownIcon className='h-3 w-3' />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent side='left' className='min-w-[140px]'>
        <DropdownMenuItem
          onClick={() => {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank')
            setIsOpen(false)
          }}
        >
          <SiFacebook className='mr-2 h-4 w-4' />
          Facebook
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            window.open(
              `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`,
              '_blank'
            )
            setIsOpen(false)
          }}
        >
          <SiX className='mr-2 h-4 w-4' />X
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            window.open(
              `https://www.linkedin.com/shareArticle?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDesc}`,
              '_blank'
            )
            setIsOpen(false)
          }}
        >
          <SiLinkedin className='mr-2 h-4 w-4' />
          LinkedIn
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            window.open(
              `https://reddit.com/submit/?url=${encodedUrl}&resubmit=true&title=${encodedTitle}`,
              '_blank'
            )
            setIsOpen(false)
          }}
        >
          <SiReddit className='mr-2 h-4 w-4' />
          Reddit
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            window.open(
              `http://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedDesc}`,
              '_blank'
            )
            setIsOpen(false)
          }}
        >
          <SiPinterest className='mr-2 h-4 w-4' />
          Pinterest
        </DropdownMenuItem>
        {typeof navigator !== 'undefined' && 'share' in navigator && (
          <DropdownMenuItem onClick={handleShare}>
            <Send className='mr-2 h-4 w-4' />
            {t('share.native')}
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default MobileNav
