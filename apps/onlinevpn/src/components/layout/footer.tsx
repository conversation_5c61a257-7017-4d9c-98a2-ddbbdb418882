'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { useState } from 'react'

import Link from '../link'

const Footer = () => {
  const t = useTranslations('footer')
  const [selectedCard, setSelectedCard] = useState<string | null>(null)

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      // 高亮选中的卡片
      setSelectedCard(sectionId)

      // 移除之前的高亮
      document.querySelectorAll('.use-case-card').forEach((card) => {
        card.classList.remove('ring-2', 'ring-airbnb-red', 'ring-offset-2')
      })

      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })

      // 添加高亮效果
      setTimeout(() => {
        const targetCard = document.getElementById(sectionId)
        if (targetCard) {
          targetCard.classList.add('ring-2', 'ring-airbnb-red', 'ring-offset-2')
        }
      }, 500)

      // 3秒后移除高亮
      setTimeout(() => {
        const targetCard = document.getElementById(sectionId)
        if (targetCard) {
          targetCard.classList.remove('ring-2', 'ring-airbnb-red', 'ring-offset-2')
        }
        setSelectedCard(null)
      }, 3500)
    }
  }

  return (
    <footer className='bg-airbnb-gray-bg mt-auto py-16'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-1 gap-8 md:grid-cols-4'>
          <div>
            <h3 className='text-airbnb-gray mb-4 text-lg font-bold'>OnlineVPN.app</h3>
            <p className='text-airbnb-gray-light mb-4 leading-relaxed'>{t('brand-description')}</p>
            <div className='flex space-x-4'>
              {/* <a
                href='mailto:<EMAIL>'
                className='text-airbnb-gray-light hover:text-airbnb-red transition-colors'
                aria-label={t('email-label')}
              >
                <svg className='h-6 w-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    d='M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
                  />
                </svg>
              </a> */}
            </div>
          </div>

          <div>
            <h4 className='text-airbnb-gray mb-4 font-semibold'>{t('sections.use-cases')}</h4>
            <ul className='text-airbnb-gray-light space-y-2'>
              <li>
                <button
                  onClick={() => scrollToSection('social-media-card')}
                  className={`hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors ${
                    selectedCard === 'social-media-card' ? 'text-airbnb-red-dark font-semibold' : ''
                  }`}
                >
                  {t('links.social-media')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('streaming-card')}
                  className={`hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors ${
                    selectedCard === 'streaming-card' ? 'text-airbnb-red-dark font-semibold' : ''
                  }`}
                >
                  {t('links.streaming')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('education-card')}
                  className={`hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors ${
                    selectedCard === 'education-card' ? 'text-airbnb-red-dark font-semibold' : ''
                  }`}
                >
                  {t('links.education')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('business-card')}
                  className={`hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors ${
                    selectedCard === 'business-card' ? 'text-airbnb-red-dark font-semibold' : ''
                  }`}
                >
                  {t('links.business')}
                </button>
              </li>
            </ul>
          </div>

          <div>
            <h4 className='text-airbnb-gray mb-4 font-semibold'>{t('sections.support')}</h4>
            <ul className='text-airbnb-gray-light space-y-2'>
              <li>
                <button
                  onClick={() => scrollToSection('faq')}
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                >
                  {t('links.faq')}
                </button>
              </li>
              <li>
                <a
                  href='mailto:<EMAIL>'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.contact-us')}
                >
                  {t('links.contact-us')}
                </a>
              </li>
              <li>
                <Link
                  href='/about'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.about')}
                >
                  {t('links.about')}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className='text-airbnb-gray mb-4 font-semibold'>{t('sections.legal')}</h4>
            <ul className='text-airbnb-gray-light space-y-2'>
              <li>
                <Link
                  href='/privacy-policy'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.privacy-policy')}
                >
                  {t('links.privacy-policy')}
                </Link>
              </li>
              <li>
                <Link
                  href='/terms-of-service'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.terms-of-service')}
                >
                  {t('links.terms-of-service')}
                </Link>
              </li>
              <li>
                <a
                  href='/sitemap.xml'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.sitemap')}
                >
                  {t('links.sitemap')}
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className='border-airbnb-border text-airbnb-gray-light mt-12 border-t pt-8 text-center'>
          <p className='mb-3'>
            {t.rich('disclaimer', {
              terms: (chunks) => (
                <Link
                  href='/terms-of-service'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.terms-of-service')}
                >
                  {chunks}
                </Link>
              ),
              privacy: (chunks) => (
                <Link
                  href='/privacy-policy'
                  className='hover:text-airbnb-red-dark font-medium text-gray-600 transition-colors'
                  title={t('links.privacy-policy')}
                >
                  {chunks}
                </Link>
              )
            })}
          </p>
          <p>{t('copyright')}</p>
          <div className='mt-6 flex justify-center space-x-4'>
            <a
              href='https://turbo0.com/item/onlinevpn'
              target='_blank'
              rel='noopener noreferrer'
              className='transition-opacity hover:opacity-80'
            >
              <img
                src='https://img.turbo0.com/badge-listed-light.svg'
                alt='Listed on Turbo0'
                style={{ height: '54px', width: 'auto' }}
              />
            </a>
            <a
              title='ai tools code.market'
              href='https://code.market?code.market=verified'
              target='_blank'
              rel='noopener noreferrer'
              className='transition-opacity hover:opacity-80'
            >
              <img
                alt='ai tools code.market'
                title='ai tools code.market'
                src='https://code.market/assets/manage-product/featured-logo-bright.svg'
                style={{ height: '54px', width: 'auto' }}
              />
            </a>
            <a
              href='https://similarlabs.com/?ref=embed'
              target='_blank'
              rel='noopener noreferrer'
              className='transition-opacity hover:opacity-80'
            >
              <img
                src='https://similarlabs.com/similarlabs-embed-badge-light.svg'
                alt='SimilarLabs Embed Badge'
                style={{ height: '54px', width: 'auto' }}
              />
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
