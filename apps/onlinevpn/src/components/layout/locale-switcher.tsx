import { useLocale, useTranslations } from '@momo/i18n-onlinevpn/client'
import { i18n, supportedLanguages } from '@momo/i18n-onlinevpn/config'
import { usePathname, useRouter } from '@momo/i18n-onlinevpn/routing'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, ScrollArea } from '@momo/ui'
import { CheckIcon, ChevronDownIcon } from 'lucide-react'
import { useTransition } from 'react'

const LocaleSwitcher = () => {
  const t = useTranslations()
  const locale = useLocale()
  const currentLanguage = supportedLanguages.find((l) => l.code === locale)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className='flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-colors hover:bg-gray-100 hover:text-pink-600'
          aria-label={t('layout.change-language')}
        >
          <span className='text-sm font-medium'>{currentLanguage?.label}</span>
          <ChevronDownIcon className='h-3 w-3' />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align='end'
        className='min-w-[180px] rounded-xl border-gray-200 bg-white p-2 shadow-xl'
      >
        <ScrollArea className='h-[var(--radix-dropdown-menu-content-available-height)] max-h-[300px]'>
          <div className='space-y-1'>
            {i18n.locales.map((locale) => (
              <Item key={locale} locale={locale} />
            ))}
          </div>
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

type ItemProps = {
  locale: string
}

const Item = (props: ItemProps) => {
  const { locale } = props
  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale()

  const languageSwitchHandler = () => {
    startTransition(() => {
      router.replace(pathname, { locale })
    })
  }

  const language = supportedLanguages.find((l) => l.code === locale)
  const isSelected = currentLocale === locale

  return (
    <button
      key={locale}
      disabled={isPending}
      onClick={languageSwitchHandler}
      className={`flex w-full items-center justify-between gap-2 rounded-lg px-3 py-2 text-left text-sm transition-colors hover:bg-gray-50 ${
        isSelected ? 'bg-pink-50 text-pink-600' : 'text-gray-700'
      } ${isPending ? 'opacity-50' : ''}`}
    >
      <div className='flex items-center gap-2'>
        <span className='text-base'>{language?.icon}</span>
        <span className='font-medium'>{language?.label}</span>
      </div>
      {isSelected && <CheckIcon className='h-4 w-4 text-pink-600' />}
    </button>
  )
}

export default LocaleSwitcher
