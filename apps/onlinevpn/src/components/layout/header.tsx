'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { useEffect } from 'react'

import Link from '../link'
import ShareMenu from '../share-menu'
import LocaleSwitcher from './locale-switcher'
import MobileNav from './mobile-nav'
import Navbar from './navbar'

const Header = () => {
  const t = useTranslations()
  const tHeader = useTranslations('header')

  const handleStartVPN = () => {
    const urlInput = document.getElementById('url-input') as HTMLInputElement
    if (urlInput) {
      // 先尝试聚焦input
      urlInput.focus()

      // 然后滚动到input位置
      const headerHeight = 80
      const inputPosition = urlInput.getBoundingClientRect().top + window.scrollY
      const offsetPosition = inputPosition - headerHeight - 60 // 额外60px间距

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    }
  }

  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector('header')
      if (header) {
        const height = header.offsetHeight
        document.documentElement.style.setProperty('--header-height', `${height}px`)
      }
    }

    updateHeaderHeight()
    window.addEventListener('resize', updateHeaderHeight)

    return () => {
      window.removeEventListener('resize', updateHeaderHeight)
    }
  }, [])

  return (
    <nav className='sticky top-0 z-50 border-b border-gray-200 bg-white'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='flex h-20 items-center justify-between'>
          {/* Skip to content link */}
          <a
            href='#skip-nav'
            className='bg-background focus-visible:ring-ring fixed left-4 top-4 -translate-y-20 rounded-sm border p-2 font-medium shadow-sm transition-transform focus-visible:translate-y-0 focus-visible:ring focus-visible:ring-offset-2'
            title={t('layout.skip-to-main-content')}
          >
            <span>{t('layout.skip-to-main-content')}</span>
          </a>

          {/* Left side - Logo and Navigation */}
          <div className='flex items-center'>
            <div className='flex flex-shrink-0 items-center'>
              <Link
                href='/'
                className='flex items-center'
                aria-label={t('layout.home')}
                title={t('layout.home')}
              >
                <div className='bg-airbnb-red mr-3 flex h-10 w-10 items-center justify-center rounded-lg shadow-md'>
                  <svg
                    className='h-7 w-7 text-white'
                    fill='none'
                    viewBox='0 0 24 24'
                    strokeWidth='2'
                  >
                    {/* Modern VPN tunnel design */}
                    <defs>
                      <linearGradient id='tunnel' x1='0%' y1='0%' x2='100%' y2='0%'>
                        <stop offset='0%' stopColor='currentColor' stopOpacity='0.3' />
                        <stop offset='50%' stopColor='currentColor' stopOpacity='0.8' />
                        <stop offset='100%' stopColor='currentColor' stopOpacity='0.3' />
                      </linearGradient>
                    </defs>

                    {/* Secure tunnel */}
                    <ellipse cx='12' cy='12' rx='10' ry='4' fill='url(#tunnel)' />
                    <ellipse
                      cx='12'
                      cy='12'
                      rx='7'
                      ry='2.5'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='1'
                      opacity='0.6'
                    />

                    {/* Data flow indicators */}
                    <circle cx='6' cy='12' r='1.5' fill='currentColor'>
                      <animate
                        attributeName='opacity'
                        values='0.3;1;0.3'
                        dur='2s'
                        repeatCount='indefinite'
                      />
                    </circle>
                    <circle cx='12' cy='12' r='1.5' fill='currentColor'>
                      <animate
                        attributeName='opacity'
                        values='0.3;1;0.3'
                        dur='2s'
                        begin='0.5s'
                        repeatCount='indefinite'
                      />
                    </circle>
                    <circle cx='18' cy='12' r='1.5' fill='currentColor'>
                      <animate
                        attributeName='opacity'
                        values='0.3;1;0.3'
                        dur='2s'
                        begin='1s'
                        repeatCount='indefinite'
                      />
                    </circle>

                    {/* Security lock indicator */}
                    <path
                      d='M10 8V6a2 2 0 114 0v2'
                      stroke='currentColor'
                      strokeWidth='1.5'
                      strokeLinecap='round'
                      fill='none'
                    />
                    <rect
                      x='9'
                      y='8'
                      width='6'
                      height='4'
                      rx='1'
                      fill='currentColor'
                      opacity='0.8'
                    />
                  </svg>
                </div>
                <div className='text-airbnb-red text-2xl font-bold'>OnlineVPN</div>
              </Link>
            </div>
            <div className='ml-10 hidden md:block'>
              <div className='flex items-baseline space-x-8'>
                <Navbar />
              </div>
            </div>
          </div>

          {/* Right side - Action buttons */}
          <div className='flex items-center space-x-4'>
            <div className='hidden items-center gap-2 md:flex'>
              <ShareMenu />
              <LocaleSwitcher />
            </div>

            {/* Start Free VPN Button - Hidden on mobile */}
            <button
              onClick={handleStartVPN}
              className='bg-airbnb-red-dark hidden transform rounded-lg px-6 py-3 text-sm font-semibold text-white shadow-lg transition-all hover:-translate-y-0.5 hover:bg-red-700 hover:shadow-xl md:block'
            >
              {tHeader('start-free-vpn')}
            </button>

            {/* Mobile Navigation */}
            <MobileNav />
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Header
