'use client'

import { SiFacebook, SiLinkedin, Si<PERSON><PERSON>erest, SiReddit, SiX } from '@icons-pack/react-simple-icons'
import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@momo/ui'
import { cn } from '@momo/utils'
import { Send } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'

import { SITE_URL } from '@/lib/constants'

interface ShareMenuProps {
  className?: string
}

const ShareMenu = ({ className }: ShareMenuProps) => {
  const t = useTranslations()
  const pathname = usePathname()
  const [currentTitle, setCurrentTitle] = useState('')
  const [currentDescription, setCurrentDescription] = useState('')

  // 获取当前页面的 OpenGraph 标题和描述
  useEffect(() => {
    const getMetaContent = () => {
      // 优先获取 og:title，如果没有则使用 document.title
      const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content')
      const pageTitle = ogTitle || document.title || t('metadata.site-title')

      // 优先获取 og:description，如果没有则使用默认描述
      const ogDescription = document
        .querySelector('meta[property="og:description"]')
        ?.getAttribute('content')
      const pageDescription = ogDescription || t('metadata.site-description')

      setCurrentTitle(pageTitle)
      setCurrentDescription(pageDescription)
    }

    // 初始设置
    getMetaContent()

    // 监听页面变化（路由变化时重新获取）
    const observer = new MutationObserver(() => {
      getMetaContent()
    })

    observer.observe(document.head, {
      childList: true,
      subtree: true
    })

    return () => observer.disconnect()
  }, [t, pathname])

  // 获取当前页面完整URL
  const currentUrl = `${SITE_URL}${pathname}`

  // URL编码处理
  const encodedUrl = encodeURIComponent(currentUrl)
  const encodedTitle = encodeURIComponent(currentTitle)
  const encodedDesc = encodeURIComponent(currentDescription)

  // 分享处理函数
  const handleShare = useCallback(async () => {
    if (navigator.share) {
      await navigator.share({
        title: currentTitle,
        text: currentDescription,
        url: currentUrl
      })
    }
  }, [currentUrl, currentDescription, currentTitle])

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={cn(
            'flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-colors hover:bg-gray-100 hover:text-pink-600',
            className
          )}
          aria-label={t('share.button-label')}
        >
          <Send className='h-4 w-4' />
          <span className='text-sm font-medium'>{t('share.button-label')}</span>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align='end'
        className='min-w-[160px] rounded-xl border-gray-200 bg-white p-2 shadow-xl'
      >
        <div className='space-y-1'>
          <button
            onClick={() => {
              window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank')
            }}
            className='flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-pink-600'
          >
            <SiFacebook className='h-4 w-4' />
            Facebook
          </button>
          <button
            onClick={() => {
              window.open(
                `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`,
                '_blank'
              )
            }}
            className='flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-pink-600'
          >
            <SiX className='h-4 w-4' />X
          </button>
          <button
            onClick={() => {
              window.open(
                `https://www.linkedin.com/shareArticle?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDesc}`,
                '_blank'
              )
            }}
            className='flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-pink-600'
          >
            <SiLinkedin className='h-4 w-4' />
            LinkedIn
          </button>
          <button
            onClick={() => {
              window.open(
                `https://reddit.com/submit/?url=${encodedUrl}&resubmit=true&title=${encodedTitle}`,
                '_blank'
              )
            }}
            className='flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-pink-600'
          >
            <SiReddit className='h-4 w-4' />
            Reddit
          </button>
          <button
            onClick={() => {
              window.open(
                `http://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedDesc}`,
                '_blank'
              )
            }}
            className='flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-pink-600'
          >
            <SiPinterest className='h-4 w-4' />
            Pinterest
          </button>
          {typeof navigator !== 'undefined' && 'share' in navigator && (
            <button
              onClick={handleShare}
              className='flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-pink-600'
            >
              <Send className='h-4 w-4' />
              {t('share.native')}
            </button>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default ShareMenu
