'use client'

import { useEffect } from 'react'

declare global {
  interface Window {
    chatwootSDK: {
      run: (config: { websiteToken: string; baseUrl: string }) => void
    }
  }
}

export default function Chatwoot() {
  useEffect(() => {
    const BASE_URL = 'https://app.chatwoot.com'
    const script = document.createElement('script')
    script.src = `${BASE_URL}/packs/js/sdk.js`
    script.defer = true
    script.async = true
    document.body.appendChild(script)

    script.onload = () => {
      window.chatwootSDK.run({
        websiteToken: 'xwJxdx9L2vbudsDBstSiNNsu',
        baseUrl: BASE_URL
      })
    }

    return () => {
      document.body.removeChild(script)
    }
  }, [])

  return null
}
