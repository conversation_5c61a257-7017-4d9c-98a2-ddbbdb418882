'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { ArrowLeft, ShieldAlert } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

const BrowserNotSupported = () => {
  const t = useTranslations('browser-not-supported')

  // 获取首页路径
  const homePath = '/'

  return (
    <div className='min-h-screen py-20'>
      <div className='mx-auto max-w-5xl px-4 sm:px-6 lg:px-8'>
        <div className='space-y-12 text-center'>
          {/* Alert Icon */}
          <div className='flex justify-center'>
            <div className='bg-airbnb-red/10 rounded-full p-8'>
              <ShieldAlert className='text-airbnb-red h-16 w-16' />
            </div>
          </div>

          {/* Content */}
          <div className='space-y-8'>
            <h2 className='text-airbnb-gray text-3xl font-bold sm:text-4xl'>{t('heading')}</h2>

            <p className='text-airbnb-gray-light mx-auto max-w-3xl text-lg leading-relaxed'>
              {t('explanation')}
            </p>

            {/* Browser Options */}
            <div className='mx-auto max-w-4xl rounded-2xl bg-white p-8 shadow-lg'>
              <h3 className='text-airbnb-gray mb-8 text-xl font-semibold'>
                {t('supported-browsers')}
              </h3>

              <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
                {/* Chrome */}
                <a
                  href='https://www.google.com/chrome/'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='group flex flex-col items-center gap-4 rounded-2xl bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg'
                  title='Download Google Chrome Browser'
                >
                  <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                    <Image
                      src='/images/icon/chrome.svg'
                      alt='Chrome'
                      fill
                      className='object-contain'
                    />
                  </div>
                  <div className='text-center'>
                    <div className='text-airbnb-gray font-semibold'>{t('chrome')}</div>
                    <div className='text-airbnb-gray-light text-sm'>{t('chrome-version')}</div>
                  </div>
                </a>

                {/* Firefox */}
                <a
                  href='https://www.mozilla.org/firefox/'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='group flex flex-col items-center gap-4 rounded-2xl bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg'
                  title='Download Mozilla Firefox Browser'
                >
                  <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                    <Image
                      src='/images/icon/firefox.svg'
                      alt='Firefox'
                      fill
                      className='object-contain'
                    />
                  </div>
                  <div className='text-center'>
                    <div className='text-airbnb-gray font-semibold'>{t('firefox')}</div>
                    <div className='text-airbnb-gray-light text-sm'>{t('firefox-version')}</div>
                  </div>
                </a>

                {/* Safari */}
                <a
                  href='https://www.apple.com/safari/'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='group flex flex-col items-center gap-4 rounded-2xl bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg'
                  title='Download Apple Safari Browser'
                >
                  <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                    <Image
                      src='/images/icon/safari.svg'
                      alt='Safari'
                      fill
                      className='object-contain'
                    />
                  </div>
                  <div className='text-center'>
                    <div className='text-airbnb-gray font-semibold'>{t('safari')}</div>
                    <div className='text-airbnb-gray-light text-sm'>{t('safari-version')}</div>
                  </div>
                </a>

                {/* Edge */}
                <a
                  href='https://www.microsoft.com/edge'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='group flex flex-col items-center gap-4 rounded-2xl bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg'
                  title='Download Microsoft Edge Browser'
                >
                  <div className='relative h-12 w-12 transition-transform duration-300 group-hover:scale-110'>
                    <Image src='/images/icon/edge.svg' alt='Edge' fill className='object-contain' />
                  </div>
                  <div className='text-center'>
                    <div className='text-airbnb-gray font-semibold'>{t('edge')}</div>
                    <div className='text-airbnb-gray-light text-sm'>{t('edge-version')}</div>
                  </div>
                </a>
              </div>
            </div>

            {/* Back to Home */}
            <div>
              <Link href={homePath} title={t('back-to-home')}>
                <button className='bg-airbnb-red hover:bg-airbnb-red-dark rounded-airbnb inline-flex transform items-center gap-2 px-8 py-4 font-semibold text-white shadow-lg transition-all hover:-translate-y-0.5 hover:shadow-xl'>
                  <ArrowLeft className='h-4 w-4' />
                  {t('back-to-home')}
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BrowserNotSupported
