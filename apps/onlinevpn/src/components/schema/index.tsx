import type {
  BreadcrumbList,
  FAQPage,
  HowTo,
  Organization,
  Service,
  WebSite,
  WithContext
} from 'schema-dts'

interface SchemaComponentProps {
  schema: WithContext<any>
}

/**
 * Generic Schema component for rendering JSON-LD structured data
 */
export const Schema = ({ schema }: SchemaComponentProps) => {
  return (
    <script
      type='application/ld+json'
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

/**
 * Organization Schema component for company information
 */
interface OrganizationSchemaProps {
  name: string
  description: string
  url: string
  logo: string
  contactPoint?: {
    telephone?: string
    email?: string
    contactType: string
  }
  sameAs?: string[]
  address?: {
    streetAddress?: string
    addressLocality?: string
    addressRegion?: string
    postalCode?: string
    addressCountry?: string
  }
}

export const OrganizationSchema = ({
  name,
  description,
  url,
  logo,
  contactPoint,
  sameAs,
  address
}: OrganizationSchemaProps) => {
  const schema: WithContext<Organization> = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    description,
    url,
    logo: {
      '@type': 'ImageObject',
      url: logo
    },
    ...(contactPoint && {
      contactPoint: {
        '@type': 'ContactPoint',
        ...contactPoint
      }
    }),
    ...(sameAs && { sameAs }),
    ...(address && {
      address: {
        '@type': 'PostalAddress',
        ...address
      }
    })
  }

  return <Schema schema={schema} />
}

/**
 * Enhanced WebSite Schema with search functionality
 */
interface WebSiteSchemaProps {
  name: string
  description: string
  url: string
  searchUrl: string
  queryInput: string
  inLanguage: string
  author?: {
    name: string
    url: string
    sameAs?: string[]
  }
  keywords?: string[]
  dateCreated?: string
  dateModified?: string
}

export const WebSiteSchema = ({
  name,
  description,
  url,
  searchUrl,
  queryInput,
  inLanguage,
  author,
  keywords,
  dateCreated,
  dateModified
}: WebSiteSchemaProps) => {
  const schema: WithContext<WebSite> = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name,
    description,
    url,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: searchUrl
      },
      'query-input': queryInput
    } as any, // Type assertion to allow query-input property
    inLanguage,
    ...(author && {
      author: {
        '@type': 'Person',
        ...author
      }
    }),
    ...(keywords && { keywords }),
    ...(dateCreated && { dateCreated }),
    ...(dateModified && { dateModified }),
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url
    }
  }

  return <Schema schema={schema} />
}

/**
 * FAQ Page Schema component
 */
interface FAQItem {
  question: string
  answer: string
}

interface FAQPageSchemaProps {
  faqs: FAQItem[]
  url?: string
}

export const FAQPageSchema = ({ faqs, url }: FAQPageSchemaProps) => {
  const schema: WithContext<FAQPage> = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    ...(url && { url }),
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  }

  return <Schema schema={schema} />
}

/**
 * HowTo Schema component for step-by-step instructions
 */
interface HowToStep {
  name: string
  text: string
  image?: string
}

interface HowToSchemaProps {
  name: string
  description: string
  steps: HowToStep[]
  totalTime?: string
  estimatedCost?: string
  supply?: string[]
  tool?: string[]
  url?: string
}

export const HowToSchema = ({
  name,
  description,
  steps,
  totalTime,
  estimatedCost,
  supply,
  tool,
  url
}: HowToSchemaProps) => {
  const schema: WithContext<HowTo> = {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name,
    description,
    ...(url && { url }),
    ...(totalTime && { totalTime }),
    ...(estimatedCost && {
      estimatedCost: {
        '@type': 'MonetaryAmount',
        currency: 'USD',
        value: estimatedCost
      }
    }),
    ...(supply && {
      supply: supply.map((item) => ({
        '@type': 'HowToSupply',
        name: item
      }))
    }),
    ...(tool && {
      tool: tool.map((item) => ({
        '@type': 'HowToTool',
        name: item
      }))
    }),
    step: steps.map((step, index) => ({
      '@type': 'HowToStep',
      position: index + 1,
      name: step.name,
      text: step.text,
      ...(step.image && {
        image: {
          '@type': 'ImageObject',
          url: step.image
        }
      })
    }))
  }

  return <Schema schema={schema} />
}

/**
 * BreadcrumbList Schema component
 */
interface BreadcrumbItem {
  name: string
  url: string
}

interface BreadcrumbListSchemaProps {
  items: BreadcrumbItem[]
}

export const BreadcrumbListSchema = ({ items }: BreadcrumbListSchemaProps) => {
  const schema: WithContext<BreadcrumbList> = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  }

  return <Schema schema={schema} />
}

/**
 * Service Schema component
 */
interface ServiceSchemaProps {
  name: string
  description: string
  provider: {
    name: string
    url: string
  }
  serviceType: string
  areaServed?: string
  url?: string
  offers?: {
    price: string
    priceCurrency: string
    availability: string
  }
}

export const ServiceSchema = ({
  name,
  description,
  provider,
  serviceType,
  areaServed,
  url,
  offers
}: ServiceSchemaProps) => {
  const schema: WithContext<Service> = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name,
    description,
    provider: {
      '@type': 'Organization',
      ...provider
    },
    serviceType,
    ...(areaServed && { areaServed }),
    ...(url && { url }),
    ...(offers && {
      offers: {
        '@type': 'Offer',
        price: offers.price,
        priceCurrency: offers.priceCurrency,
        availability: offers.availability as any // Type assertion for availability URL
      }
    })
  }

  return <Schema schema={schema} />
}
