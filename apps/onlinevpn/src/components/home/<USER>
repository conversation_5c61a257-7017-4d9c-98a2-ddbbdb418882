'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

const Testimonials = () => {
  const t = useTranslations('testimonials')
  return (
    <section className='bg-airbnb-gray-bg py-20'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='mb-16 text-center'>
          <h2 className='text-airbnb-gray mb-4 text-3xl font-bold'>{t('title')}</h2>
        </div>

        <div className='grid grid-cols-1 gap-8 md:grid-cols-3'>
          <div className='rounded-2xl bg-white p-8 shadow-sm'>
            <div className='mb-4 flex items-center'>
              <div className='bg-airbnb-red mr-4 flex h-12 w-12 items-center justify-center rounded-full'>
                <span className='font-bold text-white'>M</span>
              </div>
              <div>
                <h3 className='text-airbnb-gray font-semibold'>{t('reviews.maria.name')}</h3>
                <p className='text-airbnb-gray-light text-sm'>{t('reviews.maria.role')}</p>
              </div>
            </div>
            <p className='text-airbnb-gray-light leading-relaxed'>{t('reviews.maria.content')}</p>
            <div className='mt-4 flex text-yellow-400'>{t('reviews.maria.rating')}</div>
          </div>

          <div className='rounded-2xl bg-white p-8 shadow-sm'>
            <div className='mb-4 flex items-center'>
              <div className='bg-airbnb-red mr-4 flex h-12 w-12 items-center justify-center rounded-full'>
                <span className='font-bold text-white'>J</span>
              </div>
              <div>
                <h3 className='text-airbnb-gray font-semibold'>{t('reviews.james.name')}</h3>
                <p className='text-airbnb-gray-light text-sm'>{t('reviews.james.role')}</p>
              </div>
            </div>
            <p className='text-airbnb-gray-light leading-relaxed'>{t('reviews.james.content')}</p>
            <div className='mt-4 flex text-yellow-400'>{t('reviews.james.rating')}</div>
          </div>

          <div className='rounded-2xl bg-white p-8 shadow-sm'>
            <div className='mb-4 flex items-center'>
              <div className='bg-airbnb-red mr-4 flex h-12 w-12 items-center justify-center rounded-full'>
                <span className='font-bold text-white'>S</span>
              </div>
              <div>
                <h3 className='text-airbnb-gray font-semibold'>{t('reviews.sarah.name')}</h3>
                <p className='text-airbnb-gray-light text-sm'>{t('reviews.sarah.role')}</p>
              </div>
            </div>
            <p className='text-airbnb-gray-light leading-relaxed'>{t('reviews.sarah.content')}</p>
            <div className='mt-4 flex text-yellow-400'>{t('reviews.sarah.rating')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
