'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

import { HowToSchema } from '@/components/schema'
import { SITE_URL } from '@/lib/constants'

const HowToUse = () => {
  const t = useTranslations('how-to-use')

  // Prepare steps data for schema
  const steps = [
    {
      name: t('steps.step1.title'),
      text: t('steps.step1.description')
    },
    {
      name: t('steps.step2.title'),
      text: t('steps.step2.description')
    },
    {
      name: t('steps.step3.title'),
      text: t('steps.step3.description')
    }
  ]

  return (
    <>
      {/* HowTo Schema */}
      <HowToSchema
        name={t('title')}
        description={t('description')}
        steps={steps}
        totalTime='PT2M'
        estimatedCost='0'
        supply={['Web Browser', 'Internet Connection']}
        url={`${SITE_URL}#how-it-works`}
      />

      <section id='how-it-works' className='bg-white py-20'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='mb-16 text-center'>
            <h2 className='text-airbnb-gray mb-4 text-3xl font-bold'>{t('title')}</h2>
            <p className='text-airbnb-gray-light mx-auto max-w-3xl text-lg'>{t('description')}</p>
          </div>

          <div className='grid grid-cols-1 gap-12 md:grid-cols-3'>
            {/* Step 1 */}
            <div className='text-center'>
              <div className='bg-airbnb-red mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full shadow-lg'>
                <span className='text-2xl font-bold text-white'>1</span>
              </div>
              <h3 className='text-airbnb-gray mb-4 text-xl font-semibold'>
                {t('steps.step1.title')}
              </h3>
              <p className='text-airbnb-gray-light leading-relaxed'>
                {t('steps.step1.description')}
              </p>
            </div>

            {/* Step 2 */}
            <div className='text-center'>
              <div className='bg-airbnb-red mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full shadow-lg'>
                <span className='text-2xl font-bold text-white'>2</span>
              </div>
              <h3 className='text-airbnb-gray mb-4 text-xl font-semibold'>
                {t('steps.step2.title')}
              </h3>
              <p className='text-airbnb-gray-light leading-relaxed'>
                {t('steps.step2.description')}
              </p>
            </div>

            {/* Step 3 */}
            <div className='text-center'>
              <div className='bg-airbnb-red mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full shadow-lg'>
                <span className='text-2xl font-bold text-white'>3</span>
              </div>
              <h3 className='text-airbnb-gray mb-4 text-xl font-semibold'>
                {t('steps.step3.title')}
              </h3>
              <p className='text-airbnb-gray-light leading-relaxed'>
                {t('steps.step3.description')}
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default HowToUse
