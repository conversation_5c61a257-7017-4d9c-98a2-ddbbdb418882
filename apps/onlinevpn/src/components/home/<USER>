'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { Loader2, Zap } from 'lucide-react'
import { usePathname, useSearchParams } from 'next/navigation'
import { FormEvent, useEffect, useRef, useState } from 'react'

interface InputBoxProps {
  initialToken: string // 确保 token 是必需的
}

const InputBox = ({ initialToken }: InputBoxProps) => {
  const t = useTranslations('input-box')
  const tHero = useTranslations('hero')
  const [input, setInput] = useState('')
  const [loading, setLoading] = useState(false)
  const [token] = useState(initialToken)
  const formRef = useRef<HTMLFormElement>(null)
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const locale = pathname.split('/')[1] || 'en'

  // 自动获取URL中的q参数并设置到输入框
  useEffect(() => {
    const qParam = searchParams.get('q')
    if (qParam) {
      // 解码URL参数
      const decodedValue = decodeURIComponent(qParam)
      setInput(decodedValue)
    }
  }, [searchParams])

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    // 检查Service Worker支持
    if (!('serviceWorker' in navigator)) {
      // 如果不支持，跳转到不支持页面
      window.location.href = `/${locale}/browser-not-supported`
      return
    }

    // Validate input
    if (!input) {
      console.warn(t('error-no-input'))
      return
    }

    if (!token) {
      console.warn(t('error-no-token'))
      return
    }

    setLoading(true)

    try {
      // Submit request
      const formData = new FormData()
      formData.append('input', input)

      // 获取region参数并添加到请求中
      const regionParam = searchParams.get('r')
      if (regionParam) {
        formData.append('r', regionParam)
      }

      const response = await fetch('/api/servers', {
        method: 'POST',
        headers: {
          'x-stats-id': token
        },
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Request failed')
      }

      // 直接获取响应的文本内容并创建一个新的文档来提取URL
      const text = await response.text()
      const parser = new DOMParser()
      const doc = parser.parseFromString(text, 'text/html')
      const metaRefresh = doc.querySelector('meta[http-equiv="x-refresh"]')
      const url = metaRefresh?.getAttribute('content')

      if (url) {
        window.location.replace(url)
      } else {
        throw new Error('Invalid response format')
      }
    } catch (err) {
      console.error('Request failed:', err instanceof Error ? err.message : 'Unknown error')
      setLoading(false)
    }
  }

  return (
    <div className='relative mx-auto -mt-16 mb-16 max-w-5xl px-4 sm:px-6 lg:px-8'>
      {/* 弧形背景装饰 */}
      <div className='absolute -top-8 left-1/2 h-32 w-32 -translate-x-1/2 rounded-full bg-gradient-to-br from-white/20 to-white/5 blur-2xl'></div>

      {/* 主卡片容器 - 使用弧形设计 */}
      <div className='relative overflow-hidden rounded-3xl border border-white/20 bg-white/95 shadow-xl backdrop-blur-sm'>
        {/* 顶部弧形装饰 */}
        <div className='absolute -top-6 left-1/2 h-12 w-24 -translate-x-1/2 rounded-full bg-gradient-to-b from-pink-500/20 to-transparent'></div>

        {/* 左右侧弧形装饰 */}
        <div className='absolute -left-4 top-1/2 h-16 w-8 -translate-y-1/2 rounded-full bg-gradient-to-r from-pink-500/10 to-transparent'></div>
        <div className='absolute -right-4 top-1/2 h-16 w-8 -translate-y-1/2 rounded-full bg-gradient-to-l from-pink-500/10 to-transparent'></div>

        <div className='relative p-8'>
          <form ref={formRef} onSubmit={handleSubmit} className='space-y-6'>
            <div className='relative'>
              <input
                id='url-input'
                name='input'
                placeholder={t('placeholder')}
                className='text-airbnb-gray placeholder-airbnb-gray-light focus:border-airbnb-red focus:shadow-airbnb-red/20 w-full rounded-2xl border-2 border-gray-200/50 bg-white/80 px-6 py-4 text-lg leading-normal backdrop-blur-sm transition-all duration-300 focus:bg-white focus:shadow-lg focus:outline-none md:focus:shadow-[0_0_0_4px_rgba(255,56,92,0.1),0_8px_32px_rgba(255,56,92,0.15)]'
                style={{
                  WebkitAppearance: 'none',
                  fontSize: '16px', // 防止iOS缩放
                  lineHeight: '1.5',
                  height: 'auto',
                  // 移动端特定样式
                  WebkitTapHighlightColor: 'transparent',
                  caretColor: '#ff385c'
                }}
                value={input}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
              />
            </div>

            <button
              type='submit'
              className='from-airbnb-red group relative w-full overflow-hidden rounded-2xl bg-gradient-to-r to-pink-600 px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50'
              disabled={loading || !token}
            >
              {/* 按钮背景动画 */}
              <div className='absolute inset-0 bg-gradient-to-r from-pink-600 to-red-600 opacity-0 transition-opacity duration-300 group-hover:opacity-100'></div>

              {/* 按钮内容 */}
              <div className='relative flex w-full items-center justify-center'>
                {loading && (
                  <div className='absolute inset-0 flex items-center justify-center'>
                    <Loader2 className='h-5 w-5 animate-spin' />
                  </div>
                )}
                <span
                  className={`flex items-center gap-3 text-lg ${loading ? 'opacity-0' : 'opacity-100'}`}
                >
                  <Zap className='h-5 w-5' />
                  {t('button')}
                </span>
              </div>

              {/* 按钮光效 */}
              <div className='absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100'>
                <div className='absolute left-0 top-0 h-full w-1/3 translate-x-[-100%] skew-x-12 transform bg-gradient-to-r from-transparent via-white/20 to-transparent transition-transform duration-700 group-hover:translate-x-[300%]'></div>
              </div>
            </button>
          </form>

          {/* Trust Indicators - 重新设计为更融合的样式 */}
          <div className='mt-6 grid grid-cols-2 gap-3 md:grid-cols-4'>
            <div className='flex flex-col items-center rounded-lg bg-gradient-to-br from-green-50 to-emerald-50 p-3 text-center'>
              <div className='mb-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600'>
                <svg className='h-3 w-3 text-white' fill='currentColor' viewBox='0 0 20 20'>
                  <path
                    fillRule='evenodd'
                    d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                    clipRule='evenodd'
                  />
                </svg>
              </div>
              <span className='text-xs font-medium leading-tight text-gray-700'>
                {tHero('trust-indicators.free-online-vpn')}
              </span>
            </div>

            <div className='flex flex-col items-center rounded-lg bg-gradient-to-br from-blue-50 to-cyan-50 p-3 text-center'>
              <div className='mb-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-cyan-600'>
                <svg className='h-3 w-3 text-white' fill='currentColor' viewBox='0 0 20 20'>
                  <path
                    fillRule='evenodd'
                    d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                    clipRule='evenodd'
                  />
                </svg>
              </div>
              <span className='text-xs font-medium leading-tight text-gray-700'>
                {tHero('trust-indicators.no-download-required')}
              </span>
            </div>

            <div className='flex flex-col items-center rounded-lg bg-gradient-to-br from-purple-50 to-violet-50 p-3 text-center'>
              <div className='mb-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600'>
                <svg className='h-3 w-3 text-white' fill='currentColor' viewBox='0 0 20 20'>
                  <path
                    fillRule='evenodd'
                    d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                    clipRule='evenodd'
                  />
                </svg>
              </div>
              <span className='text-xs font-medium leading-tight text-gray-700'>
                {tHero('trust-indicators.military-grade-encryption')}
              </span>
            </div>

            <div className='flex flex-col items-center rounded-lg bg-gradient-to-br from-orange-50 to-amber-50 p-3 text-center'>
              <div className='mb-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-amber-600'>
                <svg className='h-3 w-3 text-white' fill='currentColor' viewBox='0 0 20 20'>
                  <path
                    fillRule='evenodd'
                    d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                    clipRule='evenodd'
                  />
                </svg>
              </div>
              <span className='text-xs font-medium leading-tight text-gray-700'>
                {tHero('trust-indicators.unlimited-bandwidth')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InputBox
