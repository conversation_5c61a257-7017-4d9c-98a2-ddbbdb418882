'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

const UseCases = () => {
  const t = useTranslations('use-cases')
  return (
    <section id='use-cases' className='bg-white py-20'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='mb-16 text-center'>
          <h2 className='text-airbnb-gray mb-4 text-3xl font-bold'>{t('title')}</h2>
          <p className='text-airbnb-gray-light mx-auto max-w-2xl text-lg'>{t('description')}</p>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
          {/* Use Case Card 1 */}
          <div
            id='social-media-card'
            className='use-case-card transform cursor-pointer overflow-hidden rounded-2xl bg-white shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl'
          >
            <div className='flex h-48 items-center justify-center bg-gradient-to-br from-blue-400 to-blue-600'>
              <svg
                className='h-16 w-16 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9'
                />
              </svg>
            </div>
            <div className='p-6'>
              <h3 className='text-airbnb-gray mb-2 text-lg font-semibold'>
                {t('cases.social-media.title')}
              </h3>
              <p className='text-airbnb-gray-light mb-4 text-sm'>
                {t('cases.social-media.description')}
              </p>
              <div className='flex items-center justify-between'>
                <span className='text-airbnb-red-dark font-semibold'>
                  {t('cases.social-media.service')}
                </span>
                <span className='text-airbnb-gray-light text-xs'>
                  {t('cases.social-media.rating')}
                </span>
              </div>
            </div>
          </div>

          {/* Use Case Card 2 */}
          <div
            id='streaming-card'
            className='use-case-card transform cursor-pointer overflow-hidden rounded-2xl bg-white shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl'
          >
            <div className='flex h-48 items-center justify-center bg-gradient-to-br from-green-400 to-green-600'>
              <svg
                className='h-16 w-16 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                />
              </svg>
            </div>
            <div className='p-6'>
              <h3 className='text-airbnb-gray mb-2 text-lg font-semibold'>
                {t('cases.streaming.title')}
              </h3>
              <p className='text-airbnb-gray-light mb-4 text-sm'>
                {t('cases.streaming.description')}
              </p>
              <div className='flex items-center justify-between'>
                <span className='text-airbnb-red-dark font-semibold'>
                  {t('cases.streaming.service')}
                </span>
                <span className='text-airbnb-gray-light text-xs'>
                  {t('cases.streaming.rating')}
                </span>
              </div>
            </div>
          </div>

          {/* Use Case Card 3 */}
          <div
            id='education-card'
            className='use-case-card transform cursor-pointer overflow-hidden rounded-2xl bg-white shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl'
          >
            <div className='flex h-48 items-center justify-center bg-gradient-to-br from-purple-400 to-purple-600'>
              <svg
                className='h-16 w-16 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'
                />
              </svg>
            </div>
            <div className='p-6'>
              <h3 className='text-airbnb-gray mb-2 text-lg font-semibold'>
                {t('cases.education.title')}
              </h3>
              <p className='text-airbnb-gray-light mb-4 text-sm'>
                {t('cases.education.description')}
              </p>
              <div className='flex items-center justify-between'>
                <span className='text-airbnb-red-dark font-semibold'>
                  {t('cases.education.service')}
                </span>
                <span className='text-airbnb-gray-light text-xs'>
                  {t('cases.education.rating')}
                </span>
              </div>
            </div>
          </div>

          {/* Use Case Card 4 */}
          <div
            id='business-card'
            className='use-case-card transform cursor-pointer overflow-hidden rounded-2xl bg-white shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl'
          >
            <div className='flex h-48 items-center justify-center bg-gradient-to-br from-orange-400 to-orange-600'>
              <svg
                className='h-16 w-16 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
                />
              </svg>
            </div>
            <div className='p-6'>
              <h3 className='text-airbnb-gray mb-2 text-lg font-semibold'>
                {t('cases.business.title')}
              </h3>
              <p className='text-airbnb-gray-light mb-4 text-sm'>
                {t('cases.business.description')}
              </p>
              <div className='flex items-center justify-between'>
                <span className='text-airbnb-red-dark font-semibold'>
                  {t('cases.business.service')}
                </span>
                <span className='text-airbnb-gray-light text-xs'>{t('cases.business.rating')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default UseCases
