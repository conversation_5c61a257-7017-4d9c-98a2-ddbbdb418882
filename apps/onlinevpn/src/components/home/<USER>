'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

const CTA = () => {
  const t = useTranslations('cta')
  const scrollToUrlInput = () => {
    const urlInput = document.getElementById('url-input')
    if (urlInput) {
      urlInput.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
      setTimeout(() => {
        urlInput.focus()
      }, 800)
    }
  }

  return (
    <section className='bg-white py-20'>
      <div className='mx-auto max-w-4xl px-4 text-center sm:px-6 lg:px-8'>
        <h2 className='text-airbnb-gray mb-6 text-3xl font-bold'>{t('title')}</h2>
        <p className='text-airbnb-gray-light mb-8 text-xl leading-relaxed'>{t('description')}</p>
        <button
          onClick={scrollToUrlInput}
          className='bg-airbnb-red-dark rounded-airbnb transform px-12 py-4 text-lg font-semibold text-white shadow-lg transition-all hover:-translate-y-0.5 hover:bg-red-700 hover:shadow-xl'
        >
          {t('button')}
        </button>
      </div>
    </section>
  )
}

export default CTA
