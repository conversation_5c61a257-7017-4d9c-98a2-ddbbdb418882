'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

const Hero = () => {
  const t = useTranslations('hero')
  return (
    <section
      className='relative overflow-hidden py-20 text-white'
      style={{ background: 'linear-gradient(135deg, #d63384 0%, #b83280 50%, #d63384 100%)' }}
    >
      {/* Enhanced Texture Overlay */}
      <div className='bg-hero-texture animate-grid-float absolute inset-0 opacity-60' />

      {/* Content Container */}
      <div className='relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='text-center'>
          <h1 className='mb-6 text-4xl font-bold leading-tight md:text-6xl'>
            {t('title')}
            <br />
            <span className='text-white/90'>{t('subtitle')}</span>
          </h1>

          <p className='mx-auto mb-12 max-w-3xl text-xl leading-relaxed opacity-95'>
            {t('description')}
          </p>
        </div>
      </div>
    </section>
  )
}

export default Hero
