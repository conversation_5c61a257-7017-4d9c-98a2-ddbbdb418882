'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

const Advantages = () => {
  const t = useTranslations('advantages')
  return (
    <section id='features' className='bg-airbnb-gray-bg py-20'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='mb-16 text-center'>
          <h2 className='text-airbnb-gray mb-4 text-3xl font-bold'>{t('title')}</h2>
          <p className='text-airbnb-gray-light mx-auto max-w-2xl text-lg'>{t('description')}</p>
        </div>

        <div className='grid grid-cols-1 gap-8 md:grid-cols-3'>
          {/* Feature 1 */}
          <div className='rounded-2xl bg-white p-8 text-center shadow-sm transition-all duration-300 hover:shadow-lg'>
            <div className='bg-airbnb-red mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full'>
              <svg
                className='h-8 w-8 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z'
                />
              </svg>
            </div>
            <h3 className='text-airbnb-gray mb-4 text-xl font-semibold'>
              {t('features.security.title')}
            </h3>
            <p className='text-airbnb-gray-light leading-relaxed'>
              {t('features.security.description')}
            </p>
          </div>

          {/* Feature 2 */}
          <div className='rounded-2xl bg-white p-8 text-center shadow-sm transition-all duration-300 hover:shadow-lg'>
            <div className='bg-airbnb-red mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full'>
              <svg
                className='h-8 w-8 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M13 10V3L4 14h7v7l9-11h-7z'
                />
              </svg>
            </div>
            <h3 className='text-airbnb-gray mb-4 text-xl font-semibold'>
              {t('features.speed.title')}
            </h3>
            <p className='text-airbnb-gray-light leading-relaxed'>
              {t('features.speed.description')}
            </p>
          </div>

          {/* Feature 3 */}
          <div className='rounded-2xl bg-white p-8 text-center shadow-sm transition-all duration-300 hover:shadow-lg'>
            <div className='bg-airbnb-red mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full'>
              <svg
                className='h-8 w-8 text-white'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                />
              </svg>
            </div>
            <h3 className='text-airbnb-gray mb-4 text-xl font-semibold'>
              {t('features.global.title')}
            </h3>
            <p className='text-airbnb-gray-light leading-relaxed'>
              {t('features.global.description')}
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Advantages
