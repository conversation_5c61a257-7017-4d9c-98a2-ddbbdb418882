'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'

import { FAQPageSchema } from '@/components/schema'

const FAQ = () => {
  const t = useTranslations('faq')
  // FAQ items using translations
  const faqKeys = [
    'what-is-free-online-vpn',
    'how-does-online-vpn-work',
    'is-proxy-vpn-free',
    'mobile-device-support',
    'website-access',
    'data-security'
  ]

  // Prepare FAQ data for schema
  const faqData = faqKeys.map((key) => ({
    question: t(`questions.${key}.question` as any),
    answer: t(`questions.${key}.answer` as any)
  }))

  return (
    <>
      {/* FAQ Page Schema */}
      <FAQPageSchema faqs={faqData} />

      <section id='faq' className='bg-white py-20'>
        <div className='mx-auto max-w-4xl px-4 sm:px-6 lg:px-8'>
          <div className='mb-16 text-center'>
            <h2 className='text-airbnb-gray mb-4 text-3xl font-bold'>{t('title')}</h2>
            <p className='text-airbnb-gray-light mx-auto max-w-2xl text-lg'>{t('description')}</p>
          </div>

          <div className='space-y-6'>
            {faqKeys.map((key, index) => (
              <div key={index} className='bg-airbnb-gray-bg rounded-2xl p-6'>
                <h3 className='text-airbnb-gray mb-3 text-lg font-semibold'>
                  {t(`questions.${key}.question` as any)}
                </h3>
                <p className='text-airbnb-gray-light leading-relaxed'>
                  {t(`questions.${key}.answer` as any)}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  )
}

export default FAQ
