'use client'

import Script from 'next/script'
import { useEffect } from 'react'

// import { isProduction } from '@/lib/constants'

declare global {
  interface Window {
    adsbygoogle: any[]
  }
}

const AdSenseSideRail = () => {
  // 只在生产环境显示
  //   if (!isProduction) return null

  const adsenseId = 'ca-pub-2901188880424850'

  const handleAdSenseLoad = () => {
    // AdSense 脚本加载完成后初始化广告
    if (typeof window !== 'undefined' && window.adsbygoogle) {
      try {
        const ads = document.querySelectorAll('.adsbygoogle')
        ads.forEach(() => {
          window.adsbygoogle.push({})
        })
      } catch {
        // console.error('AdSense initialization error:', error)
      }
    }
  }

  useEffect(() => {
    // 如果 AdSense 脚本已经加载，直接初始化
    if (typeof window !== 'undefined' && window.adsbygoogle) {
      handleAdSenseLoad()
    }
  }, [])

  return (
    <>
      {/* AdSense Script - 异步加载，不阻塞页面 */}
      <Script
        src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adsenseId}`}
        strategy='afterInteractive'
        crossOrigin='anonymous'
        onLoad={handleAdSenseLoad}
      />

      {/* Left Side Rail Ads */}
      <div id='adsense-left-rail' className='adsense-side-rail-ads left'>
        <div className='ad-container'>
          {/* onlinevpn-side-rail-ads-01 */}
          <ins
            className='adsbygoogle'
            style={{ display: 'inline-block', width: '120px', height: '300px' }}
            data-ad-client='ca-pub-2901188880424850'
            data-ad-slot='8064250247'
          />
          {/* onlinevpn-side-rail-ads-02 */}
          <ins
            className='adsbygoogle'
            style={{ display: 'inline-block', width: '120px', height: '300px' }}
            data-ad-client='ca-pub-2901188880424850'
            data-ad-slot='3442478958'
          />
        </div>
      </div>

      {/* Right Side Rail Ads */}
      <div id='adsense-right-rail' className='adsense-side-rail-ads right'>
        <div className='ad-container'>
          {/* onlinevpn-side-rail-ads-03 */}
          <ins
            className='adsbygoogle'
            style={{ display: 'inline-block', width: '120px', height: '300px' }}
            data-ad-client='ca-pub-2901188880424850'
            data-ad-slot='5897036308'
          />
          {/* onlinevpn-side-rail-ads-04 */}
          <ins
            className='adsbygoogle'
            style={{ display: 'inline-block', width: '120px', height: '300px' }}
            data-ad-client='ca-pub-2901188880424850'
            data-ad-slot='4639794957'
          />
        </div>
      </div>

      {/* CSS Styles */}
      <style jsx>{`
        /* AdSense Side Rail Ads Container */
        .adsense-side-rail-ads {
          position: fixed;
          top: 50%;
          transform: translateY(-50%);
          z-index: 100;
        }

        .adsense-side-rail-ads .ad-container {
          pointer-events: auto;
          display: flex;
          flex-direction: column;
          gap: 0px;
        }

        /* Left Side Ad */
        .adsense-side-rail-ads.left {
          left: 10px;
        }

        /* Right Side Ad */
        .adsense-side-rail-ads.right {
          right: 10px;
        }

        /* Responsive design - only show on large screens */
        @media only screen and (max-width: 1200px) {
          .adsense-side-rail-ads {
            display: none;
          }
        }

        .adsense-side-rail-ads .adsbygoogle {
          display: inline-block !important;
          width: 120px !important;
          height: 300px !important;
        }
      `}</style>
    </>
  )
}

export default AdSenseSideRail
