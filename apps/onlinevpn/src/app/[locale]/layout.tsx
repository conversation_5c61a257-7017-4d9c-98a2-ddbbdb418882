import '@/styles/globals.css'

import { env, flags } from '@momo/env'
import { NextIntlClientProvider } from '@momo/i18n-onlinevpn/client'
import { i18n, supportedLanguages } from '@momo/i18n-onlinevpn/config'
import { getMessages, getTranslations, setRequestLocale } from '@momo/i18n-onlinevpn/server'
import { cn } from '@momo/utils'
import { optimizeMetaDescription } from '@momo/utils'
import { GeistMono } from 'geist/font/mono'
import { GeistSans } from 'geist/font/sans'
import type { Metadata, Viewport } from 'next'
import { NuqsAdapter } from 'nuqs/adapters/next/app'

import AdSenseSideRail from '@/components/adsense-side-rail'
import Analytics from '@/components/analytics'
// import Chatwoot from '@/components/chatwoot'
import SignInDialog from '@/components/sign-in-dialog'
import { SITE_KEYWORDS, SITE_NAME, SITE_URL } from '@/lib/constants'
import { initializeRedisPool } from '@/redis'
import { getLocalizedPath } from '@/utils/get-localized-path'

import Providers from '../providers'
import ReactScan from '../react-scan'

// 初始化Redis连接池（失败不会影响应用启动）
initializeRedisPool().catch(() => {
  console.log('⚠️ Redis连接池初始化失败，应用将使用备用方式获取代理')
})

type LayoutProps = {
  children: React.ReactNode
  params: Promise<{
    locale: string
  }>
}

export const generateStaticParams = (): Array<{ locale: string }> => {
  return i18n.locales.map((locale) => ({ locale }))
}

export const generateMetadata = async (props: LayoutProps): Promise<Metadata> => {
  const { locale } = await props.params
  const t = await getTranslations({ locale, namespace: 'metadata' })

  // Optimize description for SEO
  const description = optimizeMetaDescription.default(t('site-description'))

  // Generate alternates for all supported languages
  const alternates = {
    canonical: `${SITE_URL}${getLocalizedPath({ slug: '', locale })}`,
    languages: supportedLanguages.reduce(
      (acc, lang) => {
        const langPath = getLocalizedPath({ slug: '', locale: lang.code })
        acc[lang.code] = `${SITE_URL}${langPath}`
        return acc
      },
      {} as Record<string, string>
    )
  }

  return {
    metadataBase: new URL(SITE_URL),
    title: t('site-title'),
    description,
    alternates,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1
      }
    },
    manifest: '/favicon/site.webmanifest',
    twitter: {
      card: 'summary_large_image',
      title: SITE_NAME,
      description,
      site: '@onlinevpn',
      creator: '@onlinevpn',
      images: [
        {
          url: '/images/og.png?v=8211',
          width: 1200,
          height: 630,
          alt: description
        }
      ]
    },
    keywords: SITE_KEYWORDS,
    creator: 'onlinevpn',
    openGraph: {
      url: SITE_URL,
      type: 'website',
      title: t('site-title'),
      siteName: t('site-title'),
      description,
      locale,
      images: [
        {
          url: '/images/og.png?v=8211',
          width: 1200,
          height: 630,
          alt: description,
          type: 'image/png'
        }
      ]
    },
    icons: {
      icon: '/favicon/favicon.svg',
      shortcut: '/favicon/favicon.svg',
      apple: [
        {
          url: '/favicon/apple-touch-icon.png',
          sizes: '180x180',
          type: 'image/png'
        }
      ],
      other: [
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '16x16',
          url: '/favicon/favicon-16x16.png'
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '32x32',
          url: '/favicon/favicon-32x32.png'
        }
      ]
    }
  }
}

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' }
  ]
}

const Layout = async (props: LayoutProps) => {
  const { children } = props
  const { locale } = await props.params
  setRequestLocale(locale)

  const messages = await getMessages()

  return (
    <html
      lang={locale}
      className={cn(GeistSans.variable, GeistMono.variable, 'scroll-smooth')}
      suppressHydrationWarning
    >
      <body className='relative'>
        <NuqsAdapter>
          <Providers>
            <NextIntlClientProvider messages={messages}>
              {children}
              {flags.analytics ? <Analytics /> : null}
              <AdSenseSideRail />
              <SignInDialog />
              {/* <Chatwoot /> */}
            </NextIntlClientProvider>
          </Providers>
        </NuqsAdapter>
        {env.REACT_SCAN_MONITOR_API_KEY ? (
          <ReactScan apiKey={env.REACT_SCAN_MONITOR_API_KEY} />
        ) : null}
      </body>
    </html>
  )
}

export default Layout
