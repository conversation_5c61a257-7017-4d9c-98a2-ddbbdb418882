'use client'

import { useLocale } from '@momo/i18n-onlinevpn/client'
import { i18n } from '@momo/i18n-onlinevpn/config'
import { usePathname } from 'next/navigation'

import Footer from '@/components/layout/footer'
import Header from '@/components/layout/header'

type LayoutProps = {
  children: React.ReactNode
}

const Layout = (props: LayoutProps) => {
  const { children } = props
  const pathname = usePathname()
  const locale = useLocale()

  // 判断是否为首页：
  // 1. 路径为根路径 '/' (默认英文)
  // 2. 非默认语言时匹配 /{locale} 或 /{locale}/
  const isHomePage =
    pathname === '/' ||
    (locale !== i18n.defaultLocale && new RegExp(`^/${locale}/?$`).test(pathname))

  return (
    <div className='font-airbnb flex min-h-screen flex-col bg-white'>
      <Header />
      {isHomePage ? (
        // Homepage layout without container constraints
        <main id='skip-nav' className='w-full flex-1'>
          {children}
        </main>
      ) : (
        // Other pages with container
        <main id='skip-nav' className='mx-auto mb-16 max-w-5xl flex-1 px-4 pt-12 sm:px-8'>
          {children}
        </main>
      )}
      <Footer />
    </div>
  )
}

export default Layout
