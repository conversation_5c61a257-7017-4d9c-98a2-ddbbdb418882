import { i18n } from '@momo/i18n-onlinevpn/config'
import { getTranslations, setRequestLocale } from '@momo/i18n-onlinevpn/server'
import { optimizeMetaDescription } from '@momo/utils'
import { allBlogPosts } from 'mdx/generated'
import type { Metadata, ResolvingMetadata } from 'next'
import type { Blog, WithContext } from 'schema-dts'

import FilteredPosts from '@/components/filtered-posts'
import PageTitle from '@/components/page-title'
import { SITE_NAME, SITE_URL } from '@/lib/constants'
import { generateHreflangAlternates } from '@/utils/generate-hreflang'
import { getLocalizedPath } from '@/utils/get-localized-path'

type PageProps = {
  params: Promise<{
    locale: string
  }>
  searchParams: Promise<Record<string, string | string[] | undefined>>
}

export const generateStaticParams = (): Array<{ locale: string }> => {
  return i18n.locales.map((locale) => ({ locale }))
}

export const generateMetadata = async (
  props: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> => {
  const { locale } = await props.params
  const previousOpenGraph = (await parent).openGraph ?? {}
  const previousTwitter = (await parent).twitter ?? {}
  const t = await getTranslations({ locale, namespace: 'blog' })
  const title = t('title')
  const pageTitle = `${title} | ${SITE_NAME}`
  const description = optimizeMetaDescription.default(t('description'))
  const url = getLocalizedPath({ slug: '/blog', locale })

  // Generate hreflang alternates for all supported languages
  const hreflangAlternates = generateHreflangAlternates('/blog')

  return {
    title: pageTitle,
    description,
    alternates: {
      canonical: url,
      ...hreflangAlternates
    },
    openGraph: {
      ...previousOpenGraph,
      url,
      title: pageTitle,
      description
    },
    twitter: {
      ...previousTwitter,
      title: pageTitle,
      description
    }
  }
}

const Page = async (props: PageProps) => {
  const { locale } = await props.params
  setRequestLocale(locale)
  const t = await getTranslations('blog')
  const title = t('title')
  const description = t('description')
  const url = `${SITE_URL}${getLocalizedPath({ slug: '/blog', locale })}`

  const posts = allBlogPosts
    .toSorted((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })
    .filter((post) => post.language === locale)

  const jsonLd: WithContext<Blog> = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    '@id': url,
    name: title,
    description,
    url,
    author: {
      '@type': 'Person',
      name: SITE_NAME,
      url: SITE_URL
    },
    blogPost: allBlogPosts.map((post) => ({
      '@type': 'BlogPosting',
      headline: post.title,
      url: `${url}/${post.slug}`,
      datePublished: post.date,
      dateModified: post.modifiedTime
    }))
  }

  return (
    <>
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <PageTitle title={title} description={description} />
      <FilteredPosts posts={posts} />
    </>
  )
}

export default Page
