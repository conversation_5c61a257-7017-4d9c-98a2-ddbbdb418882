'use client'

import { useTranslations } from '@momo/i18n-onlinevpn/client'
import { type TOC } from '@momo/mdx'
import { cn } from '@momo/utils'

import Link from '@/components/link'
import { useScrollspy } from '@/hooks/use-scrollspy'

type TableOfContentsProps = {
  toc: TOC[]
}

const TableOfContents = (props: TableOfContentsProps) => {
  const { toc } = props
  const activeId = useScrollspy(
    toc.map((item) => item.url),
    { rootMargin: '0% 0% -80% 0%' }
  )
  const t = useTranslations()

  return (
    <div className='hidden lg:block'>
      <div className='mb-4 pl-4'>{t('blog.on-this-page')}</div>
      <div>
        {toc.map((item) => {
          const { title, url, depth } = item

          return (
            <Link
              key={url}
              href={`#${url}`}
              className={cn(
                'text-muted-foreground hover:text-foreground block py-2.5 pr-2.5 text-sm leading-[1.2] transition-colors',
                url === activeId && 'text-foreground'
              )}
              style={{
                paddingLeft: (depth - 1) * 16
              }}
              title={`Jump to ${title}`}
            >
              {title}
            </Link>
          )
        })}
      </div>
    </div>
  )
}

export default TableOfContents
