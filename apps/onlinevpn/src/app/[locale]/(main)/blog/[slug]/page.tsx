import { getTranslations, setRequestLocale } from '@momo/i18n-onlinevpn/server'
import { getTOC } from '@momo/mdx'
import { optimizeMetaDescription } from '@momo/utils'
import { allBlogPosts } from 'mdx/generated'
import type { Metadata, ResolvingMetadata } from 'next'
import { notFound } from 'next/navigation'
import { type Article, type WithContext } from 'schema-dts'

import Mdx from '@/components/mdx'
import { BreadcrumbListSchema } from '@/components/schema'
import { SITE_NAME, SITE_URL } from '@/lib/constants'
import { generateHreflangAlternates } from '@/utils/generate-hreflang'
import { getLocalizedPath } from '@/utils/get-localized-path'

import Header from './header'
import MobileTableOfContents from './mobile-table-of-contents'
import ProgressBar from './progress-bar'
import Providers from './providers'
import TableOfContents from './table-of-contents'

type PageProps = {
  params: Promise<{
    slug: string
    locale: string
  }>
  searchParams: Promise<Record<string, string | string[] | undefined>>
}

export const generateStaticParams = (): Array<{ slug: string; locale: string }> => {
  return allBlogPosts.map((post) => ({
    slug: post.slug,
    locale: post.language
  }))
}

export const generateMetadata = async (
  props: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> => {
  const { slug, locale } = await props.params

  const post = allBlogPosts.find((p) => p.slug === slug && p.language === locale)

  if (!post) return {}

  const { date, modifiedTime, title, summary } = post
  const pageTitle = `${title} | ${SITE_NAME}`
  const optimizedSummary = optimizeMetaDescription.default(summary)

  const ISOPublishedTime = new Date(date).toISOString()
  const ISOModifiedTime = new Date(modifiedTime).toISOString()
  const previousTwitter = (await parent).twitter ?? {}
  const previousOpenGraph = (await parent).openGraph ?? {}
  const url = getLocalizedPath({ slug: `/blog/${slug}`, locale })

  // Generate hreflang alternates for all supported languages
  const hreflangAlternates = generateHreflangAlternates(`/blog/${slug}`)

  return {
    title: pageTitle,
    description: optimizedSummary,
    alternates: {
      canonical: url,
      ...hreflangAlternates
    },
    openGraph: {
      ...previousOpenGraph,
      url,
      type: 'article',
      title: pageTitle,
      description: optimizedSummary,
      publishedTime: ISOPublishedTime,
      modifiedTime: ISOModifiedTime,
      authors: SITE_URL,
      images: [
        {
          url: `/images/blog/${slug}/cover.png`,
          width: 1200,
          height: 630,
          alt: title,
          type: 'image/png'
        }
      ]
    },
    twitter: {
      ...previousTwitter,
      title: pageTitle,
      description: optimizedSummary,
      images: [
        {
          url: `/images/blog/${slug}/cover.png`,
          width: 1200,
          height: 630,
          alt: title
        }
      ]
    }
  }
}

const Page = async (props: PageProps) => {
  const { slug, locale } = await props.params
  setRequestLocale(locale)

  const post = allBlogPosts.find((p) => p.slug === slug && p.language === locale)
  const localizedPath = getLocalizedPath({ slug: `/blog/${slug}`, locale })
  const url = `${SITE_URL}${localizedPath}`

  if (!post) {
    notFound()
  }

  const { title, summary, date, modifiedTime, code, raw } = post
  const t = await getTranslations('metadata')

  const toc = await getTOC(raw)

  // Breadcrumb items for navigation hierarchy
  const breadcrumbItems = [
    {
      name: t('site-title'),
      url: `${SITE_URL}${getLocalizedPath({ slug: '', locale })}`
    },
    {
      name: 'Blog',
      url: `${SITE_URL}${getLocalizedPath({ slug: '/blog', locale })}`
    },
    {
      name: title,
      url
    }
  ]

  const jsonLd: WithContext<Article> = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    name: title,
    description: summary,
    url,
    datePublished: date,
    dateModified: modifiedTime,
    image: `${SITE_URL}/images/blog/${slug}/cover.png`,
    author: {
      '@type': 'Person',
      name: SITE_NAME,
      url: SITE_URL
    },
    publisher: {
      '@type': 'Person',
      name: SITE_NAME,
      url: SITE_URL
    }
  }

  return (
    <>
      {/* Article Schema */}
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Breadcrumb Schema */}
      <BreadcrumbListSchema items={breadcrumbItems} />

      <Providers post={post}>
        <Header />

        <div className='mt-8 flex flex-col justify-between lg:flex-row'>
          <article className='w-full lg:w-[670px]'>
            <Mdx code={code} />
          </article>
          <aside className='lg:min-w-[270px] lg:max-w-[270px]'>
            <div className='sticky top-24'>
              {toc.length > 0 ? <TableOfContents toc={toc} /> : null}
            </div>
          </aside>
        </div>
        <ProgressBar />

        {toc.length > 0 ? <MobileTableOfContents toc={toc} /> : null}
      </Providers>
    </>
  )
}

export default Page
