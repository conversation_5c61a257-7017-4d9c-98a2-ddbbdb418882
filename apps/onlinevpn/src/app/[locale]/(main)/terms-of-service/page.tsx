import { i18n } from '@momo/i18n-onlinevpn/config'
import { setRequestLocale } from '@momo/i18n-onlinevpn/server'
import { optimizeMetaDescription } from '@momo/utils'
import { allPages } from 'mdx/generated'
import type { Metadata, ResolvingMetadata } from 'next'
import { notFound } from 'next/navigation'
import type { WebPage, WithContext } from 'schema-dts'

import Mdx from '@/components/mdx'
import PageTitle from '@/components/page-title'
import { SITE_NAME, SITE_URL } from '@/lib/constants'
import { generateHreflangAlternates } from '@/utils/generate-hreflang'
import { getLocalizedPath } from '@/utils/get-localized-path'

type PageProps = {
  params: Promise<{
    locale: string
  }>
  searchParams: Promise<Record<string, string | string[] | undefined>>
}

export const generateStaticParams = (): Array<{ locale: string }> => {
  return i18n.locales.map((locale) => ({ locale }))
}

export const generateMetadata = async (
  props: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> => {
  const { locale } = await props.params
  const previousOpenGraph = (await parent).openGraph ?? {}
  const previousTwitter = (await parent).twitter ?? {}
  const page = allPages.find((p) => p.slug === 'terms-of-service' && p.language === locale)

  if (!page) {
    return {}
  }

  const { title, description } = page
  const pageTitle = `${title} | ${SITE_NAME}`
  const optimizedDescription = optimizeMetaDescription.default(description)
  const url = getLocalizedPath({ slug: '/terms-of-service', locale })

  // Generate hreflang alternates for all supported languages
  const hreflangAlternates = generateHreflangAlternates('/terms-of-service')

  return {
    title: pageTitle,
    description: optimizedDescription,
    alternates: {
      canonical: url,
      ...hreflangAlternates
    },
    openGraph: {
      ...previousOpenGraph,
      url,
      title: pageTitle,
      description: optimizedDescription
    },
    twitter: {
      ...previousTwitter,
      title: pageTitle,
      description: optimizedDescription
    }
  }
}

const Page = async (props: PageProps) => {
  const { locale } = await props.params
  setRequestLocale(locale)
  const page = allPages.find((p) => p.slug === 'terms-of-service' && p.language === locale)

  if (!page) {
    return notFound()
  }

  const { title, description, code } = page
  const url = `${SITE_URL}${getLocalizedPath({ slug: '/terms-of-service', locale })}`

  const jsonLd: WithContext<WebPage> = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: title,
    description,
    url
  }

  return (
    <>
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <PageTitle title={title} description={description} />
      <Mdx code={code} />
    </>
  )
}

export default Page
