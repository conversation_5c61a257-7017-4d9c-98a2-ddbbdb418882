import { i18n } from '@momo/i18n-onlinevpn/config'
import { getTranslations, setRequestLocale } from '@momo/i18n-onlinevpn/server'
import type { Metadata, ResolvingMetadata } from 'next'
import type { WebPage, WithContext } from 'schema-dts'

import BrowserNotSupported from '@/components/browser-not-supported'
import { SITE_URL } from '@/lib/constants'
import { generateCanonicalUrl, generateHreflangAlternates } from '@/utils/generate-hreflang'
import { getLocalizedPath } from '@/utils/get-localized-path'

type PageProps = {
  params: Promise<{
    locale: string
  }>
  searchParams: Promise<Record<string, string | string[] | undefined>>
}

export const generateStaticParams = (): Array<{ locale: string }> => {
  return i18n.locales.map((locale) => ({ locale }))
}

export const generateMetadata = async (
  props: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> => {
  const { locale } = await props.params
  const previousOpenGraph = (await parent).openGraph ?? {}
  const previousTwitter = (await parent).twitter ?? {}
  const t = await getTranslations({ locale, namespace: 'browser-not-supported' })
  const title = t('heading')
  const description = t('explanation')
  const url = getLocalizedPath({ slug: '/browser-not-supported', locale })

  // Generate canonical URL and hreflang alternates
  const canonicalUrl = generateCanonicalUrl('/browser-not-supported', locale)
  const hreflangAlternates = generateHreflangAlternates('/browser-not-supported')

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
      ...hreflangAlternates
    },
    openGraph: {
      ...previousOpenGraph,
      url,
      type: 'website',
      title,
      description
    },
    twitter: {
      ...previousTwitter,
      title,
      description
    }
  }
}

const Page = async (props: PageProps) => {
  const { locale } = await props.params
  setRequestLocale(locale)
  const t = await getTranslations({ locale, namespace: 'browser-not-supported' })
  const title = t('heading')
  const description = t('explanation')
  const url = `${SITE_URL}${getLocalizedPath({ slug: '/browser-not-supported', locale })}`

  const jsonLd: WithContext<WebPage> = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: title,
    description,
    url
  }

  return (
    <>
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <BrowserNotSupported />
    </>
  )
}

export default Page
