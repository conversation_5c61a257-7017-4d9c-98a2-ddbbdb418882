import { githubRouter } from './routers/github'
import { likesRouter } from './routers/likes'
import { ratesRouter } from './routers/rates'
import { usersRouter } from './routers/users'
import { viewsRouter } from './routers/views'
import { wakatimeRouter } from './routers/wakatime'
import { youtubeRouter } from './routers/youtube'
import { createTRPCRouter } from './trpc'

export const appRouter = createTRPCRouter({
  github: githubRouter,
  youtube: youtubeRouter,
  wakatime: wakatimeRouter,
  views: viewsRouter,
  likes: likesRouter,
  rates: ratesRouter,
  users: usersRouter
})

export type AppRouter = typeof appRouter
