import { env } from '@momo/env'
import { createPool, Factory, Options, Pool } from 'generic-pool'
import { Redis, RedisOptions } from 'ioredis'

import { createLogger } from '@/lib/logger'

// 创建Redis Pool专用的logger实例
const logger = createLogger('redis-pool')

// 连接池单例
let _redisPool: Pool<Redis> | null = null
// 是否已初始化
let _isInitialized = false
// 是否正在初始化
let _isInitializing = false
// 初始化Promise
let _initPromise: Promise<void> | null = null
// 健康检查定时器
let _healthCheckInterval: NodeJS.Timeout | null = null

// Redis连接工厂
const createRedisFactory = (redisOptions: RedisOptions): Factory<Redis> => ({
  create: async () => {
    try {
      const client = new Redis({
        ...redisOptions,
        enableReadyCheck: false
      })

      // 设置事件监听器
      client.on('error', (err) => {
        logger.error(`⚠️ Redis连接错误: ${err}`)
      })

      client.on('close', () => {
        // logger.warn(`⚠️ Redis连接关闭`)
      })

      // 验证连接是否可用
      await client.ping()
      return client
    } catch (error) {
      logger.error(`⚠️ 创建Redis连接失败: ${error}`)
      throw error
    }
  },

  destroy: async (client: Redis) => {
    try {
      await client.quit()
      logger.info(`🔄 销毁了Redis连接`)
    } catch (error) {
      logger.error(`⚠️ 销毁Redis连接失败: ${error}`)
      // 确保连接被关闭，即使quit方法失败
      client.disconnect()
    }
  },

  validate: async (client: Redis) => {
    try {
      // 验证连接是否可用
      await client.ping()
      return true
    } catch (error) {
      logger.error(`⚠️ Redis连接验证失败: ${error}`)
      return false
    }
  }
})

/**
 * 获取Redis连接池实例
 * 如果池不存在，则创建一个新的
 */
export function getRedisPool(): Pool<Redis> {
  // 如果连接池已存在，直接返回
  if (_redisPool) {
    return _redisPool
  }

  // 创建一个新的连接池
  const redisOptions: RedisOptions = {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    db: env.REDIS_DB,
    username: env.REDIS_USERNAME,
    password: env.REDIS_PASSWORD,
    connectTimeout: 3000,
    retryStrategy: (times) => Math.min(times * 50, 3000)
  }

  const poolOptions: Options = {
    min: 3, // 最小保持3个连接
    max: 10, // 最大10个连接
    acquireTimeoutMillis: 3000, // 获取连接超时3秒
    idleTimeoutMillis: 0, // 空闲连接不超时（从5分钟改为永不超时）
    evictionRunIntervalMillis: 0, // 禁用空闲连接检查（因为连接永不超时，所以不需要检查）
    testOnBorrow: true, // 借用时检查连接
    testOnReturn: false, // 归还时不检查连接
    autostart: true, // 自动启动
    fifo: true // 先进先出
  }

  const factory = createRedisFactory(redisOptions)
  _redisPool = createPool<Redis>(factory, poolOptions)

  // 设置事件监听器
  _redisPool.on('factoryCreateError', (err) => {
    logger.error(`⚠️ Redis工厂创建连接错误: ${err}`)
  })

  _redisPool.on('factoryDestroyError', (err) => {
    logger.error(`⚠️ Redis工厂销毁连接错误: ${err}`)
  })

  return _redisPool
}

/**
 * 初始化Redis连接池
 * 预热连接池，确保最小连接数可用
 */
export async function initRedisPool(): Promise<void> {
  // 防止重复初始化
  if (_isInitialized) return

  // 如果正在初始化，返回初始化Promise
  if (_isInitializing && _initPromise) {
    return _initPromise
  }

  _isInitializing = true

  // 创建初始化Promise
  _initPromise = (async () => {
    try {
      const pool = getRedisPool()
      const minConnections = pool.min || 3 // 获取配置的最小连接数，默认为3

      // 预热连接池，使用配置的最小连接数
      const warmupPromises = []
      for (let i = 0; i < minConnections; i++) {
        warmupPromises.push(
          pool.acquire().then((client) => {
            logger.info(`✅ 预热创建的Redis连接 ${i + 1}/${minConnections}`)
            return pool.release(client)
          })
        )
      }

      await Promise.all(warmupPromises)

      // 启动定期健康检查
      startPoolHealthCheck()

      logger.info(`✅ Redis连接池初始化完成`)
      _isInitialized = true
    } catch (error) {
      logger.error(`⚠️ Redis连接池初始化失败: ${error}`)
      // 初始化失败，但不抛出错误，允许应用继续运行
    } finally {
      _isInitializing = false
    }
  })()

  return _initPromise
}

/**
 * 启动连接池健康检查
 * 定期检查连接池状态，确保连接数不低于最小值
 */
function startPoolHealthCheck() {
  // 清除现有的健康检查定时器（如果存在）
  if (_healthCheckInterval) {
    clearInterval(_healthCheckInterval)
    _healthCheckInterval = null
  }

  // 每60秒检查一次连接池健康状况
  _healthCheckInterval = setInterval(async () => {
    if (!_redisPool) return

    const stats = getPoolStats()

    // 检查连接池大小是否满足最小连接数要求
    if (stats.size < stats.min) {
      logger.warn(`⚠️ 连接池大小(${stats.size})低于最小值(${stats.min})，尝试恢复...`)

      try {
        // 创建足够的连接以达到最小值
        const needToCreate = stats.min - stats.size
        const repairPromises = []

        for (let i = 0; i < needToCreate; i++) {
          repairPromises.push(
            _redisPool.acquire().then((client) => {
              logger.info(`✅ 健康检查创建的Redis连接 ${i + 1}/${needToCreate}`)
              return _redisPool?.release(client)
            })
          )
        }

        await Promise.all(repairPromises)
        logger.info(`✅ 连接池已恢复至最小连接数，当前状态: ${JSON.stringify(getPoolStats())}`)
      } catch (error) {
        logger.error(`⚠️ 恢复连接池最小连接数失败: ${error}`)
      }
    }
  }, 60000) // 每60秒执行一次

  // 确保进程退出时清理定时器
  process.on('beforeExit', () => {
    if (_healthCheckInterval) {
      clearInterval(_healthCheckInterval)
      _healthCheckInterval = null
    }
  })
}

/**
 * 使用Redis连接执行操作
 * 这个函数会自动从连接池获取连接，执行操作，然后归还连接
 * @param operation 要执行的操作函数
 * @returns 操作结果
 */
export async function withRedisClient<T>(
  operation: (client: Redis) => Promise<T>
): Promise<T | null> {
  let client: Redis | undefined

  try {
    // 如果连接池未初始化，先初始化
    if (!_isInitialized) {
      await initRedisPool()
    }

    const pool = getRedisPool()

    // 从池中获取连接
    client = await pool.acquire()

    // 执行操作
    return await operation(client)
  } catch (error) {
    logger.error(`⚠️ Redis操作执行失败: ${error}`)
    return null
  } finally {
    // 释放连接回池
    if (client && _redisPool) {
      try {
        await _redisPool.release(client)
      } catch (error) {
        logger.error(`⚠️ 释放Redis连接失败: ${error}`)
      }
    }
  }
}

/**
 * 获取连接池统计信息
 * @returns 连接池统计数据
 */
export function getPoolStats() {
  if (!_redisPool) {
    return {
      size: 0,
      available: 0,
      borrowed: 0,
      min: 0,
      max: 0,
      pending: 0
    }
  }

  return {
    size: _redisPool.size,
    available: _redisPool.available,
    borrowed: _redisPool.borrowed,
    min: _redisPool.min,
    max: _redisPool.max,
    pending: _redisPool.pending
  }
}

/**
 * 关闭Redis连接池，释放所有连接
 */
export async function closeRedisPool(): Promise<void> {
  if (_healthCheckInterval) {
    clearInterval(_healthCheckInterval)
    _healthCheckInterval = null
  }

  if (_redisPool) {
    try {
      logger.info(`🔄 开始关闭Redis连接池...`)
      await _redisPool.drain()
      await _redisPool.clear()
      _redisPool = null
      _isInitialized = false
      _isInitializing = false
      _initPromise = null
      logger.info(`✅ Redis连接池已成功关闭`)
    } catch (error) {
      logger.error(`⚠️ 关闭Redis连接池时发生错误: ${error}`)
    }
  }
}

// 应用退出时关闭连接池
process.on('beforeExit', () => {
  if (_redisPool) {
    closeRedisPool().catch(() => {})
  }
})
