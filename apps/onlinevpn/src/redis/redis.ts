import { env } from '@momo/env'
import { Redis } from 'ioredis'

import { createLogger } from '@/lib/logger'

import { generateKey, getActiveWorkersKey, WorkerInfo } from './redis-constants'
import { getPoolStats, initRedisPool, withRedisClient } from './redis-pool'

// 创建Redis专用的logger实例
const logger = createLogger('redis')

// Redis客户端上的自定义命令类型
type RedisWithCommands = Redis & {
  selectWorkerByConnection(key: string, prefix: string, region?: string): Promise<string | null>
}

// Lua脚本定义
const SCRIPTS = {
  // 获取最优Worker的脚本 (基于load和memory_usage的综合评分)
  selectWorkerByConnection: {
    lua: `
    -- 获取活跃workers的key
    local activeWorkersKey = KEYS[1]
    local workerKeyPrefix = ARGV[1]
    local targetRegion = ARGV[2]

    -- 从sorted set中获取最新的50个worker IDs (按分数降序)
    local workers = redis.call('ZREVRANGE', activeWorkersKey, 0, 49)

    if #workers == 0 then
      return nil
    end

    local optimalWorker = nil
    local minScore = math.huge

    for i, workerId in ipairs(workers) do
      -- 构建worker的key
      local workerKey = workerKeyPrefix .. workerId

      -- 获取worker信息
      local workerData = redis.call('GET', workerKey)

      if workerData then
        -- 解析JSON
        local success, workerInfo = pcall(cjson.decode, workerData)
        if success and workerInfo then
          -- 检查地区是否匹配
          local regionMatches = true
          if targetRegion ~= "" then
            regionMatches = (workerInfo.region == targetRegion)
          end

          -- 只有地区匹配的worker才参与load比较
          if regionMatches then
            local load = tonumber(workerInfo.load) or math.huge
            local memory_usage = tonumber(workerInfo.memory_usage) or 1.0

            -- 计算综合评分: load权重0.7 + memory_usage权重0.3
            -- 评分越低越好
            local score = load * 0.7 + memory_usage * 0.3

            -- 更新最优worker (最小评分)
            if score < minScore then
              minScore = score
              optimalWorker = workerData
            end
          end
        end
      end
    end

    return optimalWorker
    `,
    numberOfKeys: 1 // KEYS参数的数量
  }
}

/**
 * 在Redis客户端上注册自定义命令
 * ioredis会自动管理脚本的缓存和EVALSHA的使用
 */
function registerScripts(redis: Redis): void {
  Object.entries(SCRIPTS).forEach(([name, script]) => {
    redis.defineCommand(name, script)
  })
}

/**
 * 使用连接池获取最佳可用worker
 * 算法: 从最新的50个活跃worker中选择综合评分最低的一个
 * 评分算法: load * 0.7 + memory_usage * 0.3 (评分越低越优)
 * @param region - 可选的地区参数，用于筛选特定地区的worker
 * @returns 返回最适合的worker信息，如果没有可用worker或者出错则返回null
 */
export async function selectWorkerByConnectionPool(region?: string): Promise<WorkerInfo | null> {
  // 使用连接池执行操作
  return withRedisClient(async (redis) => {
    try {
      // 先注册自定义命令到这个连接
      registerScripts(redis)

      const startTime = Date.now()

      // 获取所需的key和参数
      const activeWorkersKey = getActiveWorkersKey()
      const workerKeyPrefix = generateKey(`${env.HEARTBEAT_WORKER_KEY}:`)

      // 调用自定义命令 (ioredis会自动管理脚本缓存)
      const result = await (redis as RedisWithCommands).selectWorkerByConnection(
        activeWorkersKey,
        workerKeyPrefix,
        region || ''
      )

      const executionTime = Date.now() - startTime

      const poolStats = getPoolStats()

      if (!result) {
        logger.warn(
          `⚠️ 未找到可用worker，执行耗时: ${executionTime}ms，连接池状态: ${JSON.stringify(poolStats)}`
        )
        return null
      }

      // 解析结果并返回
      const workerInfo = JSON.parse(result) as WorkerInfo
      const score = workerInfo.load * 0.7 + workerInfo.memory_usage * 0.3
      logger.info(
        `✅ 找到最优worker: ${workerInfo.id}${region ? ` (region: ${region})` : ''}，load: ${workerInfo.load?.toFixed(2)}，内存使用: ${(workerInfo.memory_usage * 100)?.toFixed(1)}%，综合评分: ${score.toFixed(3)}，执行耗时: ${executionTime}ms，连接池状态: ${JSON.stringify(poolStats)}`
      )
      return workerInfo
    } catch (error) {
      logger.error(`获取最佳可用worker失败: ${error}`)
      return null
    }
  })
}

/**
 * 检查Redis连接池是否就绪
 */
export async function isRedisPoolReady(): Promise<boolean> {
  try {
    // 测试连接池是否可用
    const result = await withRedisClient(async (redis) => {
      // 先注册自定义命令
      registerScripts(redis)
      // ping返回PONG字符串
      const pong = await redis.ping()
      return pong === 'PONG'
    })
    return !!result
  } catch (error) {
    logger.error(`Redis连接池健康检查失败: ${error}`)
    return false
  }
}

/**
 * 初始化应用时调用此函数预热连接池
 */
export async function initializeRedisPool(): Promise<void> {
  try {
    await initRedisPool()

    // 测试连接池并注册脚本
    const isReady = await isRedisPoolReady()

    const poolStats = getPoolStats()

    if (isReady) {
      logger.info(`Redis连接池已预热，状态: ${JSON.stringify(poolStats)}`)
    } else {
      logger.warn(`Redis连接池预热完成但健康检查未通过，状态: ${JSON.stringify(poolStats)}`)
    }
  } catch (error) {
    logger.error(`Redis连接池预热失败，应用将继续运行: ${error}`)
  }
}
