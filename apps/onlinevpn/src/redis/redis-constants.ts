import { env } from '@momo/env'

/**
 * Worker节点信息类型定义
 */
export interface WorkerInfo {
  id: string
  version: string
  region: string
  load: number
  memory_usage: number
  started_at: string
  timestamp: string
}

/**
 * 生成带有前缀的Redis键名
 */
export function generateKey(key: string): string {
  return `${env.REDIS_KEY_PREFIX}${key}`
}

/**
 * 获取Worker键名
 */
export function getWorkerKey(workerIp: string): string {
  return generateKey(`${env.HEARTBEAT_WORKER_KEY}:${workerIp}`)
}

/**
 * 获取活跃Workers键名
 */
export function getActiveWorkersKey(): string {
  return generateKey(env.HEARTBEAT_ACTIVE_WORKERS_KEY)
}
