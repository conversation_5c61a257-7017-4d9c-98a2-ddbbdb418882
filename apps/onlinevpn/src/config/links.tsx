import {
  type IconType,
  SiFacebook,
  SiGithub,
  SiInstagram,
  SiX,
  SiYoutube
} from '@icons-pack/react-simple-icons'

import {
  SITE_FACEBOOK_URL,
  SITE_GITHUB_URL,
  SITE_INSTAGRAM_URL,
  SITE_X_URL,
  SITE_YOUTUBE_URL
} from '@/lib/constants'

type SocialLinks = Array<{
  href: string
  title: string
  icon: IconType
}>

export const HEADER_LINKS = [
  {
    href: '/#features',
    key: 'features'
  },
  {
    href: '/#how-it-works',
    key: 'how-it-works'
  },
  {
    href: '/#use-cases',
    key: 'use-cases'
  }
] as const

export const FOOTER_LINKS = [
  {
    id: 1,
    links: [
      { href: '/', key: 'home' },
      { href: '/blog', key: 'blog' },
      { href: '/about', key: 'about' }
    ]
  },
  {
    id: 2,
    links: [
      { href: '/privacy-policy', key: 'privacy-policy' },
      { href: '/terms-of-service', key: 'terms-of-service' }
    ]
  },
  {
    id: 3,
    links: [
      { href: SITE_FACEBOOK_URL, key: 'facebook' },
      { href: SITE_X_URL, key: 'x' },
      { href: SITE_YOUTUBE_URL, key: 'youtube' }
    ]
  }
] as const

export const SOCIAL_LINKS: SocialLinks = [
  {
    href: SITE_GITHUB_URL,
    title: 'GitHub',
    icon: SiGithub
  },
  {
    href: SITE_FACEBOOK_URL,
    title: 'Facebook',
    icon: SiFacebook
  },
  {
    href: SITE_INSTAGRAM_URL,
    title: 'Instagram',
    icon: SiInstagram
  },
  {
    href: SITE_X_URL,
    title: 'X',
    icon: SiX
  },
  {
    href: SITE_YOUTUBE_URL,
    title: 'YouTube',
    icon: SiYoutube
  }
]
