import { env } from '@momo/env'

import { createLogger } from '@/lib/logger'

import { selectWorkerByConnectionPool } from '../redis'

const logger = createLogger('proxy-servers')

// 环境变量中的代理服务器列表
const ENV_PROXIES: string = env.FALLBACK_SERVERS || ''

/**
 * 获取环境变量中配置的所有代理
 */
export function getFallbackServer(): string {
  const proxies = ENV_PROXIES.split(',')
    .map((url: string) => url.trim())
    .filter(Boolean)

  if (proxies.length === 0) {
    throw new Error('没有可用的代理服务器')
  }

  return proxies[Math.floor(Math.random() * proxies.length)] || ''
}

/**
 * 获取最优代理
 * 优先从Redis连接池中获取负载最低的，否则随机选择一个
 */
export async function selectProxyServer(region?: string): Promise<string> {
  // 尝试从Redis连接池获取最优代理
  try {
    // 使用连接池实现
    const worker = await selectWorkerByConnectionPool(region)
    if (worker?.id) {
      return worker.id
    }
    logger.warn('⚠️ Redis连接池中无可用代理')
  } catch (error) {
    logger.error(`⚠️ Redis获取代理失败: ${error}`)
  }

  // Redis方法失败，使用环境变量配置的代理
  return getFallbackServer()
}
