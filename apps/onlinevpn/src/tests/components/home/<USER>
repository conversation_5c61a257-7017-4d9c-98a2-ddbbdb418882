import { NextIntlClientProvider } from '@momo/i18n-onlinevpn/client'
import messages from '@momo/i18n-onlinevpn/messages/en.json'
import { render, screen } from '@testing-library/react'
import { describe, expect, it } from 'vitest'

import Hero from '@/components/home/<USER>'

describe('<Hero />', () => {
  it('should have a hero image', () => {
    render(
      <NextIntlClientProvider locale='en' messages={messages}>
        <Hero />
      </NextIntlClientProvider>
    )

    expect(screen.getByAltText('Proxy')).toBeInTheDocument()
  })
})
