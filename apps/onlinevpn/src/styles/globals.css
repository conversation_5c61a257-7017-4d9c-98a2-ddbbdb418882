@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    height: 100%;
  }

  body {
    min-height: 100vh;
    margin: 0;
    padding: 0;
  }

  /* 修复移动端底部空白问题 */
  @supports (-webkit-touch-callout: none) {
    body {
      min-height: -webkit-fill-available;
    }
  }

  /* 修复移动端输入框光标问题 */
  @media (max-width: 768px) {
    input[type='text'],
    input[type='url'],
    input[type='search'] {
      -webkit-appearance: none;
      appearance: none;
      -webkit-tap-highlight-color: transparent;
      transform: translateZ(0); /* 强制硬件加速 */
    }

    input:focus {
      transform: translateZ(0); /* 保持硬件加速 */
      -webkit-user-select: text;
      user-select: text;
    }
  }
}

@layer components {
  .h-content {
    min-height: calc(100vh - 416px - 96px - 96px - 64px - 24px);
  }

  @media (max-width: 640px) {
    .h-content {
      min-height: calc(100vh - 600px - 96px - 96px - 64px - 24px);
    }
  }
}

.shiki span {
  color: var(--shiki-light);
}

.dark .shiki span {
  color: var(--shiki-dark);
}

pre.shiki {
  font-size: 13px;
}

pre.shiki .highlighted {
  margin: 0 -16px;
  padding: 0 16px;
  display: inline-block;
  min-width: calc(100% + 32px);
  background-color: theme('colors.primary.DEFAULT / 10%');
}
