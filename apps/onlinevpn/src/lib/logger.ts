import 'winston-daily-rotate-file'

import { env } from '@momo/env'
import chalk from 'chalk'
import dayjs from 'dayjs'
import fs from 'fs'
import path from 'path'
import util from 'util'
import winston from 'winston'

// 创建日志目录
const logDir = path.join(process.cwd(), 'logs')
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true })
}

// 启用chalk强制着色（解决某些环境下颜色不显示的问题）
chalk.level = 3 // 强制启用颜色级别：3 = 256色

// 定义日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
}

// 定义日志级别类型
type LogLevel = keyof typeof levels

// 定义颜色配置类型 - 使用 any 类型临时解决 chalk 的类型问题
interface ColorConfig {
  bg: any
  text: any
}

// 定义日志颜色（更丰富的颜色方案）
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'cyan'
}

// 添加颜色
winston.addColors(colors)

// 增强的日志格式，包含模块名和更好的结构化
const customFormat = winston.format.printf((info) => {
  const moduleStr = info.module ? `[${info.module}]` : ''
  const timestamp = `${info.timestamp}`
  const level = `${info.level.toUpperCase()}`

  // 为文件输出构建格式化的日志
  return `${timestamp} ${level.padEnd(7)} ${moduleStr ? moduleStr + ' ' : ''}${info.message}`
})

// 创建格式
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  customFormat
)

// 为控制台准备的颜色主题
const levelColors: Record<LogLevel, ColorConfig> = {
  error: { bg: chalk.bgRed, text: chalk.white.bold },
  warn: { bg: chalk.bgYellow, text: chalk.black.bold },
  info: { bg: chalk.bgGreen, text: chalk.black.bold },
  http: { bg: chalk.bgMagenta, text: chalk.white.bold },
  debug: { bg: chalk.bgCyan, text: chalk.black.bold }
}

// 默认颜色配置
const defaultColorConfig: ColorConfig = { bg: chalk.bgWhite, text: chalk.black.bold }

// 控制台格式（带有更丰富的颜色）
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.printf((info) => {
    // 准备着色的组件
    const moduleColor = chalk.blue
    const timeColor = chalk.gray
    // 删除进程ID显示
    // const processPid = chalk.yellow(`[${process.pid}]`)

    // 获取当前级别的颜色配置
    const levelColorConfig = levelColors[info.level as LogLevel] || defaultColorConfig

    // 创建彩色级别标签
    const levelPadded = ` ${info.level.toUpperCase()} `.padEnd(8, ' ')
    const levelColored = levelColorConfig.bg(levelColorConfig.text(levelPadded))

    // 创建模块名称
    const moduleStr = info.module ? moduleColor(`[${info.module}]`) : ''

    // 组装最终日志，移除进程ID
    return `${timeColor(info.timestamp)} ${levelColored} ${moduleStr ? moduleStr + ' ' : ''}${info.message}`
  })
)

// 自定义文件名生成器函数
const filenameGenerator = (time: Date) => {
  const today = dayjs().format('YYYY-MM-DD')
  const logDate = dayjs(time).format('YYYY-MM-DD')

  // 如果是今天的日志，不加日期
  if (logDate === today) {
    return 'onlinevpn.log'
  }

  // 如果不是今天的日志，加上日期
  return `onlinevpn-${logDate}.log`
}

// 自定义错误日志文件名生成器
const errorFilenameGenerator = (time: Date) => {
  const today = dayjs().format('YYYY-MM-DD')
  const logDate = dayjs(time).format('YYYY-MM-DD')

  // 如果是今天的日志，不加日期
  if (logDate === today) {
    return 'onlinevpn-error.log'
  }

  // 如果不是今天的日志，加上日期
  return `onlinevpn-error-${logDate}.log`
}

// 定义日志传输
const transports = [
  // 控制台输出 (改进的颜色方案)
  new winston.transports.Console({
    format: consoleFormat,
    stderrLevels: ['error'], // 错误级别输出到stderr
    consoleWarnLevels: ['warn'] // 警告级别使用console.warn
  }),

  // 按天轮转的日志文件
  new winston.transports.DailyRotateFile({
    dirname: logDir,
    filename: 'onlinevpn-%DATE%.log', // 必须有%DATE%占位符，但实际使用filenameGenerator
    datePattern: 'YYYY-MM-DD',
    maxFiles: '30d', // 保留最近30天的日志
    format,
    createSymlink: true, // 创建符号链接
    symlinkName: 'onlinevpn.log', // 今天日志的符号链接名称
    auditFile: path.join(logDir, 'onlinevpn-audit.json'), // 轮转记录文件
    auditHashType: 'md5',
    utc: false,
    extension: '', // 防止添加额外扩展名
    // 自定义文件名
    // @ts-expect-error - DefinitelyTyped缺少此属性的类型定义
    filename_cb: filenameGenerator
  }),

  // 错误日志单独保存
  new winston.transports.DailyRotateFile({
    level: 'error',
    dirname: logDir,
    filename: 'onlinevpn-error-%DATE%.log', // 必须有%DATE%占位符，但实际使用errorFilenameGenerator
    datePattern: 'YYYY-MM-DD',
    maxFiles: '30d', // 保留最近30天的日志
    format,
    createSymlink: true, // 创建符号链接
    symlinkName: 'onlinevpn-error.log', // 今天错误日志的符号链接名称
    auditFile: path.join(logDir, 'onlinevpn-error-audit.json'), // 轮转记录文件
    auditHashType: 'md5',
    utc: false,
    extension: '', // 防止添加额外扩展名
    // 自定义文件名
    // @ts-expect-error - DefinitelyTyped缺少此属性的类型定义
    filename_cb: errorFilenameGenerator
  })
]

// 从环境变量获取日志级别，默认为'info'
const logLevel = env.LOG_LEVEL

// 创建 winston logger 实例
const winstonLogger = winston.createLogger({
  level: logLevel,
  levels,
  format,
  transports,
  defaultMeta: { module: 'app' } // 默认模块名
})

// 改进的格式化函数，更好地处理各种类型的参数
function formatArgs(args: unknown[]): string {
  if (args.length === 0) return ''

  // 处理第一个参数
  const firstArg = args[0]

  // 处理错误对象
  if (firstArg instanceof Error) {
    const errorMsg = `${firstArg.message}\n${firstArg.stack || ''}`
    return args.length === 1 ? errorMsg : `${errorMsg} ${formatArgs(args.slice(1))}`
  }

  // 只有一个参数的情况
  if (args.length === 1) {
    if (typeof firstArg === 'object' && firstArg !== null) {
      try {
        // 使用 util.inspect 而不是 JSON.stringify 来更好地处理循环引用和格式
        return util.inspect(firstArg, { depth: 4, colors: false, maxArrayLength: 100 })
      } catch {
        return String(firstArg)
      }
    }
    return String(firstArg)
  }

  // 多个参数的情况
  try {
    // 如果第一个参数是字符串，尝试用 util.format
    if (typeof firstArg === 'string') {
      // 检查是否包含 %s, %d 等格式化标记
      if (/%[sdjifoO%]/.test(firstArg)) {
        return util.format(...args)
      } else {
        // 不包含格式化标记，格式化每个参数
        return args
          .map((arg) => {
            if (arg instanceof Error) {
              return `${arg.message}\n${arg.stack || ''}`
            }

            if (typeof arg === 'object' && arg !== null) {
              try {
                return util.inspect(arg, { depth: 3, colors: false, maxArrayLength: 50 })
              } catch {
                return String(arg)
              }
            }

            return String(arg)
          })
          .join(' ')
      }
    } else {
      // 第一个参数不是字符串，格式化所有参数
      return args
        .map((arg) => {
          if (arg instanceof Error) {
            return `${arg.message}\n${arg.stack || ''}`
          }

          if (typeof arg === 'object' && arg !== null) {
            try {
              return util.inspect(arg, { depth: 3, colors: false, maxArrayLength: 50 })
            } catch {
              return String(arg)
            }
          }
          return String(arg)
        })
        .join(' ')
    }
  } catch {
    // 如果有任何异常，尝试简单地串联所有参数
    return args.map((arg) => String(arg)).join(' ')
  }
}

// 基础 logger 实例
const baseLogger = {
  debug: (...args: unknown[]) => winstonLogger.debug(formatArgs(args)),
  info: (...args: unknown[]) => winstonLogger.info(formatArgs(args)),
  warn: (...args: unknown[]) => winstonLogger.warn(formatArgs(args)),
  error: (...args: unknown[]) => winstonLogger.error(formatArgs(args)),
  http: (...args: unknown[]) => winstonLogger.http(formatArgs(args))
}

// 导出默认 logger 实例
export const logger = baseLogger

/**
 * 创建一个带有模块名的 logger 实例
 * @param moduleName 模块名称
 * @returns 带有模块上下文的 logger 实例
 */
export function createLogger(moduleName: string) {
  const moduleLogger = winston.createLogger({
    level: logLevel,
    levels,
    format,
    transports: winstonLogger.transports,
    defaultMeta: { module: moduleName }
  })

  return {
    debug: (...args: unknown[]) => moduleLogger.debug(formatArgs(args)),
    info: (...args: unknown[]) => moduleLogger.info(formatArgs(args)),
    warn: (...args: unknown[]) => moduleLogger.warn(formatArgs(args)),
    error: (...args: unknown[]) => moduleLogger.error(formatArgs(args)),
    http: (...args: unknown[]) => moduleLogger.http(formatArgs(args))
  }
}

export default baseLogger
