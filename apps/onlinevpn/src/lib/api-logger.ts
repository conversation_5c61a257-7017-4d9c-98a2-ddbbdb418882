import { NextRequest, NextResponse } from 'next/server'

import { createLogger } from './logger'

// 创建专用的API日志记录器
const logger = createLogger('api-logger')

export interface ApiLoggerOptions {
  includePath?: boolean
  includeMethod?: boolean
  includeBody?: boolean
  includeHeaders?: boolean
  includeStatus?: boolean
}

const defaultOptions: ApiLoggerOptions = {
  includePath: true,
  includeMethod: true,
  includeBody: true,
  includeHeaders: false,
  includeStatus: true
}

/**
 * API 日志记录器中间件
 * @param req NextRequest 请求对象
 * @param handler 下一个处理函数
 * @param options 日志选项
 */
export async function apiLogger(
  req: NextRequest,
  handler: () => Promise<NextResponse>,
  options: ApiLoggerOptions = defaultOptions
) {
  const startTime = Date.now()
  const {
    includePath = true,
    includeMethod = true,
    includeBody = true,
    includeHeaders = false,
    includeStatus = true
  } = options

  // 构建请求日志
  const logParts: string[] = []
  if (includeMethod) logParts.push(`[${req.method}]`)
  if (includePath) logParts.push(req.url)

  // 记录请求体（如果适用）
  let requestBody = null
  if (includeBody && req.body && req.headers.get('content-type')?.includes('application/json')) {
    try {
      const clonedReq = req.clone()
      requestBody = await clonedReq.json()
      logParts.push(`Body: ${JSON.stringify(requestBody)}`)
    } catch {
      // 无法解析为 JSON，忽略
    }
  }

  // 记录请求头
  if (includeHeaders) {
    const headers: Record<string, string> = {}
    req.headers.forEach((value, key) => {
      // 排除敏感信息
      if (!['authorization', 'cookie'].includes(key.toLowerCase())) {
        headers[key] = value
      }
    })
    logParts.push(`Headers: ${JSON.stringify(headers)}`)
  }

  // 请求日志
  logger.info(`API Request: ${logParts.join(' ')}`)

  try {
    // 执行处理函数
    const response = await handler()

    // 计算响应时间
    const endTime = Date.now()
    const duration = endTime - startTime

    // 构建响应日志
    const responseLogParts = [`Duration: ${duration}ms`]

    if (includeStatus) {
      responseLogParts.push(`Status: ${response.status}`)
    }

    // 记录响应体
    if (includeBody && response.headers.get('content-type')?.includes('application/json')) {
      try {
        const clonedRes = response.clone()
        const responseBody = await clonedRes.json()
        responseLogParts.push(`Body: ${JSON.stringify(responseBody)}`)
      } catch {
        // 无法解析为 JSON，忽略
      }
    }

    // 响应日志
    if (response.status >= 400) {
      logger.error(`API Response: ${responseLogParts.join(' ')}`)
    } else {
      logger.info(`API Response: ${responseLogParts.join(' ')}`)
    }

    return response
  } catch (error) {
    // 记录错误
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`API Error: ${errorMessage}`, {
      stack: error instanceof Error ? error.stack : undefined,
      url: req.url,
      method: req.method
    })

    throw error
  }
}
