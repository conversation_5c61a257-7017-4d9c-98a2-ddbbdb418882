import { supportedLanguages } from '@momo/i18n-onlinevpn/config'

import { SITE_URL } from '@/lib/constants'

import { getLocalizedPath } from './get-localized-path'

/**
 * Generate hreflang alternates for all supported languages
 * @param slug - The page slug (e.g., '', '/about', '/blog/post-title')
 * @returns Object with canonical URL and language alternates
 */
export function generateHreflangAlternates(slug: string = '') {
  const languages = supportedLanguages.reduce(
    (acc, lang) => {
      const langPath = getLocalizedPath({ slug, locale: lang.code })
      acc[lang.code] = `${SITE_URL}${langPath}`
      return acc
    },
    {} as Record<string, string>
  )

  // Add x-default pointing to the default language (English)
  const defaultLangPath = getLocalizedPath({ slug, locale: 'en' })
  languages['x-default'] = `${SITE_URL}${defaultLangPath}`

  return {
    languages
  }
}

/**
 * Generate canonical URL for a specific locale and slug
 * @param slug - The page slug
 * @param locale - The current locale
 * @returns Canonical URL
 */
export function generateCanonicalUrl(slug: string = '', locale: string) {
  const localizedPath = getLocalizedPath({ slug, locale })
  return `${SITE_URL}${localizedPath}`
}
