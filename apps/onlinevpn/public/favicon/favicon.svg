<svg width="32" height="32" viewBox="0 0 32 32" fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <!-- Background with rounded corners -->
    <rect width="32" height="32" rx="6" fill="#FF5A5F"/>

    <!-- VPN tunnel design optimized for small sizes -->
    <defs>
        <linearGradient id="tunnel-grad" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="white" stop-opacity="0.4" />
            <stop offset="50%" stop-color="white" stop-opacity="0.9" />
            <stop offset="100%" stop-color="white" stop-opacity="0.4" />
        </linearGradient>
    </defs>

    <!-- Main tunnel ellipse -->
    <ellipse cx="16" cy="16" rx="12" ry="5" fill="url(#tunnel-grad)" />
    <ellipse cx="16" cy="16" rx="9" ry="3" fill="none" stroke="white" stroke-width="1.5" opacity="0.7" />

    <!-- Data flow dots - simplified for small sizes -->
    <circle cx="8" cy="16" r="2" fill="white" opacity="0.9" />
    <circle cx="16" cy="16" r="2" fill="white" opacity="1" />
    <circle cx="24" cy="16" r="2" fill="white" opacity="0.7" />

    <!-- Security lock - simplified and larger for visibility -->
    <path d="M13 10V8a3 3 0 116 0v2" stroke="white" stroke-width="2" stroke-linecap="round" fill="none" />
    <rect x="12" y="10" width="8" height="6" rx="1.5" fill="white" opacity="0.9" />
    <circle cx="16" cy="13" r="1" fill="#FF5A5F" />
</svg>
