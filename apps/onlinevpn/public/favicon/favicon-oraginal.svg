<svg width="32" height="32" viewBox="0 0 24 24" fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="4" fill="#FF5A5F"/>

    <defs>
        <linearGradient id="tunnel" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="white" stop-opacity="0.3" />
            <stop offset="50%" stop-color="white" stop-opacity="0.8" />
            <stop offset="100%" stop-color="white" stop-opacity="0.3" />
        </linearGradient>
    </defs>

    <!-- Secure tunnel -->
    <ellipse cx="12" cy="12" rx="10" ry="4" fill="url(#tunnel)" />
    <ellipse cx="12" cy="12" rx="7" ry="2.5" fill="none" stroke="white" stroke-width="1" opacity="0.6" />

    <!-- Data flow indicators with animation -->
    <circle cx="6" cy="12" r="1.5" fill="white">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" />
    </circle>
    <circle cx="12" cy="12" r="1.5" fill="white">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="0.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="18" cy="12" r="1.5" fill="white">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" begin="1s" repeatCount="indefinite" />
    </circle>

    <!-- Security lock indicator -->
    <path d="M10 8V6a2 2 0 114 0v2" stroke="white" stroke-width="1.5" stroke-linecap="round" fill="none" />
    <rect x="9" y="8" width="6" height="4" rx="1" fill="white" opacity="0.8" />
</svg>
