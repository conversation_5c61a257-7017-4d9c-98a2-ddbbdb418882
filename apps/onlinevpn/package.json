{"private": true, "name": "@momo/onlinevpn", "version": "0.0.0", "description": "OnlineVPN - Free Online VPN Proxy Service for Anonymous Browsing", "license": "MIT", "type": "module", "scripts": {"build": "pnpm build:mdx && dotenv -e ../../.env.production -- next build", "build:mdx": "mdx build", "bundle-analyzer": "cross-env ANALYZE=true pnpm run build", "clean": "rm -rf .next .turbo .mdx test-results playwright-report", "dev": "concurrently \"mdx dev\" \"dotenv -e ../../.env.local -- next dev -p 3000\" \"wait-on http://localhost:3000 && open http://localhost:3000\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "start": "dotenv -e ../../.env.local -- next start -p 3000", "test:e2e": "cross-env NODE_ENV=test dotenv -e ../../.env.local -- playwright test", "test:e2e:inspector": "cross-env NODE_ENV=test dotenv -e ../../.env.local -- playwright test $1 --debug", "test:e2e:ui": "cross-env NODE_ENV=test dotenv -e ../../.env.local -- playwright test --ui", "type-check": "tsc --noEmit"}, "dependencies": {"@auth/drizzle-adapter": "1.7.2", "@hookform/resolvers": "^3.9.1", "@icons-pack/react-simple-icons": "10.2.0", "@momo/db": "workspace:*", "@momo/env": "workspace:*", "@momo/i18n-onlinevpn": "workspace:*", "@momo/mdx": "workspace:*", "@momo/ui": "workspace:*", "@momo/utils": "workspace:*", "@nanostores/react": "^0.8.4", "@normy/react-query": "^0.17.1", "@number-flow/react": "^0.4.4", "@octokit/rest": "^21.0.2", "@paralleldrive/cuid2": "^2.2.2", "@tanstack/react-query": "^5.62.15", "@tanstack/react-query-devtools": "^5.62.10", "@tanstack/react-query-next-experimental": "^5.62.10", "@tanstack/react-table": "^8.20.6", "@trpc/client": "11.0.0-rc.666", "@trpc/react-query": "11.0.0-rc.666", "@trpc/server": "11.0.0-rc.666", "@types/ioredis": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.20.0", "@upstash/redis": "^1.28.4", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "cobe": "^0.6.3", "dayjs": "^1.11.13", "fast-xml-parser": "^4.5.1", "geist": "^1.3.1", "generic-pool": "^3.9.0", "ioredis": "^5.6.0", "jose": "^6.0.8", "js-sha512": "^0.9.0", "lucide-react": "^0.469.0", "markdown-to-jsx": "^7.7.2", "nanostores": "^0.11.3", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.4", "nuqs": "^2.3.0", "pg": "^8.13.1", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.14.1", "react-medium-image-zoom": "^5.2.13", "react-scan": "^0.0.54", "react-spring": "^9.7.5", "rss": "^1.2.2", "superjson": "^2.2.2", "tinycolor2": "^1.6.0", "usehooks-ts": "^3.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.1"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@momo/eslint-config": "workspace:*", "@momo/kv": "workspace:*", "@momo/shared": "workspace:*", "@momo/tailwind-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@next/bundle-analyzer": "^15.1.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/node": "^22.10.2", "@types/react": "19.0.2", "@types/react-dom": "19.0.2", "@types/rss": "^0.0.32", "@types/tinycolor2": "^1.4.6", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.0", "postcss": "^8.4.49", "postcss-lightningcss": "^1.0.1", "postcss-load-config": "^6.0.1", "schema-dts": "^1.1.2", "sharp": "^0.33.5", "tailwindcss": "^3.4.17", "wait-on": "^8.0.2"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}}