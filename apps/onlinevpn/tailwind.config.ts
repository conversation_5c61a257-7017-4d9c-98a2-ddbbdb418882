import sharedConfig from '@momo/tailwind-config'
import { type Config } from 'tailwindcss'

const config: Config = {
  darkMode: 'class',
  content: ['./src/**/*.{js,ts,jsx,tsx,md,mdx}', '../../packages/ui/src/**/*.{js,ts,jsx,tsx}'],
  presets: [sharedConfig],
  theme: {
    extend: {
      colors: {
        'airbnb-red': '#ff385c',
        'airbnb-red-dark': '#d70466',
        'airbnb-gray': '#222222',
        'airbnb-gray-light': '#717171',
        'airbnb-gray-bg': '#f7f7f7',
        'airbnb-border': '#dddddd'
      },
      fontFamily: {
        airbnb: ['Circular', '-apple-system', 'BlinkMacSystemFont', 'Roboto', 'sans-serif']
      },
      borderRadius: {
        airbnb: '12px'
      },
      backgroundImage: {
        'nav-link-indicator':
          'radial-gradient(44.6% 825% at 50% 50%, rgb(255 133 133) 0%, rgb(255 72 109 / 0) 100%)',
        'nav-link-indicator-dark':
          'radial-gradient(44.6% 825% at 50% 50%, rgb(255 28 28) 0%, rgb(255 72 109 / 0) 100%)',
        'email-button': 'linear-gradient(180deg, rgb(210 10 30) 5%, rgb(239 90 90) 100%)',
        'hero-texture':
          'linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px), linear-gradient(rgba(255, 255, 255, 0.015) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.015) 1px, transparent 1px)'
      },
      backgroundSize: {
        'hero-texture': '40px 40px, 40px 40px, 120px 120px, 120px 120px'
      },
      boxShadow: {
        'feature-card': '0 -1px 3px 0 rgb(0 0 0 / 0.05)',
        'feature-card-dark': '0 0 0 1px rgb(255 255 255 / 0.06), 0 -1px rgb(255 255 255 / 0.1)',
        'airbnb-card': '0 6px 16px rgba(0, 0, 0, 0.12)',
        'airbnb-card-hover': '0 10px 28px rgba(0, 0, 0, 0.25)'
      },
      animation: {
        'grid-float': 'gridFloat 25s ease-in-out infinite',
        'soft-pulse': 'softPulse 4s ease-in-out infinite'
      },
      keyframes: {
        gridFloat: {
          '0%, 100%': {
            transform: 'translate(0, 0)',
            opacity: '0.6'
          },
          '50%': {
            transform: 'translate(2px, -2px)',
            opacity: '0.8'
          }
        },
        softPulse: {
          '0%, 100%': {
            opacity: '0.3',
            transform: 'scale(1)'
          },
          '50%': {
            opacity: '0.6',
            transform: 'scale(1.05)'
          }
        }
      }
    }
  }
}

export default config
