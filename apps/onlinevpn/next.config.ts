import { env } from '@momo/env'
import { withI18n } from '@momo/i18n-onlinevpn/plugin'
import { NextConfigHeaders } from '@momo/shared'
import bundleAnalyzer from '@next/bundle-analyzer'
import type { NextConfig } from 'next'
import ReactComponentName from 'react-scan/react-component-name/webpack'

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true'
})

const config: NextConfig = {
  output: 'standalone',

  // 启用生产环境源映射以改善调试体验和PageSpeed评分
  productionBrowserSourceMaps: true,

  experimental: {
    optimizePackageImports: ['shiki'],
    // 启用字体优化
    optimizeServerReact: true
  },

  eslint: {
    ignoreDuringBuilds: !!process.env.CI
  },
  typescript: {
    ignoreBuildErrors: !!process.env.CI
  },

  transpilePackages: ['@momo/*'],

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com'
      },
      {
        protocol: 'https',
        hostname: '**.googleusercontent.com'
      }
    ]
  },

  async redirects() {
    return [
      {
        source: '/pc-specs',
        destination: '/uses',
        permanent: true
      },
      {
        source: '/atom',
        destination: '/rss.xml',
        permanent: true
      },
      {
        source: '/feed',
        destination: '/rss.xml',
        permanent: true
      },
      {
        source: '/rss',
        destination: '/rss.xml',
        permanent: true
      }
    ]
  },

  async headers() {
    return NextConfigHeaders
  },

  webpack: (c, { dev, isServer }) => {
    if (env.REACT_SCAN_MONITOR_API_KEY) {
      c.plugins.push(ReactComponentName({}))
    }

    // 优化源映射配置
    if (!dev && !isServer) {
      c.devtool = 'source-map'
    }

    // CSS 优化配置
    if (!dev) {
      c.optimization = {
        ...c.optimization,
        splitChunks: {
          ...c.optimization?.splitChunks,
          cacheGroups: {
            ...c.optimization?.splitChunks?.cacheGroups,
            styles: {
              name: 'styles',
              type: 'css/mini-extract',
              chunks: 'all',
              enforce: true
            }
          }
        }
      }
    }

    return c
  }
}

export default withI18n('./i18n.config.ts', withBundleAnalyzer(config))
