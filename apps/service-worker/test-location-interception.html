<!DOCTYPE html>
<html>
<head>
    <title>Location Interception Test</title>
</head>
<body>
    <h1>Location Interception Test</h1>
    <button onclick="simulateObfuscatedCode()">Test Obfuscated Redirect</button>
    <button onclick="testLocationInterception()">Run All Tests</button>
    <div id="results"></div>

    <script>
        // 模拟你遇到的具体混淆代码
        function simulateObfuscatedCode() {
            console.log('=== Simulating Obfuscated Code ===');

            // 模拟原始混淆代码的逻辑
            function t(num, str) {
                // 模拟解混淆函数
                if (num === 810 && str === "mP7&") return "ati";
                return "";
            }

            function r(num) {
                // 模拟解混淆函数
                if (num === 2916) return "hre";
                if (num === 2386) return "htt";
                return "";
            }

            try {
                // 模拟混淆的location访问
                const locationKey = "loc" + t(810, "mP7&") + "on";
                const hrefKey = r(2916) + "f";

                console.log('Obfuscated location key:', locationKey);
                console.log('Obfuscated href key:', hrefKey);

                // 模拟混淆代码的检查逻辑
                if (!new RegExp("123av.(com|ws|gg)|1av.to|njav.tv|.pw$").test(window[locationKey].hostname)) {
                    console.log('Hostname check failed, attempting redirect...');

                    // 这就是被混淆的重定向代码
                    window[locationKey][hrefKey] = r(2386) + "ps:" + "//123av.com";

                    console.log('Redirect attempted!');
                } else {
                    console.log('Hostname check passed, no redirect needed');
                }
            } catch (e) {
                console.log('Obfuscated code failed:', e.message);
            }
        }

        // 模拟混淆代码的测试
        function testLocationInterception() {
            const results = document.getElementById('results');

            function log(message) {
                const div = document.createElement('div');
                div.textContent = message;
                results.appendChild(div);
            }

            log('=== Location Interception Test ===');

            // 测试1: 直接访问
            try {
                log('Test 1 - Direct access: ' + (typeof window.location));
                log('location.href: ' + window.location.href);
            } catch (e) {
                log('Test 1 failed: ' + e.message);
            }

            // 测试2: 模拟混淆 - 字符串拼接
            try {
                const loc = "loc";
                const ation = "ation";
                const locationStr = loc + ation;
                log('Test 2 - String concatenation: ' + locationStr);

                // 模拟混淆代码访问
                const locationObj = window[locationStr];
                log('window["location"] type: ' + typeof locationObj);
                log('window["location"].href: ' + locationObj.href);
            } catch (e) {
                log('Test 2 failed: ' + e.message);
            }

            // 测试3: 模拟函数返回字符串
            try {
                function getLocationString() {
                    return "location";
                }

                const locationObj = window[getLocationString()];
                log('Test 3 - Function return: ' + typeof locationObj);
                log('Function-based access href: ' + locationObj.href);
            } catch (e) {
                log('Test 3 failed: ' + e.message);
            }

            // 测试4: 检查__polct是否存在
            try {
                log('Test 4 - __polct exists: ' + (typeof window.__polct !== 'undefined'));
                if (window.__polct) {
                    log('__polct.href: ' + window.__polct.href);
                }
            } catch (e) {
                log('Test 4 failed: ' + e.message);
            }

            // 测试5: 模拟复杂混淆
            try {
                const parts = ["loc", "ati", "on"];
                const key = parts.join("").replace("ati", "a" + "ti");
                const locationObj = window[key];
                log('Test 5 - Complex obfuscation: ' + typeof locationObj);
                log('Complex access href: ' + locationObj.href);
            } catch (e) {
                log('Test 5 failed: ' + e.message);
            }

            // 测试6: 赋值测试
            try {
                log('Test 6 - Assignment test');
                const originalHref = window.location.href;

                // 模拟混淆的赋值
                const loc = "loc" + "ation";
                window[loc].href = "https://example.com";

                log('Assignment successful - href changed');

                // 恢复原始URL（如果可能）
                window.location.href = originalHref;
            } catch (e) {
                log('Test 6 failed: ' + e.message);
            }

            log('=== Test Complete ===');
        }

        // 等待页面加载完成后运行测试
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', testLocationInterception);
        } else {
            testLocationInterception();
        }
    </script>
</body>
</html>
