<!DOCTYPE html>
<html>
<head>
    <title>Location Interception Test</title>
</head>
<body>
    <h1>Location Interception Test</h1>
    <div id="results"></div>
    
    <script>
        // 模拟混淆代码的测试
        function testLocationInterception() {
            const results = document.getElementById('results');
            
            function log(message) {
                const div = document.createElement('div');
                div.textContent = message;
                results.appendChild(div);
            }
            
            log('=== Location Interception Test ===');
            
            // 测试1: 直接访问
            try {
                log('Test 1 - Direct access: ' + (typeof window.location));
                log('location.href: ' + window.location.href);
            } catch (e) {
                log('Test 1 failed: ' + e.message);
            }
            
            // 测试2: 模拟混淆 - 字符串拼接
            try {
                const loc = "loc";
                const ation = "ation";
                const locationStr = loc + ation;
                log('Test 2 - String concatenation: ' + locationStr);
                
                // 模拟混淆代码访问
                const locationObj = window[locationStr];
                log('window["location"] type: ' + typeof locationObj);
                log('window["location"].href: ' + locationObj.href);
            } catch (e) {
                log('Test 2 failed: ' + e.message);
            }
            
            // 测试3: 模拟函数返回字符串
            try {
                function getLocationString() {
                    return "location";
                }
                
                const locationObj = window[getLocationString()];
                log('Test 3 - Function return: ' + typeof locationObj);
                log('Function-based access href: ' + locationObj.href);
            } catch (e) {
                log('Test 3 failed: ' + e.message);
            }
            
            // 测试4: 检查__polct是否存在
            try {
                log('Test 4 - __polct exists: ' + (typeof window.__polct !== 'undefined'));
                if (window.__polct) {
                    log('__polct.href: ' + window.__polct.href);
                }
            } catch (e) {
                log('Test 4 failed: ' + e.message);
            }
            
            // 测试5: 模拟复杂混淆
            try {
                const parts = ["loc", "ati", "on"];
                const key = parts.join("").replace("ati", "a" + "ti");
                const locationObj = window[key];
                log('Test 5 - Complex obfuscation: ' + typeof locationObj);
                log('Complex access href: ' + locationObj.href);
            } catch (e) {
                log('Test 5 failed: ' + e.message);
            }
            
            // 测试6: 赋值测试
            try {
                log('Test 6 - Assignment test');
                const originalHref = window.location.href;
                
                // 模拟混淆的赋值
                const loc = "loc" + "ation";
                window[loc].href = "https://example.com";
                
                log('Assignment successful - href changed');
                
                // 恢复原始URL（如果可能）
                window.location.href = originalHref;
            } catch (e) {
                log('Test 6 failed: ' + e.message);
            }
            
            log('=== Test Complete ===');
        }
        
        // 等待页面加载完成后运行测试
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', testLocationInterception);
        } else {
            testLocationInterception();
        }
    </script>
</body>
</html>
