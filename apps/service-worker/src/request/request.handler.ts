import { RefererManager } from '../utils/referer.manager'
import { ClientManager } from '../utils/client.manager'
import { toProxyURLForServerWorker } from '@/utils/url'

// @ts-ignore
declare const self: ServiceWorkerGlobalScope

export class RequestHandler {
  // 检查请求有效性 - 不再抛出错误，而是记录警告
  checkRequest(request: Request): void {
    const contentType = request.headers.get('Content-Type')

    // 只记录警告，不中断请求
    if (request.method === 'POST' && !contentType) {
      console.warn('警告: POST 请求缺少 Content-Type 头部', request.url)
    }
  }

  // 获取Referer信息
  async getReferer(event: FetchEvent, requestUrl: string): Promise<string> {
    // 使用 ClientManager 获取当前客户端
    const client = await ClientManager.getInstance().getCurrentClient(event.clientId, requestUrl)

    // 获取 pageUrl
    const pageUrl = client?.url || event.request.referrer || requestUrl

    // 获取存储的 referer
    const storedReferer = RefererManager.getInstance().match(pageUrl)
    return storedReferer || pageUrl
  }

  // 准备代理请求
  async prepareRequest(request: Request, event: FetchEvent, headers: Headers): Promise<Request> {
    try {
      // 补充没有__pot参数的情况
      let requestUrl = request.url
      if (!requestUrl.includes('__pot=')) {
        requestUrl = toProxyURLForServerWorker(requestUrl, self.location.href)
      }

      // 获取referer信息
      const referer = await this.getReferer(event, request.url)
      headers.set('X-PO-RF', referer)

      // 为 POST 请求补充缺失的 Content-Type
      if (request.method === 'POST' && !headers.has('Content-Type')) {
        headers.set('Content-Type', 'text/plain;charset=UTF-8')
      }

      const options: RequestInit & { duplex?: 'half' } = {
        method: request.method,
        keepalive: request.keepalive,
        headers,
        mode: 'cors', // 确保使用CORS模式
        credentials: 'include', // 包含凭证
        cache: 'default'
      }

      // 处理非 GET/HEAD 请求
      if (!['GET', 'HEAD'].includes(request.method)) {
        if (headers.get('Content-Type')?.includes('application/x-www-form-urlencoded')) {
          const formData = await request.formData()
          options.body = new URLSearchParams(formData as any).toString()
        } else {
          options.body = await request.clone().text()
        }
        options.duplex = 'half'
      }

      return new Request(requestUrl, options)
    } catch (error) {
      console.error('Failed to create proxy request:', error)
      throw error
    }
  }

  // 处理OPTIONS预检请求
  handleCORS(request: Request): Response {
    const headers = new Headers()

    // 设置CORS相关头部
    let origin = '*'
    if (request.headers.has('origin')) {
      origin = request.headers.get('origin') || '*'
      headers.set('Vary', 'Origin')
    }

    headers.set('Access-Control-Allow-Origin', origin)
    headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD')
    headers.set('Access-Control-Allow-Headers', '*')
    headers.set('Access-Control-Allow-Credentials', 'true')
    headers.set('Access-Control-Max-Age', '86400') // 24小时

    return new Response(null, {
      headers: headers,
      status: 204 // No Content
    })
  }
}
