import type { BaseProcessor } from '../processors/base.processor'

// @ts-ignore
declare const self: ServiceWorkerGlobalScope

export class ResponseHandler {
  // 内容处理器数组
  private readonly processors: BaseProcessor[]

  constructor(processors: BaseProcessor[]) {
    // 直接使用传入的处理器列表
    this.processors = processors
  }

  // 设置响应头
  setupHeaders(response: Response, request?: Request): Headers {
    const headers = new Headers(response.headers)

    // 移除安全相关头部
    headers.delete('content-security-policy')
    headers.delete('content-security-policy-report-only')
    headers.delete('x-frame-options') // 允许网页被嵌入iframe
    headers.delete('x-content-type-options')

    // 添加CORS相关头部，允许跨域访问
    let originValue = '*'
    // 如果仍然没有确定origin，使用当前Service Worker的origin
    try {
      const url = new URL(self.location.href)
      originValue = url.origin
    } catch (e) {
      console.warn('[ProxyOrb] 无法确定当前origin，使用通配符')
    }

    // 检查并设置CORS头部
    // 注意：如果设置了Access-Control-Allow-Origin为非*值，必须设置Vary: Origin
    headers.set('Access-Control-Allow-Origin', originValue)
    if (originValue !== '*') {
      headers.set('Vary', 'Origin')
    }

    // 添加其他CORS标准头部
    headers.set('Access-Control-Allow-Credentials', 'true')
    headers.set('Access-Control-Allow-Headers', '*')

    // 保留原始响应的Content-Type
    const contentType = response.headers.get('content-type')
    if (contentType) {
      headers.set('Content-Type', contentType)
    }

    // 设置跨域缓存控制
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')

    return headers
  }

  // 查找合适的处理器
  findProcessor(contentType: string, url: string): BaseProcessor | undefined {
    return this.processors.find((p) => p.canProcess(contentType, url))
  }

  // 应用内容处理器
  async applyProcessor(processor: BaseProcessor, content: string, url: string): Promise<string> {
    try {
      return await processor.process(content, url)
    } catch (error) {
      console.error('[ProxyOrb] Process content failed:', error)
      return content // 处理失败时返回原始内容
    }
  }

  // 处理响应
  async processResponse(request: Request, response: Response): Promise<Response> {
    try {
      // 1. 克隆 response，这样我们就可以多次读取
      const responseClone = response.clone()

      // 2. 检查重定向响应
      if (response.redirected) {
        return this.handleRedirectResponse(request, response)
      }

      // 3. 创建带有CORS头部的响应头
      const headers = this.setupHeaders(responseClone, request)
      const contentType = responseClone.headers.get('Content-Type') || ''

      // 4. 检查状态码，对于无消息体的状态码进行特殊处理
      const noBodyStatusCodes = [204, 205, 304]
      if (noBodyStatusCodes.includes(response.status)) {
        return new Response(null, {
          headers,
          status: response.status,
          statusText: response.statusText
        })
      }

      // 5. 查找合适的处理器
      const processor = this.findProcessor(contentType, request.url)
      if (processor) {
        try {
          const content = await responseClone.text()
          const processedContent = await this.applyProcessor(processor, content, request.url)
          return new Response(processedContent, {
            headers,
            status: response.status,
            statusText: response.statusText
          })
        } catch (processingError) {
          console.error('[ProxyOrb] Process content failed:', processingError)
          // 处理失败时，返回原始响应但仍添加CORS头部
          return new Response(response.body, {
            headers,
            status: response.status,
            statusText: response.statusText
          })
        }
      }

      // 6. 对于无需处理的响应，使用原始的response但添加CORS头部
      return new Response(response.body, {
        headers,
        status: response.status,
        statusText: response.statusText
      })
    } catch (error) {
      console.error('[ProxyOrb] Process response failed:', error)
      throw error
    }
  }

  // 处理重定向响应
  private handleRedirectResponse(request: Request, response: Response): Response {
    // 对于导航请求，需要特殊处理让浏览器知道URL变化
    if (request.mode === 'navigate') {
      // 获取重定向后的URL
      const redirectUrl = response.url
      console.log(`[ProxyOrb] redirect: ${request.url} -> ${redirectUrl}`)

      // 创建一个新的响应，包含Location头以通知浏览器进行页面重定向
      const headers = this.setupHeaders(response, request)
      headers.set('Location', redirectUrl)

      // 根据原始响应的类型返回相应的重定向状态码
      // 由于fetch API会自动跟随重定向，所以我们无法获取原始状态码
      // 但是我们可以根据请求方法做出合理的选择
      let status = 302 // 默认临时重定向
      let statusText = 'Found'

      // 如果是POST、PUT等非安全方法，使用307确保方法不变
      if (request.method !== 'GET' && request.method !== 'HEAD') {
        status = 307
        statusText = 'Temporary Redirect'
      }

      return new Response(null, {
        status,
        statusText,
        headers
      })
    }

    // 对于非导航请求，直接处理重定向后的响应
    return new Response(response.body, {
      headers: this.setupHeaders(response, request),
      status: response.status,
      statusText: response.statusText
    })
  }

  // 创建错误响应
  createErrorReply(error: Error): Response {
    const errorResponse = {
      error: 'Proxy request failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    })
  }
}
