import { SETTINGS_SKIP_SCHEMES } from '@/config/constants'
import type { BaseProcessor } from '../processors/base.processor'
import { RequestHandler } from './request.handler'
import { ResponseHandler } from './response.handler'

// @ts-ignore
declare const self: ServiceWorkerGlobalScope

export class ProxyFetch {
  private requestHandler: RequestHandler
  private responseHandler: ResponseHandler

  constructor(processors: BaseProcessor[]) {
    this.requestHandler = new RequestHandler()
    this.responseHandler = new ResponseHandler(processors)
  }

  async processRequest(request: Request, event: FetchEvent): Promise<Response> {
    try {
      this.requestHandler.checkRequest(request)

      // 解析请求URL
      const headers = new Headers(request.headers)
      const proxyRequest = await this.requestHandler.prepareRequest(request, event, headers)
      const response = await fetch(proxyRequest)

      // 检查是否内部页面
      if (response.headers.get('X-PO-Page')) {
        return response
      }

      return await this.responseHandler.processResponse(request, response)
    } catch (error) {
      console.error('Proxy request failed:', error)
      return this.responseHandler.createErrorReply(error as Error)
    }
  }

  // handleFetchEvent 方法简化为
  async onFetch(event: FetchEvent): Promise<Response> {
    const request = event.request

    // 1. 检查OPTIONS预检请求 - 优先处理
    if (request.method === 'OPTIONS') {
      return this.requestHandler.handleCORS(request)
    }

    // 2. 检查是否需要跳过处理
    if (SETTINGS_SKIP_SCHEMES.some((skip) => request.url.startsWith(skip))) {
      return fetch(request)
    }

    // 3. 检查是否需要跳过处理(浏览器默认会请求代理服务器的favicon.ico)
    if (!request.url.includes('__pot=') && request.url.endsWith('favicon.ico')) {
      return new Response(null, { status: 404 })
    }

    try {
      return await this.processRequest(request, event)
    } catch (error) {
      console.error('Fetch event handling failed:', error)
      return fetch(request)
    }
  }
}
