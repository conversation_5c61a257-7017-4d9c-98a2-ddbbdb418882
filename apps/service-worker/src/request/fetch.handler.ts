import { SETTINGS_SKIP_SCHEMES } from '@/config/constants'
import type { BaseProcessor } from '../processors/base.processor'
import { RequestHandler } from './request.handler'
import { ResponseHandler } from './response.handler'

// @ts-ignore
declare const self: ServiceWorkerGlobalScope

export class ProxyFetch {
  private requestHandler: RequestHandler
  private responseHandler: ResponseHandler

  constructor(processors: BaseProcessor[]) {
    this.requestHandler = new RequestHandler()
    this.responseHandler = new ResponseHandler(processors)
  }

  async processRequest(request: Request, event: FetchEvent): Promise<Response> {
    try {
      this.requestHandler.checkRequest(request)

      // 解析请求URL
      const headers = new Headers(request.headers)
      const proxyRequest = await this.requestHandler.prepareRequest(request, event, headers)
      const response = await fetch(proxyRequest)

      // 检查是否内部页面
      if (response.headers.get('X-PO-Page')) {
        return response
      }

      return await this.responseHandler.processResponse(request, response)
    } catch (error) {
      console.error('Proxy request failed:', error)
      return this.responseHandler.createErrorReply(error as Error)
    }
  }

  // handleFetchEvent 方法简化为
  async onFetch(event: FetchEvent): Promise<Response> {
    const request = event.request

    // 1. 检查OPTIONS预检请求 - 优先处理
    if (request.method === 'OPTIONS') {
      return this.requestHandler.handleCORS(request)
    }

    // 2. 拦截恶意导航请求 - 新增关键功能
    if (request.mode === 'navigate') {
      const navigationResult = await this.handleNavigationRequest(request, event)
      if (navigationResult) {
        return navigationResult
      }
    }

    // 3. 检查是否需要跳过处理
    if (SETTINGS_SKIP_SCHEMES.some((skip) => request.url.startsWith(skip))) {
      return fetch(request)
    }

    // 4. 检查是否需要跳过处理(浏览器默认会请求代理服务器的favicon.ico)
    if (!request.url.includes('__pot=') && request.url.endsWith('favicon.ico')) {
      return new Response(null, { status: 404 })
    }

    try {
      return await this.processRequest(request, event)
    } catch (error) {
      console.error('Fetch event handling failed:', error)
      return fetch(request)
    }
  }

  /**
   * 处理导航请求，拦截恶意重定向
   */
  private async handleNavigationRequest(request: Request, event: FetchEvent): Promise<Response | null> {
    try {
      const requestUrl = new URL(request.url)
      const currentOrigin = new URL(self.location.href).origin

      console.log('[NavigationInterceptor] Navigation request detected:', request.url)

      // 检查是否是恶意重定向目标
      const maliciousDomains = [
        '123av.com',
        '123av.ws',
        '123av.gg',
        '1av.to',
        'njav.tv'
      ]

      const isMaliciousRedirect = maliciousDomains.some(domain =>
        requestUrl.hostname.includes(domain)
      )

      if (isMaliciousRedirect) {
        console.warn('[NavigationInterceptor] *** BLOCKED MALICIOUS NAVIGATION ***:', request.url)

        // 获取当前页面的客户端
        const client = await self.clients.get(event.clientId)
        if (client) {
          // 通知客户端显示拦截信息
          client.postMessage({
            type: 'navigation-blocked',
            blockedUrl: request.url,
            reason: 'Malicious redirect detected'
          })
        }

        // 返回一个阻止页面，而不是执行重定向
        const blockPage = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Navigation Blocked</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 50px;
                background: #f0f0f0;
              }
              .warning {
                background: #ff4444;
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin: 20px auto;
                max-width: 600px;
              }
              .details {
                background: white;
                padding: 20px;
                border-radius: 10px;
                margin: 20px auto;
                max-width: 600px;
                border: 1px solid #ddd;
              }
            </style>
          </head>
          <body>
            <div class="warning">
              <h1>🛡️ Navigation Blocked</h1>
              <p>A malicious redirect attempt has been detected and blocked.</p>
            </div>
            <div class="details">
              <h3>Details:</h3>
              <p><strong>Blocked URL:</strong> ${request.url}</p>
              <p><strong>Reason:</strong> This appears to be an unwanted redirect to a potentially malicious site.</p>
              <p><strong>Action:</strong> The navigation has been blocked for your security.</p>
              <button onclick="history.back()">← Go Back</button>
            </div>
          </body>
          </html>
        `

        return new Response(blockPage, {
          status: 200,
          headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'X-Navigation-Blocked': 'true'
          }
        })
      }

      // 检查是否是代理域名内的导航（正常情况）
      if (requestUrl.origin === currentOrigin) {
        console.log('[NavigationInterceptor] Same-origin navigation, allowing:', request.url)
        return null // 让正常流程处理
      }

      // 检查是否是外部导航但没有代理参数
      if (!requestUrl.searchParams.has('__pot')) {
        console.log('[NavigationInterceptor] External navigation without proxy params:', request.url)

        // 可以选择将外部导航转换为代理请求
        // 但这里我们先记录并允许，避免破坏正常功能
        return null
      }

      return null // 其他情况让正常流程处理

    } catch (error) {
      console.error('[NavigationInterceptor] Error handling navigation:', error)
      return null // 出错时让正常流程处理
    }
  }
}
