import { SETTINGS_SKIP_SCHEMES } from '@/config/constants'
import type { BaseProcessor } from '../processors/base.processor'
import { RequestHandler } from './request.handler'
import { ResponseHandler } from './response.handler'

// @ts-ignore
declare const self: ServiceWorkerGlobalScope

export class ProxyFetch {
  private requestHandler: RequestHandler
  private responseHandler: ResponseHandler

  constructor(processors: BaseProcessor[]) {
    this.requestHandler = new RequestHandler()
    this.responseHandler = new ResponseHandler(processors)
  }

  async processRequest(request: Request, event: FetchEvent): Promise<Response> {
    try {
      this.requestHandler.checkRequest(request)

      // 解析请求URL
      const headers = new Headers(request.headers)
      const proxyRequest = await this.requestHandler.prepareRequest(request, event, headers)
      const response = await fetch(proxyRequest)

      // 检查是否内部页面
      if (response.headers.get('X-PO-Page')) {
        return response
      }

      return await this.responseHandler.processResponse(request, response)
    } catch (error) {
      console.error('Proxy request failed:', error)
      return this.responseHandler.createErrorReply(error as Error)
    }
  }

  // handleFetchEvent 方法简化为
  async onFetch(event: FetchEvent): Promise<Response> {
    const request = event.request

    // 1. 检查OPTIONS预检请求 - 优先处理
    if (request.method === 'OPTIONS') {
      return this.requestHandler.handleCORS(request)
    }

    // 2. 拦截导航请求 - 关键的恶意重定向拦截
    if (request.mode === 'navigate') {
      const navigationResult = await this.handleNavigationRequest(request, event)
      if (navigationResult) {
        return navigationResult
      }
    }

    // 3. 检查是否需要跳过处理
    if (SETTINGS_SKIP_SCHEMES.some((skip) => request.url.startsWith(skip))) {
      return fetch(request)
    }

    // 4. 检查是否需要跳过处理(浏览器默认会请求代理服务器的favicon.ico)
    if (!request.url.includes('__pot=') && request.url.endsWith('favicon.ico')) {
      return new Response(null, { status: 404 })
    }

    try {
      return await this.processRequest(request, event)
    } catch (error) {
      console.error('Fetch event handling failed:', error)
      return fetch(request)
    }
  }

  /**
   * 处理导航请求，拦截恶意重定向
   * 这是解决混淆代码绕过location拦截的关键方法
   */
  private async handleNavigationRequest(request: Request, event: FetchEvent): Promise<Response | null> {
    try {
      const requestUrl = new URL(request.url)

      console.log('[NavigationInterceptor] Navigation request detected:', request.url)

      // 定义恶意重定向目标域名列表
      const maliciousDomains = [
        '123av.com',
        '123av.ws',
        '123av.gg',
        '1av.to',
        'njav.tv'
      ]

      // 检查是否是恶意重定向
      const isMaliciousRedirect = maliciousDomains.some(domain =>
        requestUrl.hostname.includes(domain) || requestUrl.hostname.endsWith(domain)
      )

      if (isMaliciousRedirect) {
        console.warn('[NavigationInterceptor] *** BLOCKED MALICIOUS NAVIGATION ***:', request.url)

        // 创建拦截页面
        const blockPage = this.createBlockedNavigationPage(request.url)

        return new Response(blockPage, {
          status: 200,
          headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'X-Navigation-Blocked': 'true',
            'X-Blocked-URL': request.url
          }
        })
      }

      // 对于其他导航请求，让正常流程处理
      return null

    } catch (error) {
      console.error('[NavigationInterceptor] Error handling navigation:', error)
      return null
    }
  }

  /**
   * 创建被阻止导航的页面
   */
  private createBlockedNavigationPage(blockedUrl: string): string {
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>导航已被阻止</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
          }
          .shield {
            font-size: 64px;
            margin-bottom: 20px;
          }
          h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
          }
          .warning {
            background: #ff4757;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
          }
          .details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
          }
          .url {
            word-break: break-all;
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
          }
          .actions {
            margin-top: 30px;
          }
          button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background 0.3s;
          }
          button:hover {
            background: #5a6fd8;
          }
          .secondary {
            background: #6c757d;
          }
          .secondary:hover {
            background: #5a6268;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="shield">🛡️</div>
          <h1>恶意重定向已被阻止</h1>

          <div class="warning">
            <strong>安全警告：</strong>检测到可疑的重定向尝试，已自动阻止以保护您的安全。
          </div>

          <div class="details">
            <h3>详细信息：</h3>
            <p><strong>被阻止的URL：</strong></p>
            <div class="url">${blockedUrl}</div>
            <p><strong>阻止原因：</strong>该URL被识别为潜在的恶意重定向目标。</p>
            <p><strong>处理方式：</strong>导航请求已被Service Worker拦截并阻止。</p>
          </div>

          <div class="actions">
            <button onclick="history.back()">← 返回上一页</button>
            <button class="secondary" onclick="window.close()">关闭页面</button>
          </div>
        </div>

        <script>
          // 记录拦截事件
          console.log('[NavigationBlocked] Malicious redirect blocked:', '${blockedUrl}');

          // 可以添加更多的客户端逻辑
          document.addEventListener('DOMContentLoaded', function() {
            // 显示拦截成功的通知
            setTimeout(() => {
              const notification = document.createElement('div');
              notification.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: bold;
              \`;
              notification.textContent = '✅ 恶意重定向已成功拦截';
              document.body.appendChild(notification);

              setTimeout(() => notification.remove(), 5000);
            }, 500);
          });
        </script>
      </body>
      </html>
    `
  }
}
