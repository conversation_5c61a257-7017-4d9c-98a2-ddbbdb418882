// import { describe, expect, it } from 'vitest'
// import { SETTINGS_URL_PATTERN } from './constants'

// describe('SETTINGS_URL_PATTERN', () => {
//   const pattern = SETTINGS_URL_PATTERN()

//   describe('HTML attributes with URLs', () => {
//     it('should match URLs in img src with quotes', () => {
//       const html = '<img src="https://example.com/image.jpg">'
//       const matches = [...html.matchAll(pattern)]
//       const url = matches[0]?.groups?.url
//       expect(url).toContain('https://example.com/image.jpg')
//       expect(matches.length).toBeGreaterThan(0)
//     })

//     it('should match URLs in img src without quotes', () => {
//       const html = '<img src=https://example.com/image.jpg>'
//       const matches = [...html.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com/image.jpg')
//       expect(matches.length).toBeGreaterThan(0)
//     })

//     it('should match URLs in anchor href with quotes', () => {
//       const html = '<a href="/path/to/page">'
//       const matches = [...html.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('/path/to/page')
//       expect(matches.length).toBeGreaterThan(0)
//     })
//   })

//   describe('data attributes', () => {
//     it('should match URLs in data-url with quotes', () => {
//       const html = '<div data-url="https://example.com">'
//       const matches = [...html.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com')
//       expect(matches.length).toBeGreaterThan(0)
//     })

//     it('should match URLs in data-url without quotes', () => {
//       const html = '<div data-url=https://example.com>'
//       const matches = [...html.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com')
//       expect(matches.length).toBeGreaterThan(0)
//     })
//   })

//   describe('CSS url()', () => {
//     it('should match URLs in url() with quotes', () => {
//       const css = 'background-image: url("https://example.com/bg.jpg")'
//       const matches = [...css.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com/bg.jpg')
//       expect(matches.length).toBeGreaterThan(0)
//     })

//     it('should match URLs in url() without quotes', () => {
//       const css = 'background-image: url(https://example.com/bg.jpg)'
//       const matches = [...css.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com/bg.jpg')
//       expect(matches.length).toBeGreaterThan(0)
//     })
//   })

//   describe('String literals', () => {
//     it('should match quoted URLs', () => {
//       const text = '"https://example.com/page"'
//       const matches = [...text.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com/page')
//       expect(matches.length).toBeGreaterThan(0)
//     })

//     it('should match unquoted URLs', () => {
//       const text = 'https://example.com/page'
//       const matches = [...text.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('https://example.com/page')
//       expect(matches.length).toBeGreaterThan(0)
//     })

//     it('should match relative paths', () => {
//       const text = '"/path/to/resource"'
//       const matches = [...text.matchAll(pattern)]
//       expect(matches[0]?.groups?.url).toBe('/path/to/resource')
//       expect(matches.length).toBeGreaterThan(0)
//     })
//   })
// })
