interface SETTINGS_DOMAttributes {
  [key: string]: string
}

export const SETTINGS_DOM_ATTRIBUTES = {
  // 可点击的
  a: 'href',
  area: 'href',
  form: 'action',
  input: 'src',
  //   video: 'src',
  //   audio: 'src',
  //   source: 'src',
  //   track: 'src',
  iframe: 'src'
  //   embed: 'src',
  //   img: 'src',
  //   link: 'href',
  //   script: 'src'
} as const satisfies SETTINGS_DOMAttributes

export const SETTINGS_SKIP_SCHEMES: string[] = [
  // 浏览器扩展相关
  'chrome-extension://',
  'chrome-untrusted://',
  'moz-extension://',
  'edge-extension://',

  // 浏览器内部页面
  'chrome://',
  'about:',
  'data:',
  'file:',

  // 开发调试相关
  'webpack://',

  // 常见的邮件、电话、短信服务
  'mailto:',
  'tel:',
  'sms:'
] as const

// 流媒体内容类型
export const STREAMING_CONTENT_TYPES = [
  'application/vnd.apple.mpegurl',
  'application/x-mpegurl',
  'application/dash+xml',
  'application/vnd.ms-sstr+xml',
  'video/mp2t',
  'application/mp4'
] as const

// PostMessage 拦截相关常量
export const POST_MESSAGE = {
  // 数据和来源的封装键名
  DATA_KEY: '__po_data',
  ORIGIN_KEY: '__po_origin',

  // 辅助函数名称
  PREPARE_DATA_FUNCTION: '__poPreparePostMessageData',
  PREPARE_ORIGIN_FUNCTION: '__poPreparePostMessageOrigin'
} as const

export const SETTINGS_URL_PATTERN = (): RegExp => {
  const attributeGroups: Record<string, string[]> = {}
  Object.entries(SETTINGS_DOM_ATTRIBUTES).forEach(([tag, attr]) => {
    // @ts-ignore
    if (!attributeGroups[attr]) {
      // @ts-ignore
      attributeGroups[attr] = []
    }
    // @ts-ignore
    attributeGroups[attr].push(tag)
  })

  const patterns = [
    // 1. 处理标签属性，支持有引号、无引号、转义等多种情况
    ...Object.entries(attributeGroups).map(
      ([attr, tags]) =>
        `<\\s*(?:${tags.join('|')})\\b(?:(?!>)[\\s\\S])*\\b${attr}\\s*=\\s*(?:\\\\["'](?<url>.*?)(?:\\\\)?["']|["'](?<url>.*?)["']|(?<url>[^\\s>]*))`
    )
    // 2. 处理 data- 属性，支持有引号和无引号的情况
    // `data-(?:href|url|link)=(?:["'](?<url>[^"'>\\s]+)["']|(?<url>[^\\s>"']+))`,
    // 3. 处理 CSS url()，支持有引号、无引号和括号的情况
    // `url\\((?:["'](?<url>[^"'()\\s]+)["']|(?<url>[^"'()\\s]+))\\)`
  ].join('|')

  return new RegExp(patterns, 'gi')
}

// 代理服务器专用的 cookie 名称列表
export const PROXY_RESERVED_COOKIES = ['__poss', '__poex'] as readonly string[]
