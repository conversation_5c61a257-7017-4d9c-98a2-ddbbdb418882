declare global {
  interface Window {
    __processUrl: (url: string | null | undefined) => string
    __polct: Location
    __proxyInjected?: boolean
  }

  interface Document {
    __polct: Location
    __poOriginalReferrer: string
    __poOriginalCookie: string
  }

  interface XMLHttpRequest {
    __poOriginalUrl: string
  }

  interface TargetConfig {
    protocol: string
    hostname: string
    port: string
  }

  interface InterceptorConfig {
    proxyOrigin: string
    pot: string
    targetUrl: string
    target: TargetConfig
    serviceWorkerUrl: string
  }

  // 为 iframe 添加自定义属性
  interface HTMLIFrameElement {
    _originalSandbox?: string
    __intercepted?: boolean
  }
}

export {}
