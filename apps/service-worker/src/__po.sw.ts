// @ts-ignore
declare const self: ServiceWorkerGlobalScope

// Service Worker 入口文件
import { ProxyFetch } from './request/fetch.handler'
import { ProxyHTMLProcessor } from './processors/html.processor'
import { ProxyManifestProcessor } from './processors/manifest.processor'
import { ProxyJSProcessor } from './processors/js.processor'
import { RefererManager } from './utils/referer.manager'
import { PostMessageManager } from './utils/post-message.manager'
import { ClientManager } from './utils/client.manager'
import { SW_VERSION } from './config/version'
import type { BaseProcessor } from './processors/base.processor'

// Initialize processors
const processors: BaseProcessor[] = [
  new ProxyHTMLProcessor(),
  new ProxyJSProcessor(),
  new ProxyManifestProcessor()
]

// Initialize fetch handler
const fetchHandler = new ProxyFetch(processors)

// 初始化 PostMessageManager
const postMessageManager = PostMessageManager.getInstance()
postMessageManager.setupServiceWorkerInterception(self)

// 初始化 ClientManager
const clientManager = ClientManager.getInstance()

// Service Worker Event Handlers
self.addEventListener('install', () => {
  console.log('[SW] Installing new version:', SW_VERSION)
  try {
    void self.skipWaiting()
    console.log('[SW] skipWaiting called successfully')
  } catch (error) {
    console.error('[SW] Error during skipWaiting:', error)
  }
})

// @ts-ignore
self.addEventListener('activate', (event: ExtendableEvent) => {
  console.log('[SW] Activating new version:', SW_VERSION)
  event.waitUntil(
    Promise.all([
      // 1. 禁用 Navigation Preload
      (async () => {
        if (self.registration.navigationPreload) {
          try {
            await self.registration.navigationPreload.disable()
            console.log('[SW] Navigation Preload disabled successfully')
          } catch (error) {
            console.error('[SW] Error disabling Navigation Preload:', error)
          }
        }
      })(),

      // 2. 声明 clients, 接管已打开的页面
      self.clients
        .claim()
        .then(() => {
          console.log('[SW] Clients claimed successfully')

          // 通知所有客户端 Service Worker 已就绪
          clientManager
            .broadcastMessage({
              type: 'swready',
              version: SW_VERSION
            })
            .then((count) => {
              console.log(`[SW] Sent 'swready' message to ${count} clients`)
            })
        })
        .catch((error) => {
          console.error('[SW] Error during clients.claim:', error)
        })
    ])
  )
})

// 添加消息处理
self.addEventListener('message', (event: ExtendableMessageEvent) => {
  const data = postMessageManager.unwrapMessageData(event.data)

  // 处理 SET_REFERER 消息
  if (data?.type === 'SET_REFERER') {
    RefererManager.getInstance().set(data.url, data.referer)
    console.log('[SW] SET_REFERER:', data.url, 'referer:', data.referer)
  }

  // 处理 CSP_FRAME 消息（iframe 通信）
  else if (data?.type === 'csp_frame') {
    console.log('[SW] Received frame info:', data.pageUrl, 'isTop:', data.isTop)

    // 可以在这里添加 iframe 特定的逻辑
  }

  // 处理导航消息
  else if (data?.type === 'navigate' && event.source) {
    try {
      const targetUrl = data.url || ''
      // @ts-ignore - 类型兼容性问题
      event.source.navigate(targetUrl).catch((error: Error) => {
        console.error('[SW] Navigation error:', error)
      })
    } catch (error) {
      console.error('[SW] Error processing navigate message:', error)
    }
  }
})

self.addEventListener(
  'fetch',
  (event: FetchEvent) => {
    event.stopPropagation()
    event.stopImmediatePropagation()

    event.respondWith(
      fetchHandler.onFetch(event).catch((error) => {
        console.error('[SW] Fetch error:', error, 'for URL:', event.request.url)
        throw error // 重新抛出错误以便浏览器可以处理
      })
    )
  },
  true
)
