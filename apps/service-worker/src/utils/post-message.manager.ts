import { toProxyURL } from './url'
import { POST_MESSAGE } from '@/config/constants'

// 为辅助函数扩展全局类型
declare global {
  interface Window {
    [key: string]: any
  }
  interface ServiceWorkerGlobalScope {
    [key: string]: any
  }
}

/**
 * PostMessage 管理器
 *
 * 负责处理 postMessage 的拦截和代理操作，提供统一的接口给页面拦截器和 Service Worker 使用
 *
 * 主要功能:
 * 1. 拦截 window.postMessage 方法，处理目标源参数
 * 2. 重写 MessageEvent.data 和 MessageEvent.origin 属性的 getter 方法
 * 3. 提供辅助函数用于 JS 代码中 postMessage 调用的处理
 * 4. 实现消息数据的封装和解封装逻辑
 *
 * 参考了 croxyproxy 项目的实现，但进行了以下改进：
 * - 使用 TypeScript 类型系统提高代码安全性
 * - 分离页面和 Service Worker 的拦截逻辑
 * - 提供统一的消息数据处理接口
 * - 更好地支持现代浏览器中 postMessage 的两种调用方式
 */
export class PostMessageManager {
  private static instance: PostMessageManager | null = null

  // 数据和来源的封装键名
  private readonly dataKey = POST_MESSAGE.DATA_KEY
  private readonly originKey = POST_MESSAGE.ORIGIN_KEY

  // 辅助函数名称
  public readonly prepareDataFunctionName = POST_MESSAGE.PREPARE_DATA_FUNCTION
  public readonly prepareOriginFunctionName = POST_MESSAGE.PREPARE_ORIGIN_FUNCTION

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): PostMessageManager {
    if (!PostMessageManager.instance) {
      PostMessageManager.instance = new PostMessageManager()
    }
    return PostMessageManager.instance
  }

  /**
   * 初始化页面的 postMessage 拦截
   * @param window 窗口对象
   * @param targetOrigin 目标来源信息
   */
  public setupPageInterception(
    window: Window,
    targetOrigin: { protocol: string; hostname: string }
  ): void {
    const self = this

    // 保存原始的 postMessage 方法
    const originalPostMessage = window.postMessage.bind(window)

    // 添加辅助函数到全局对象
    this.addHelperFunctions(window, targetOrigin.protocol + '//' + targetOrigin.hostname)

    // 监听接收到的消息事件
    window.addEventListener(
      'message',
      function (event) {
        // 处理特定消息类型
        if (self.isUserTrustedEvent(event)) {
          const data = self.unwrapMessageData(event.data)

          if (data && typeof data === 'object' && typeof data.type === 'string') {
            // 处理导航消息
            if (data.type === 'navigate') {
              const targetUrl = event.origin === 'null' ? data.url : self.processUrl(data.url || '')
              window.location.href = targetUrl
            }
            // 处理 Service Worker 就绪消息
            else if (data.type === 'swready') {
              console.log('[PostMessage] Service worker content script is ready')
              // 检查是否在 iframe 中
              if (self.isInIframe(window)) {
                try {
                  // 向 Service Worker 发送 iframe 信息
                  const source = event.source as Window
                  if (source && typeof source.postMessage === 'function') {
                    source.postMessage(
                      {
                        type: 'csp_frame',
                        pageUrl: window.location.toString(),
                        isTop: false,
                        origin: window.location.origin
                      },
                      '*' as any
                    )
                  }
                } catch (error) {
                  console.error('[PostMessage] Error sending frame info:', error)
                }
              }
            }
          }
        }

        // 重写 event.origin，使其返回目标协议和主机名
        Object.defineProperty(event, 'origin', {
          get: () => targetOrigin.protocol + '//' + targetOrigin.hostname
        })
      },
      true // 在捕获阶段处理
    )

    // 重写 window.postMessage 方法
    // 需要考虑两种重载：旧的 (message, targetOrigin, transfer?) 和新的 (message, options?)
    const newPostMessage = function (
      message: any,
      targetOriginOrOptions: string | WindowPostMessageOptions | undefined,
      transfer?: Transferable[]
    ): void {
      // 处理新的方式调用 window.postMessage(message, { targetOrigin })
      if (targetOriginOrOptions && typeof targetOriginOrOptions === 'object') {
        // 直接传递选项对象给原始方法
        return originalPostMessage(message, targetOriginOrOptions)
      }

      // 处理旧的方式调用 window.postMessage(message, targetOrigin, transfer?)
      let targetOrigin = targetOriginOrOptions as string
      if (targetOrigin !== '*') {
        // 对目标来源进行处理
        targetOrigin = self.processUrl(targetOrigin) || targetOrigin
      }
      const finalTargetOrigin = targetOrigin || '*'

      // 调用原始的 postMessage 方法
      if (transfer) {
        return originalPostMessage(message, finalTargetOrigin, transfer)
      } else {
        return originalPostMessage(message, finalTargetOrigin)
      }
    }

    // 赋值给 window.postMessage
    window.postMessage = newPostMessage as typeof window.postMessage

    // 设置页面消息事件拦截
    this.setupWindowMessageInterception(window)
  }

  /**
   * 初始化 Service Worker 的 postMessage 拦截
   * @param swGlobal Service Worker 的 self 对象
   */
  public setupServiceWorkerInterception(swGlobal: ServiceWorkerGlobalScope): void {
    // 添加辅助函数到 ServiceWorker 全局对象
    if (typeof swGlobal.location !== 'undefined') {
      this.addHelperFunctions(swGlobal as unknown as Window, swGlobal.location.origin)
    }

    // 设置 Service Worker 消息事件拦截
    this.setupServiceWorkerMessageInterception(swGlobal)
  }

  /**
   * 设置页面环境的消息事件拦截
   * @param context Window 对象
   */
  private setupWindowMessageInterception(context: Window): void {
    // 获取数据时的拦截函数
    const getDataInterceptor = (originalGetter: () => any): any => {
      const originalData = originalGetter()
      return this.unwrapMessageData(originalData)
    }

    // 获取来源时的拦截函数
    const getOriginInterceptor = function (this: any, originalGetter: () => any): any {
      const originalData = this.__poOriginalData

      if (PostMessageManager.getInstance().isWrappedData(originalData)) {
        // 如果是封装的数据，返回其来源
        return originalData[PostMessageManager.getInstance().originKey]
      } else if (this.source && this.source.location) {
        // 尝试从 source 对象获取来源
        const sourceUrl = this.source.location.href
        try {
          return new URL(sourceUrl).origin
        } catch (e) {
          return originalGetter()
        }
      } else {
        return originalGetter()
      }
    }

    // 拦截 MessageEvent
    if ('MessageEvent' in context) {
      try {
        this.definePropertyOverride(
          (context as any).MessageEvent.prototype,
          'data',
          getDataInterceptor,
          function () {}
        )
      } catch (error) {
        console.error('[PostMessageManager] Error overriding MessageEvent.data:', error)
      }

      try {
        this.definePropertyOverride(
          (context as any).MessageEvent.prototype,
          'origin',
          getOriginInterceptor,
          function () {}
        )
      } catch (error) {
        console.error('[PostMessageManager] Error overriding MessageEvent.origin:', error)
      }
    }
  }

  /**
   * 设置 Service Worker 环境的消息事件拦截
   * @param context ServiceWorkerGlobalScope 对象
   */
  private setupServiceWorkerMessageInterception(context: ServiceWorkerGlobalScope): void {
    // 获取数据时的拦截函数
    const getDataInterceptor = (originalGetter: () => any): any => {
      const originalData = originalGetter()
      return this.unwrapMessageData(originalData)
    }

    // 获取来源时的拦截函数
    const getOriginInterceptor = function (this: any, originalGetter: () => any): any {
      const originalData = this.__poOriginalData

      if (PostMessageManager.getInstance().isWrappedData(originalData)) {
        // 如果是封装的数据，返回其来源
        return originalData[PostMessageManager.getInstance().originKey]
      } else {
        return originalGetter()
      }
    }

    // 拦截 ExtendableMessageEvent (Service Worker)
    if ('ExtendableMessageEvent' in context) {
      try {
        this.definePropertyOverride(
          (context as any).ExtendableMessageEvent.prototype,
          'data',
          getDataInterceptor,
          function () {}
        )
      } catch (error) {
        console.error('[PostMessageManager] Error overriding ExtendableMessageEvent.data:', error)
      }

      try {
        this.definePropertyOverride(
          (context as any).ExtendableMessageEvent.prototype,
          'origin',
          getOriginInterceptor,
          function () {}
        )
      } catch (error) {
        console.error('[PostMessageManager] Error overriding ExtendableMessageEvent.origin:', error)
      }
    }
  }

  /**
   * 添加辅助函数到全局对象
   * @param context 全局对象 (Window 或 ServiceWorkerGlobalScope)
   * @param originValue 来源值
   */
  private addHelperFunctions(
    context: Window | ServiceWorkerGlobalScope,
    originValue: string
  ): void {
    const self = this

    // 添加准备数据的辅助函数
    context[this.prepareDataFunctionName] = function (data: any): any {
      if ('Window' in context) {
        const wrappedData: Record<string, any> = {}
        wrappedData[self.dataKey] = self.unwrapMessageData(data)
        wrappedData[self.originKey] = originValue
        return wrappedData
      } else {
        return data
      }
    }

    // 添加准备目标来源的辅助函数
    context[this.prepareOriginFunctionName] = function (targetOrigin: any): any {
      if (
        'Window' in context &&
        (typeof targetOrigin === 'string' || targetOrigin instanceof String)
      ) {
        return '*'
      } else {
        return targetOrigin
      }
    }
  }

  /**
   * 封装消息数据
   * @param data 原始数据
   * @param origin 来源 URL
   * @returns 封装后的数据
   */
  public wrapMessageData(data: any, origin: string): any {
    if (!data) return data

    // 避免重复封装
    if (this.isWrappedData(data)) {
      return data
    }

    const wrappedData: Record<string, any> = {}
    wrappedData[this.dataKey] = data
    wrappedData[this.originKey] = origin
    return wrappedData
  }

  /**
   * 解封装消息数据
   * @param data 可能被封装的数据
   * @returns 解封装后的数据
   */
  public unwrapMessageData(data: any): any {
    if (!data) return data

    // 如果是封装的数据，提取实际数据
    if (this.isWrappedData(data)) {
      return data[this.dataKey]
    }

    // 递归处理数组
    if (Array.isArray(data)) {
      for (let i = 0; i < data.length; i++) {
        if (this.isWrappedData(data[i])) {
          data[i] = data[i][this.dataKey]
        } else {
          // 递归处理嵌套的数组项
          this.unwrapMessageData(data[i])
        }
      }
    }
    // 递归处理对象
    else if (typeof data === 'object' && data !== null) {
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          if (this.isWrappedData(data[key])) {
            data[key] = data[key][this.dataKey]
          } else {
            // 递归处理嵌套的对象属性
            this.unwrapMessageData(data[key])
          }
        }
      }
    }

    return data
  }

  /**
   * 获取封装数据的原始来源
   * @param data 可能被封装的数据
   * @returns 如果是封装数据，返回其来源；否则返回 null
   */
  public getWrappedOrigin(data: any): string | null {
    if (this.isWrappedData(data)) {
      return data[this.originKey]
    }
    return null
  }

  /**
   * 检查是否为封装的数据
   * @param data 要检查的数据
   * @returns 是否为封装的数据
   */
  public isWrappedData(data: any): boolean {
    return !!data && typeof data === 'object' && this.dataKey in data && this.originKey in data
  }

  /**
   * 处理 URL，将其转换为代理 URL
   * @param url 原始 URL
   * @returns 处理后的 URL
   */
  private processUrl(url: string | null | undefined): string {
    if (!url) return ''
    return toProxyURL(url, window.location.href)
  }

  /**
   * 自定义属性重写方法
   * @param obj 要修改的对象
   * @param prop 要修改的属性
   * @param getter getter 函数
   * @param setter setter 函数
   */
  private definePropertyOverride(
    obj: any,
    prop: string,
    getter: (originalGetter: () => any) => any,
    setter: (originalSetter: (val: any) => void, val: any) => void
  ): void {
    const descriptor = Object.getOwnPropertyDescriptor(obj, prop)
    if (!descriptor) return

    const originalGetter = descriptor.get
    const originalSetter = descriptor.set

    if (!originalGetter || !originalSetter) return

    Object.defineProperty(obj, `__poOriginal${prop}`, {
      get: originalGetter,
      set: originalSetter,
      configurable: true
    })

    Object.defineProperty(obj, prop, {
      get: function (this: any) {
        return getter.call(this, originalGetter.bind(this))
      },
      set: function (this: any, val: any) {
        this.__poOriginalData = val
        setter.call(this, originalSetter.bind(this), val)
      },
      configurable: true,
      enumerable: descriptor.enumerable
    })
  }

  /**
   * 检查事件是否为用户触发的可信事件
   * @param event 事件对象
   * @returns 是否为可信事件
   */
  private isUserTrustedEvent(event: Event): boolean {
    return 'isTrusted' in event ? event.isTrusted : true
  }

  /**
   * 检查当前窗口是否在 iframe 中
   * @param win 窗口对象
   * @returns 是否在 iframe 中
   */
  private isInIframe(win: Window): boolean {
    try {
      return win.self !== win.top
    } catch (e) {
      // 如果出现异常，通常是由于跨域限制，这种情况下我们认为在 iframe 中
      return true
    }
  }
}
