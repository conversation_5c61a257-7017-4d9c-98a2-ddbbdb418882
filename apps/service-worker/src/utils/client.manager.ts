import { PostMessageManager } from './post-message.manager'
import { appendQueryParam } from './url'

export class ClientManager {
  private static instance: ClientManager

  private constructor() {}

  public static getInstance(): ClientManager {
    if (!ClientManager.instance) {
      ClientManager.instance = new ClientManager()
    }
    return ClientManager.instance
  }

  /**
   * 获取当前页面的客户端
   * @param clientId 请求的客户端ID
   * @param url 请求的URL
   * @returns 返回匹配的客户端或undefined
   */
  public async getCurrentClient(clientId?: string, url?: string): Promise<Client | undefined> {
    try {
      // 获取所有客户端
      // @ts-ignore
      const clientList = await self.clients.matchAll({
        type: 'window',
        includeUncontrolled: true
      })

      // 根据clientId或URL查找匹配的客户端
      return clientList.find(
        (client: any) => (clientId && client.id === clientId) || client.url === url
      )
    } catch (error) {
      console.error('[Client] Error getting current client:', error)
      return undefined
    }
  }

  /**
   * 获取当前页面的URL
   * @param clientId 请求的客户端ID
   * @param url 请求的URL
   * @returns 返回当前页面的URL或undefined
   */
  public async getCurrentPageUrl(clientId?: string, url?: string): Promise<string | undefined> {
    try {
      const client = await this.getCurrentClient(clientId, url)
      return client?.url
    } catch (error) {
      console.error('[Client] Error getting current page URL:', error)
      return undefined
    }
  }

  /**
   * 获取当前页面的源
   * @param clientId 请求的客户端ID
   * @param url 请求的URL
   * @returns 返回当前页面的源或undefined
   */
  public async getCurrentPageOrigin(clientId?: string, url?: string): Promise<string | undefined> {
    try {
      const currentUrl = await this.getCurrentPageUrl(clientId, url)
      if (currentUrl) {
        return new URL(currentUrl).origin
      }
      return undefined
    } catch (error) {
      console.error('[Client] Error getting current page origin:', error)
      return undefined
    }
  }

  /**
   * 从客户端URL中获取pot参数并添加到目标URL中
   * @param requestUrl 请求URL
   * @param clientId 客户端ID
   * @returns 返回可能被修改的URL对象
   */
  public async appendPotFromClient(requestUrl: string, clientId?: string): Promise<URL> {
    const urlObj = new URL(requestUrl)

    // 如果URL已经包含__pot参数，直接返回
    if (requestUrl.includes('__pot=')) {
      return urlObj
    }

    try {
      // 获取当前客户端
      const client = await this.getCurrentClient(clientId, requestUrl)
      if (client?.url) {
        const clientUrl = new URL(client.url)
        const pot = clientUrl.searchParams.get('__pot')
        if (pot) {
          // 创建一个新的URL，使用客户端的origin
          const newUrl = new URL(urlObj.href)
          // 保留所有原始参数
          const searchParams = new URLSearchParams(urlObj.search)
          // 设置新的origin和完整的search参数
          newUrl.protocol = clientUrl.protocol
          newUrl.host = clientUrl.host
          newUrl.port = clientUrl.port
          newUrl.search = searchParams.toString()
          // 添加pot参数
          const newUrlStr = appendQueryParam(newUrl, '__pot', pot)
          return new URL(newUrlStr)
        }
      }
    } catch (error) {
      console.error('[Client] Error appending pot parameter:', error)
    }

    return urlObj
  }

  /**
   * 向特定客户端发送消息
   * @param clientId 目标客户端ID
   * @param message 要发送的消息
   * @returns 是否成功发送
   */
  public async sendMessageToClient(clientId: string, message: any): Promise<boolean> {
    try {
      const client = await this.getCurrentClient(clientId)
      if (client && 'postMessage' in client) {
        // 获取当前页面的来源
        const origin = await this.getCurrentPageOrigin(clientId)
        // 使用 PostMessageManager 封装消息
        const wrappedMessage = origin
          ? PostMessageManager.getInstance().wrapMessageData(message, origin)
          : message

        // @ts-ignore - Client 和 WindowClient 类型不完全匹配
        client.postMessage(wrappedMessage)
        return true
      }
      return false
    } catch (error) {
      console.error('[Client] Error sending message to client:', error)
      return false
    }
  }

  /**
   * 向所有客户端广播消息
   * @param message 要广播的消息
   * @returns 成功发送消息的客户端数量
   */
  public async broadcastMessage(message: any): Promise<number> {
    try {
      // @ts-ignore
      const clients = await self.clients.matchAll({
        type: 'window',
        includeUncontrolled: true
      })

      let successCount = 0
      for (const client of clients) {
        try {
          if ('postMessage' in client) {
            // 获取当前客户端的来源
            const origin = client.url ? new URL(client.url).origin : undefined
            // 使用 PostMessageManager 封装消息
            const wrappedMessage = origin
              ? PostMessageManager.getInstance().wrapMessageData(message, origin)
              : message

            // @ts-ignore - Client 和 WindowClient 类型不完全匹配
            client.postMessage(wrappedMessage)
            successCount++
          }
        } catch (e) {
          console.error('[Client] Error sending message to specific client:', e)
        }
      }

      return successCount
    } catch (error) {
      console.error('[Client] Error broadcasting message:', error)
      return 0
    }
  }
}
