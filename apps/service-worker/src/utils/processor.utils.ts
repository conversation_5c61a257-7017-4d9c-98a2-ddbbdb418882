import { POST_MESSAGE, SETTINGS_URL_PATTERN } from '@/config/constants'
import { toProxyURL } from './url'

/**
 * 处理 JavaScript 中的 location 引用
 * 注意：由于增强的运行时拦截机制，静态替换已不是主要防护手段
 * 这个函数现在主要作为辅助措施，处理明文的location引用
 * @param jsContent JavaScript 内容
 * @returns 处理后的 JavaScript 内容
 */
export function processLocationReferences(jsContent: string): string {
  // 运行时拦截已经能够处理混淆的location访问
  // 这里只处理明文的location引用作为额外保护
  let processed = jsContent.replace(/\blocation\b/g, '__polct')

  // 添加运行时检查，确保location对象被正确拦截
  const runtimeCheck = `
  <script>
    // 运行时location拦截检查
    if (typeof window !== 'undefined' && typeof window.__polct !== 'undefined') {
      if (window.location !== window.__polct) {
        try {
          Object.defineProperty(window, 'location', {
            get: function() { return window.__polct; },
            set: function(v) {
              if (typeof v === 'string') {
                window.__polct.href = v;
              }
            },
            configurable: true
          });
        } catch(e) {
          console.debug('[RuntimeLocationCheck] Failed:', e);
        }
      }
    }
      </script>
  `

  // 在代码开头添加运行时检查
//   processed = runtimeCheck + processed

  return processed
}

/**
 * 处理 JavaScript 中的 postMessage 方法调用
 * 将各种 postMessage 调用模式替换为使用我们的包装函数的调用
 * @param jsContent JavaScript 内容
 * @returns 处理后的 JavaScript 内容
 */
export function processPostMessageCalls(jsContent: string): string {
  const dataFunction = POST_MESSAGE.PREPARE_DATA_FUNCTION
  const originFunction = POST_MESSAGE.PREPARE_ORIGIN_FUNCTION

  // 预处理步骤：保护类/对象中的方法定义
  let processedContent = jsContent.replace(/\{[\s\n]*postMessage\s*\(([^)]*)\)\s*\{/g, (match) => {
    return match.replace('postMessage', '__PO_METHOD_DEFINITION')
  })

  // 处理其他形式的方法定义
  processedContent = processedContent.replace(/\bpostMessage\s*\(([^)]*)\)\s*\{/g, (match) => {
    // 只有在之后紧跟着函数体的情况下才标记
    if (match.trim().endsWith('{')) {
      return match.replace('postMessage', '__PO_METHOD_DEFINITION')
    }
    return match
  })

  // 主处理步骤：使用更精确的捕获组处理方法调用
  const pattern =
    /(\b(?:window\.|self\.|parent\.|frames\.|top\.|globalThis\.)?)(postMessage)(\s*\()((?:[^)(]|\((?:[^)(]|\([^)(]*\))*\))*)(\))/g

  processedContent = processedContent.replace(
    pattern,
    (match, prefix, name, openParen, argsStr, closeParen) => {
      // 跳过已被标记为方法定义的
      if (match.includes('__PO_METHOD_DEFINITION')) {
        return match.replace('__PO_METHOD_DEFINITION', 'postMessage')
      }

      // 检查调用后是否紧跟花括号，这可能是方法定义
      try {
        const matchPosition = jsContent.indexOf(match)
        if (matchPosition !== -1) {
          const afterText = jsContent.substring(matchPosition + match.length).trim()
          if (afterText.startsWith('{')) {
            return match // 方法定义，不处理
          }
        }
      } catch (e) {
        // 忽略错误，继续处理
      }

      // 如果已经包含我们的包装函数，直接返回原始匹配
      if (argsStr.includes(dataFunction) || argsStr.includes(originFunction)) {
        return match
      }

      try {
        // 解析并处理参数
        const args = parsePostMessageArgs(argsStr.trim())

        if (!args.message) return match // 没有有效参数，返回原样

        // 构建新的调用
        let newArgs = `${dataFunction}(${args.message})`

        if (args.targetOrigin) {
          newArgs += `, ${originFunction}(${args.targetOrigin})`
        } else if (args.options) {
          newArgs += `, ${args.options}`
        }

        if (args.transfer) {
          newArgs += `, ${args.transfer}`
        }

        return `${prefix || ''}${name}${openParen}${newArgs}${closeParen}`
      } catch (e) {
        // 处理出错时返回原始匹配，避免破坏代码
        console.warn('处理postMessage时出错:', e)
        return match
      }
    }
  )

  // 恢复被替换的方法定义标记
  processedContent = processedContent.replace(/__PO_METHOD_DEFINITION/g, 'postMessage')

  return processedContent
}

/**
 * 解析postMessage参数
 * @param argsStr 参数字符串
 * @returns 结构化的参数对象
 */
function parsePostMessageArgs(argsStr: string): {
  message?: string
  targetOrigin?: string
  options?: string
  transfer?: string
} {
  // 处理空参数情况
  if (!argsStr.trim()) return {}

  const args = splitArguments(argsStr)

  // 直接使用解构赋值简化代码
  const [message, secondArg, transfer] = args

  const result: {
    message?: string
    targetOrigin?: string
    options?: string
    transfer?: string
  } = { message }

  // 只有在有消息参数的情况下才处理其他参数
  if (!message) return {}

  // 处理第二个参数（targetOrigin或options）
  if (secondArg) {
    const trimmed = secondArg.trim()
    if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
      result.options = secondArg
    } else {
      result.targetOrigin = secondArg
    }
  }

  // 处理第三个参数（transfer）
  if (transfer) {
    result.transfer = transfer
  }

  return result
}

/**
 * 分割函数调用的参数，正确处理嵌套的括号、数组和对象
 * @param argsStr 参数字符串
 * @returns 分割后的参数数组
 */
export function splitArguments(argsStr: string): string[] {
  const result: string[] = []
  let current = ''
  let depth = 0
  let inString = false
  let stringChar = ''

  for (let i = 0; i < argsStr.length; i++) {
    const char = argsStr[i]

    // 处理字符串
    if ((char === '"' || char === "'") && (i === 0 || argsStr[i - 1] !== '\\')) {
      if (!inString) {
        inString = true
        stringChar = char
      } else if (char === stringChar) {
        inString = false
      }
    }

    // 处理括号嵌套
    if (!inString) {
      if (char === '(' || char === '[' || char === '{') {
        depth++
      } else if (char === ')' || char === ']' || char === '}') {
        depth--
      } else if (char === ',' && depth === 0) {
        // 当且仅当不在任何括号内且遇到逗号时才分割参数
        result.push(current)
        current = ''
        continue
      }
    }

    current += char
  }

  // 添加最后一个参数
  if (current.trim()) {
    result.push(current)
  }

  return result.map((arg) => arg.trim())
}

/**
 * 处理HTML字符串中的URL链接
 * 用于复用在HTML处理器和DOM动态拦截器之间
 * @param html HTML字符串内容
 * @param requestUrl 当前请求的URL上下文
 * @param processUrlFn 处理URL的函数（例如toProxyURL或类似函数）
 * @returns 处理后的HTML字符串
 */
export function processHTMLElementLinks(html: string, requestUrl: string): string {
  const pattern = SETTINGS_URL_PATTERN()
  console.log('[HTML] Processing HTML pattern:', pattern)

  // 使用正则表达式模式处理特定标签的属性
  return html.replace(pattern, (match: string, ...args: string[]) => {
    try {
      // 使用命名捕获组，直接获取 URL
      const groups = args[args.length - 1] // 获取命名捕获组对象
      // @ts-ignore
      const url = groups?.url
      if (!url) return match
      console.log('[HTML] Parsed URL:', url, requestUrl)
      const processedUrl = toProxyURL(url, requestUrl)
      if (!processedUrl) return match

      return match.replace(url, processedUrl)
    } catch (e) {
      console.warn('Failed to process URL:', match, e)
      return match
    }
  })
}
