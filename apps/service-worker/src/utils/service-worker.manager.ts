import { SW_VERSION } from '../config/version'

export class ServiceWorkerManager {
  private static instance: ServiceWorkerManager

  private constructor() {}

  static getInstance(): ServiceWorkerManager {
    if (!ServiceWorkerManager.instance) {
      ServiceWorkerManager.instance = new ServiceWorkerManager()
    }
    return ServiceWorkerManager.instance
  }

  /**
   * 注册 Service Worker
   */
  public async register(
    options: {
      immediate?: boolean // 是否立即注册
      version?: string // SW版本号
    } = {}
  ): Promise<ServiceWorkerRegistration> {
    if (!('serviceWorker' in navigator)) {
      throw new Error('Service Worker not supported')
    }

    try {
      const targetVersion = options.version || SW_VERSION

      // 获取现有的 Service Worker registrations
      const registrations = await navigator.serviceWorker.getRegistrations()

      // 检查是否有匹配版本的 registration
      for (const registration of registrations) {
        if (!registration.active) continue

        const currentVersion = new URL(registration.active.scriptURL).searchParams.get('v')
        if (currentVersion === targetVersion) {
          console.log(
            '[SW] Service Worker version matches, skipping registration, currentVersion: ',
            currentVersion,
            'targetVersion: ',
            targetVersion
          )
          return registration
        }
      }

      // 如果没有匹配的版本，注销所有现有的 Service Workers
      await Promise.all(registrations.map((reg) => reg.unregister()))

      // 注册新的 Service Worker
      const swPath = `/__po.sw.js?v=${targetVersion}`
      const registration = await navigator.serviceWorker.register(swPath, {
        scope: '/',
        updateViaCache: 'none'
      })

      // 如果需要立即激活
      if (options.immediate) {
        await this.waitForActivation(registration)
      }

      return registration
    } catch (error) {
      console.error('[SW] Registration failed:', error)
      throw error
    }
  }

  /**
   * 等待 Service Worker 激活
   */
  public async waitForActivation(registration: ServiceWorkerRegistration): Promise<void> {
    const worker = registration.installing
    if (worker) {
      await new Promise<void>((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Service Worker installation timeout'))
        }, 10000)

        worker.addEventListener('statechange', () => {
          if (worker.state === 'activated') {
            clearTimeout(timeoutId)
            resolve()
          } else if (worker.state === 'redundant') {
            clearTimeout(timeoutId)
            reject(new Error('Service Worker became redundant'))
          }
        })
      })
    }
  }

  /**
   * 等待 Service Worker 控制页面
   */
  public async waitForControl(): Promise<void> {
    if (!navigator.serviceWorker.controller) {
      await new Promise<void>((resolve) => {
        navigator.serviceWorker.addEventListener('controllerchange', () => resolve(), {
          once: true
        })
      })
    }
  }
}
