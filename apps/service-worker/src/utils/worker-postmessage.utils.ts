/**
 * PostMessage 辅助工具
 *
 * 此文件实现了在 Web Worker 和主线程（Window）环境中处理 postMessage 通信的功能：
 * - preparePostMessageData：将消息数据封装为 ProxyOrb 格式
 * - preparePostMessageOrigin：处理 postMessage 的目标源参数
 * - setupPostMessageHelpers：设置全局辅助函数并启用消息处理增强
 *
 * 使用方法：
 * 1. 导入并调用 setupPostMessageHelpers() 函数，它会自动检测环境并设置相应的功能
 * 2. 之后可以在全局访问到 __poPreparePostMessageData 等辅助方法
 */

// 数据和来源的封装键名
export const PO_DATA_KEY = '__po_data'
export const PO_ORIGIN_KEY = '__po_origin'

// 定义全局辅助函数类型，同时适用于 Window 和 Worker 环境
type GlobalWithHelpers = {
  __poPreparePostMessageData: (data: any) => any
  __poPreparePostMessageOrigin: (targetOrigin: string) => string
}

/**
 * 检测当前是否为 Window 环境
 * @returns 是否为 Window 环境
 */
export const isWindow = (): boolean => {
  return 'Window' in self
}

/**
 * 检查是否为封装的数据
 * @param data 要检查的数据
 * @returns 是否为封装的数据
 */
export const isWrappedData = (data: any): boolean => {
  return !!data && typeof data === 'object' && PO_DATA_KEY in data && PO_ORIGIN_KEY in data
}

/**
 * 获取当前环境的来源信息
 */
export const getCurrentOrigin = (): string => {
  try {
    // 优先使用 __polct 中的 origin（如果存在）
    const globalObj = typeof window !== 'undefined' ? window : self
    if ((globalObj as any).__polct?.origin) {
      return (globalObj as any).__polct.origin
    }

    // 使用标准 location.origin
    if (location?.origin) {
      return location.origin
    }
  } catch (e) {
    // 忽略错误
  }

  // 默认使用通配符
  return '*'
}

/**
 * 准备要通过 postMessage 发送的数据
 * 将原始数据封装到带有源信息的结构中
 *
 * @param data 原始消息数据
 * @returns 封装后的数据
 */
export const preparePostMessageData = (data: any): any => {
  // Window 环境下进行封装，Worker 环境下不做处理
  if (!isWindow()) {
    return data
  }

  // 避免重复封装
  if (isWrappedData(data)) {
    return data
  }

  // 封装数据与源信息
  const wrappedData: Record<string, any> = {}
  wrappedData[PO_DATA_KEY] = data
  wrappedData[PO_ORIGIN_KEY] = getCurrentOrigin()

  return wrappedData
}

/**
 * 准备 postMessage 的目标源参数
 * 根据环境处理目标源参数
 *
 * @param targetOrigin 原始目标源
 * @returns 处理后的目标源
 */
export const preparePostMessageOrigin = (targetOrigin: string): string => {
  const obj = self
  if (
    'Window' in obj &&
    (typeof targetOrigin === 'string' || (targetOrigin as any) instanceof String)
  ) {
    return '*'
  } else {
    return targetOrigin
  }
}

/**
 * 解封装消息数据
 * 从封装的数据结构中提取原始数据
 *
 * @param data 可能被封装的数据
 * @returns 解封装后的数据
 */
export const unwrapMessageData = (data: any): any => {
  if (!data) return data

  // 如果是封装的数据，提取实际数据
  if (isWrappedData(data)) {
    return data[PO_DATA_KEY]
  }

  // 递归处理数组
  if (Array.isArray(data)) {
    for (let i = 0; i < data.length; i++) {
      if (isWrappedData(data[i])) {
        data[i] = data[i][PO_DATA_KEY]
      } else {
        // 递归处理嵌套的数组项
        unwrapMessageData(data[i])
      }
    }
  }
  // 递归处理对象
  else if (typeof data === 'object' && data !== null) {
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (isWrappedData(data[key])) {
          data[key] = data[key][PO_DATA_KEY]
        } else {
          // 递归处理嵌套的对象属性
          unwrapMessageData(data[key])
        }
      }
    }
  }

  return data
}

/**
 * 处理 MessageEvent，自动解包数据
 * @param event 原始消息事件
 * @returns 修改后的消息事件
 */
const processMessageEvent = (event: MessageEvent): MessageEvent => {
  // 不要使用 Object.assign，因为 MessageEvent 有只读属性
  const modifiedEvent = event

  // 保存原始数据用于获取 origin
  const originalDataSymbol = Symbol('originalData')
  ;(modifiedEvent as any)[originalDataSymbol] = event.data

  // 解包数据
  const unwrappedData = unwrapMessageData(event.data)

  // 创建访问器，在读取 data 时返回解包的数据
  Object.defineProperty(event, 'data', {
    configurable: true,
    get: function () {
      return unwrappedData
    }
  })

  // 创建访问器，在读取 origin 时优先返回封装数据中的 origin
  Object.defineProperty(event, 'origin', {
    configurable: true,
    get: function () {
      // 如果原始数据中包含 origin 信息，使用它
      if (
        (this as any)[originalDataSymbol] &&
        typeof (this as any)[originalDataSymbol] === 'object' &&
        PO_ORIGIN_KEY in (this as any)[originalDataSymbol]
      ) {
        return (this as any)[originalDataSymbol][PO_ORIGIN_KEY]
      }
      // 否则返回原始 origin
      return event.origin
    }
  })

  return modifiedEvent
}

/**
 * 为 Worker 环境设置消息拦截和处理
 */
const setupWorkerEnhancedMessageHandling = () => {
  // 保存原始的 onmessage 处理器
  let originalOnMessageHandler: ((this: WorkerGlobalScope, ev: MessageEvent) => any) | null = null

  // 重定义 onmessage 属性
  Object.defineProperty(self, 'onmessage', {
    configurable: true,
    get: function () {
      return originalOnMessageHandler
    },
    set: function (handler) {
      // 保存原始处理器
      originalOnMessageHandler = handler

      // 如果设置了处理器，创建一个包装处理器来自动解包消息
      if (handler) {
        const wrappedHandler = function (this: WorkerGlobalScope, event: MessageEvent) {
          // 处理消息事件，解包数据
          const modifiedEvent = processMessageEvent(event)

          // 调用原始处理器
          return handler.call(this, modifiedEvent)
        }

        // 移除之前可能存在的消息监听器
        self.removeEventListener('message', wrappedHandler as EventListener)

        // 添加新的包装监听器
        self.addEventListener('message', wrappedHandler as EventListener)
      }
    },
    enumerable: true
  })

  // 原始的 addEventListener 方法
  const originalAddEventListener = self.addEventListener

  // 保存原始方法的引用
  const originalAddEventListenerImpl = self.addEventListener

  // 创建一个自定义的 addEventListener 函数，完全覆盖原有实现
  // 使用 Function.prototype.apply 方式可以绕过类型检查
  self.addEventListener = function addEventListener() {
    const args = Array.from(arguments)
    const type = args[0]
    const listener = args[1]
    const options = args[2]

    if (type === 'message' && listener) {
      // 创建一个包装的监听器，自动解包消息数据
      const wrappedListener = function wrappedMessageListener(this: any, event: Event) {
        // 确保是 MessageEvent 类型
        if (!(event instanceof MessageEvent)) return

        // 处理消息事件，解包数据
        const modifiedEvent = processMessageEvent(event as MessageEvent)

        // 使用修改后的事件调用原始监听器
        if (typeof listener === 'function') {
          return listener.call(this, modifiedEvent)
        } else if (
          listener &&
          typeof (listener as EventListenerObject).handleEvent === 'function'
        ) {
          return (listener as EventListenerObject).handleEvent.call(listener, modifiedEvent)
        }
      }

      // 使用 Function.prototype.apply 调用原始方法
      return Function.prototype.apply.call(originalAddEventListenerImpl, self, [
        type,
        wrappedListener,
        options
      ])
    }

    // 对于非 message 事件，使用原始方法
    return Function.prototype.apply.call(originalAddEventListenerImpl, self, args)
  } as typeof self.addEventListener
}

/**
 * 设置全局辅助函数并启用消息处理增强
 * 根据环境自动选择适当的设置方式
 */
export const setupPostMessageHelpers = () => {
  // 检测环境类型
  const windowEnv = isWindow()

  // 全局对象引用 (Worker 中是 self，Window 中是 window)
  const globalObj = windowEnv ? window : self

  // 转换类型，确保安全访问
  const globalWithHelpers = globalObj as unknown as GlobalWithHelpers & typeof globalObj

  // 设置全局辅助函数
  globalWithHelpers.__poPreparePostMessageData = preparePostMessageData
  globalWithHelpers.__poPreparePostMessageOrigin = preparePostMessageOrigin
  // 注意：不再提供__poUnwrapMessageData全局方法

  // 在 Worker 环境中设置增强的消息处理机制
  if (!windowEnv) {
    setupWorkerEnhancedMessageHandling()
    console.log('[ProxyOrb] PostMessage helper functions initialized in Worker')
  } else {
    console.log('[ProxyOrb] PostMessage helper functions initialized in Window')
  }
}
