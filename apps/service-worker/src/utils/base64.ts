/**
 * Base64 解码函数，仅支持浏览器环境
 * @param str 要解码的字符串
 */
export function decodePot(str: string): string {
  try {
    // 处理padding：解码前自动添加必要的等号
    let processedStr = str
    const paddingLength = 4 - (str.length % 4)
    if (paddingLength < 4) {
      processedStr = str + '='.repeat(paddingLength)
    }

    // 浏览器环境解码
    return atob(processedStr)
  } catch (e) {
    return str
  }
}

/**
 * Base64 编码函数，仅支持浏览器环境
 * @param str 要编码的字符串
 */
export function encodePot(str: string): string {
  try {
    // 浏览器环境编码
    const encoded = btoa(str)

    // 移除所有padding字符（等号）
    return encoded.replace(/=+$/, '')
  } catch (e) {
    return str
  }
}
