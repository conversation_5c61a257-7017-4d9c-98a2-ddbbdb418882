export class RefererManager {
  private static instance: RefererManager
  private map: Map<string, string>
  private cleanupInterval: number

  private constructor() {
    this.map = new Map<string, string>()
    this.cleanupInterval = 5 * 60 * 1000 // 5分钟
  }

  static getInstance(): RefererManager {
    if (!RefererManager.instance) {
      RefererManager.instance = new RefererManager()
    }
    return RefererManager.instance
  }

  /**
   * 设置 URL 和对应的 referer
   * @param url 需要存储的 URL
   * @param referer 对应的 referer
   */
  public set(url: string, referer: string): void {
    try {
      this.map.set(url, referer)
      // 设置自动清理定时器
      setTimeout(() => {
        this.delete(url)
      }, this.cleanupInterval)
    } catch (error) {
      console.error('[RefererManager] Failed to set referer:', error)
    }
  }

  /**
   * 获取指定 URL 的 referer
   * @param url 要查询的 URL
   */
  public get(url: string): string | undefined {
    try {
      return this.map.get(url)
    } catch (error) {
      console.error('[RefererManager] Failed to get referer:', error)
      return undefined
    }
  }

  /**
   * 删除指定 URL 的记录
   * @param url 要删除的 URL
   */
  private delete(url: string): void {
    try {
      this.map.delete(url)
    } catch (error) {
      console.error('[RefererManager] Failed to delete referer:', error)
    }
  }

  /**
   * 根据 URL 模式匹配 referer
   * @param url 要匹配的 URL
   */
  public match(url: string): string | undefined {
    try {
      const urlObj = new URL(url)
      for (const [storedUrl, referer] of this.map.entries()) {
        try {
          const storedUrlObj = new URL(storedUrl)
          if (storedUrlObj.pathname === urlObj.pathname) {
            return referer
          }
        } catch (error) {
          console.warn('[RefererManager] Invalid stored URL:', storedUrl)
          this.delete(storedUrl)
        }
      }
      return undefined
    } catch (error) {
      console.error('[RefererManager] Failed to match referer:', error)
      return undefined
    }
  }

  /**
   * 清理所有过期的记录
   */
  public cleanup(): void {
    try {
      this.map.clear()
    } catch (error) {
      console.error('[RefererManager] Failed to cleanup:', error)
    }
  }

  /**
   * 获取当前存储的记录数量
   */
  public size(): number {
    return this.map.size
  }
}
