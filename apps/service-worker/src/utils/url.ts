import { SETTINGS_SKIP_SCHEMES } from '@/config/constants'
import { encodePot } from './base64'

// TrustedScriptURL type definition
declare class TrustedScriptURL {
  private constructor()
  toString(): string
}

// 预编译常用的正则表达式
const HTML_HEX_ENTITY_REGEX = /&#x([0-9a-f]+);/gi
const HTML_DEC_ENTITY_REGEX = /&#(\d+);/g
const PROTOCOL_SLASH_REGEX = /([a-z]):\/+/i
const HTTP_SLASH_REGEX = /(https?|ftp|ws):\/\/+/g
const TRIM_TABS_REGEX = /\t+/g
const PERCENT_ENCODING_REGEX = /%25([0-9A-F]{2})/gi
const LEFT_BRACKET_REGEX = /\[/g
const RIGHT_BRACKET_REGEX = /\]/g
const HTML_COMMENT_REGEX = /<!--.*?-->/g
const QUOTES_REGEX = /^["'](.+)["']$/

// URL转换缓存实现
const proxyURLCache = new Map<string, string>()
const CACHE_MAX_SIZE = 5000 // 缓存最大条目数

/**
 * 检查缓存大小并在必要时清理
 */
function checkCacheSize() {
  if (proxyURLCache.size > CACHE_MAX_SIZE) {
    const keysToDelete = Array.from(proxyURLCache.keys()).slice(
      0,
      Math.floor(proxyURLCache.size / 2)
    )
    keysToDelete.forEach((key) => proxyURLCache.delete(key))
    console.log(`[URL Cache] Cleared ${keysToDelete.length} cache entries`)
  }
}

/**
 * 函数缓存装饰器
 * @param prefix 缓存键前缀，用于区分不同函数
 * @param fn 需要缓存的函数
 * @returns 带缓存功能的函数
 */
function withCache<T extends (...args: any[]) => string>(prefix: string, fn: T): T {
  return ((...args: Parameters<T>): string => {
    // 创建缓存键 - 将所有参数序列化
    const serializedArgs = args
      .map((arg) => {
        // 处理TrustedScriptURL对象
        if (arg instanceof Object && typeof arg.toString === 'function') {
          return arg.toString()
        }
        return String(arg)
      })
      .join('|')

    const cacheKey = `${prefix}|${serializedArgs}`

    // 检查缓存
    const cachedResult = proxyURLCache.get(cacheKey)
    if (cachedResult) {
      return cachedResult
    }

    // 执行原函数
    const result = fn(...args)

    // 缓存结果
    proxyURLCache.set(cacheKey, result)

    // 检查缓存大小
    if (proxyURLCache.size % 100 === 0) {
      checkCacheSize()
    }

    return result
  }) as T
}

// 常见HTML实体映射 - 使用Map提高查找性能
const COMMON_ENTITIES = new Map([
  ['&lt;', '<'],
  ['&gt;', '>'],
  ['&amp;', '&'],
  ['&quot;', '"'],
  ['&apos;', "'"],
  ['&nbsp;', ' '],
  ['&copy;', '©'],
  ['&reg;', '®'],
  ['&trade;', '™'],
  ['&mdash;', '—'],
  ['&ndash;', '–'],
  ['&hellip;', '…'],
  ['&laquo;', '«'],
  ['&raquo;', '»']
])

// 预编译实体正则表达式
const ENTITY_REGEX_CACHE = new Map(
  Array.from(COMMON_ENTITIES.keys()).map((entity) => [
    entity,
    new RegExp(entity.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
  ])
)

/**
 * 私有方法：规范化和清理URL字符串，处理各种边缘情况
 * @param urlString - 原始URL字符串
 * @returns 处理后的URL字符串
 */
const normalizeUrl = (urlString: string): string => {
  // 快速路径：如果URL为空或不包含任何特殊字符，直接返回
  if (!urlString) return urlString

  // 处理以双斜杠开头的URL (协议相对URL)，转换为https协议
  if (urlString.startsWith('//')) {
    urlString = `https:${urlString}`
  }

  // 性能优化：首先检查是否需要处理，避免不必要的操作
  const needsProcessing =
    urlString.includes('&') ||
    urlString.includes('#') ||
    urlString.includes('\\') ||
    urlString.includes('%') ||
    urlString.includes('[') ||
    urlString.includes('<') ||
    urlString.includes('"') ||
    urlString.includes("'") ||
    urlString.includes('\t')

  if (!needsProcessing) return urlString

  // 处理各种HTML实体编码
  let normalizedUrl = urlString

  // 处理十六进制HTML实体 (如 &#x2F; -> /)
  if (normalizedUrl.includes('&#x')) {
    normalizedUrl = normalizedUrl.replace(HTML_HEX_ENTITY_REGEX, (_, hex) =>
      String.fromCodePoint(parseInt(hex, 16))
    )
  }

  // 处理十进制HTML实体 (如 &#47; -> /)
  if (normalizedUrl.includes('&#')) {
    normalizedUrl = normalizedUrl.replace(HTML_DEC_ENTITY_REGEX, (_, dec) =>
      String.fromCodePoint(parseInt(dec, 10))
    )
  }

  // 处理常见HTML实体 - 只处理存在的实体
  for (const [entity, char] of COMMON_ENTITIES) {
    if (normalizedUrl.includes(entity)) {
      normalizedUrl = normalizedUrl.replace(ENTITY_REGEX_CACHE.get(entity)!, char)
    }
  }

  // 检查是否需要处理协议
  if (normalizedUrl.includes('://')) {
    // 处理连续的斜杠 (如 http:////example.com -> http://example.com)
    normalizedUrl = normalizedUrl.replace(PROTOCOL_SLASH_REGEX, '$1://')
    normalizedUrl = normalizedUrl.replace(HTTP_SLASH_REGEX, '$1://')
  }

  // 处理空格和Tab (有些URL可能包含空格或Tab)
  if (normalizedUrl.includes('\t')) {
    normalizedUrl = normalizedUrl.trim().replace(TRIM_TABS_REGEX, ' ')
  } else if (normalizedUrl[0] === ' ' || normalizedUrl[normalizedUrl.length - 1] === ' ') {
    normalizedUrl = normalizedUrl.trim()
  }

  // 处理百分号编码
  if (normalizedUrl.includes('%25')) {
    try {
      // 尝试解码，如果已经是解码状态不会有影响
      normalizedUrl = decodeURIComponent(normalizedUrl.replace(PERCENT_ENCODING_REGEX, '%$1'))
    } catch (e) {
      // 如果解码失败，保持原样
    }
  }

  // 处理特殊情况：URL中包含未编码的中括号
  if (normalizedUrl.includes('[') || normalizedUrl.includes(']')) {
    normalizedUrl = normalizedUrl
      .replace(LEFT_BRACKET_REGEX, '%5B')
      .replace(RIGHT_BRACKET_REGEX, '%5D')
  }

  // 处理HTML注释中的URL
  if (normalizedUrl.includes('<!--')) {
    normalizedUrl = normalizedUrl.replace(HTML_COMMENT_REGEX, '')
  }

  // 移除URL前后可能的引号（有时URL被引号包围）
  if (
    (normalizedUrl[0] === '"' || normalizedUrl[0] === "'") &&
    (normalizedUrl[normalizedUrl.length - 1] === '"' ||
      normalizedUrl[normalizedUrl.length - 1] === "'")
  ) {
    normalizedUrl = normalizedUrl.replace(QUOTES_REGEX, '$1')
  }

  return normalizedUrl
}

/**
 * 检查URL是否为转义过的URL，如果是则返回解转义后的URL
 *
 * @param url - 需要检查的URL字符串
 * @returns 返回一个包含检查结果和解转义URL的对象，{isEscaped: boolean, unescaped: string}
 */
const unEscapedURL = (url: string): { isEscaped: boolean; unescaped: string } => {
  if (!url) return { isEscaped: false, unescaped: url }

  // 反转义URL
  const unescaped = url.replace(/\\(.)/g, '$1') // 通用替换：移除任何字符前的反斜杠

  // 如果反转义后的URL与原URL不同，说明原URL是被转义过的
  const isEscaped = unescaped !== url

  return {
    isEscaped,
    unescaped
  }
}

/**
 * 将URL转义，将特殊字符前添加反斜杠
 * @param url - 需要转义的URL
 * @returns 转义后的URL
 */
const escapeURL = (url: string): string => {
  return url
    .replace(/\//g, '\\/')
    .replace(/:/g, '\\:')
    .replace(/\?/g, '\\?')
    .replace(/&/g, '\\&')
    .replace(/=/g, '\\=')
    .replace(/%/g, '\\%')
}

/**
 * 处理URL返回结果，根据原始URL的格式决定是否添加转义
 * @param url - 处理后的URL
 * @param isEscaped - 原始URL是否为转义格式
 * @returns 格式化后的URL
 */
const formatReturnURL = (url: string, isEscaped: boolean): string => {
  // 如果原始URL是转义格式，则进行转义
  return isEscaped ? escapeURL(url) : url
}

/**
 * 检查URL是否应该被跳过代理处理
 * @param url - 要检查的URL
 * @returns 如果URL应该被跳过返回true，否则返回false
 */
const shouldSkipUrl = (url: string): boolean => {
  if (!url) return false

  // 检查URL是否在跳过列表中
  return SETTINGS_SKIP_SCHEMES.some((skipPattern) => url.startsWith(skipPattern))
}

/**
 * 添加pot参数到URL，使用字符串拼接方式保持原始URL参数的编码状态
 * 因为使用 searchParams.append 会导致参数被编码
 *
 * @param url - URL对象或字符串
 * @param potValue - pot参数值
 * @returns 添加了pot参数的URL字符串
 */
export function appendQueryParam(url: URL | string, key: string, value: string): string {
  const urlString = url instanceof URL ? url.toString() : url
  const hasHash = urlString.includes('#')

  // 提取哈希部分
  let urlWithoutHash = urlString
  let hashPart = ''

  if (hasHash) {
    const hashIndex = urlString.indexOf('#')
    urlWithoutHash = urlString.substring(0, hashIndex)
    hashPart = urlString.substring(hashIndex)
  }

  // 添加pot参数
  const connector = urlWithoutHash.includes('?') ? '&' : '?'
  const resultUrl = `${urlWithoutHash}${connector}${key}=${value}${hashPart}`

  return resultUrl
}

/**
 * 将原始 URL 转换为代理 URL (未缓存的实现)
 * @param originalUrl - 原始 URL (可以是相对路径或绝对路径)
 * @param pageUrl - 当前页面的 URL
 * @returns 转换后的代理 URL
 */
function _toProxyURL(originalUrl: string | TrustedScriptURL, pageUrl: string): string {
  // 将 TrustedScriptURL 对象转换为字符串
  const urlString = originalUrl instanceof Object ? originalUrl.toString() : originalUrl

  // 使用normalizeUrl方法处理URL边缘情况
  const normalizedUrl = normalizeUrl(urlString)
  console.log('[URL] Normalized URL:', normalizedUrl)
  const { isEscaped, unescaped } = unEscapedURL(normalizedUrl)
  console.log('[URL] Unescaped URL:', unescaped)

  // 检查URL是否在跳过列表中
  if (shouldSkipUrl(unescaped)) {
    console.log('[URL] Skipping URL (in skip list):', unescaped)
    return formatReturnURL(unescaped, isEscaped)
  }

  try {
    // 解析当前页面URL（复用URL对象，减少创建开销）
    const currentPage = new URL(pageUrl)

    // 获取必要的代理信息
    let potValue = currentPage.searchParams.get('__pot')
    try {
      const requestUrl = new URL(unescaped)
      if (requestUrl.origin) {
        // 使用请求URL自身的域名来代理
        potValue = encodePot(requestUrl.origin)
      }
    } catch (e) {
      // console.error("Error:", unescaped)
    }

    if (!potValue) {
      console.error('Missing __pot parameter in current URL')
      return formatReturnURL(unescaped, isEscaped)
    }

    // 空URL
    if (!unescaped) {
      const targetUrl = new URL('/', currentPage.origin)
      const resultUrl = appendQueryParam(targetUrl, '__pot', potValue)
      return formatReturnURL(resultUrl, isEscaped)
    }

    // 锚点链接
    if (unescaped.startsWith('#')) {
      const targetUrl = new URL(currentPage.pathname + unescaped, currentPage.origin)
      const resultUrl = appendQueryParam(targetUrl, '__pot', potValue)
      return formatReturnURL(resultUrl, isEscaped)
    }

    // 特殊协议URL - 直接返回，无需代理
    if (
      unescaped.startsWith('data:') ||
      unescaped.startsWith('blob:') ||
      unescaped.startsWith('javascript:')
    ) {
      return formatReturnURL(unescaped, isEscaped)
    }

    // 处理绝对URL和协议相对URL
    if (unescaped.startsWith('http') || unescaped.startsWith('ws') || unescaped.startsWith('//')) {
      const fullUrl = unescaped.startsWith('//') ? `${currentPage.protocol}${unescaped}` : unescaped
      try {
        const parsedUrl = new URL(fullUrl)

        // 同源检查：如果URL与当前页面同源，则直接返回原始URL
        if (parsedUrl.origin === currentPage.origin) {
          console.log('toProxyURL: same origin:', unescaped)
          return formatReturnURL(unescaped, isEscaped)
        }

        const targetUrl = new URL(
          parsedUrl.pathname + parsedUrl.search + parsedUrl.hash,
          currentPage.origin
        )
        // 使用originalUrl的origin的base64编码作为pot参数
        potValue = encodePot(parsedUrl.origin)

        // 使用新的appendQueryParam函数替换原来的方法
        const resultUrl = appendQueryParam(targetUrl, '__pot', potValue)

        return formatReturnURL(resultUrl, isEscaped)
      } catch (e) {
        // 尝试修复不规范但可能有效的URL
        console.warn('Attempting to fix malformed URL:', fullUrl)
        // 处理缺少协议的URL
        const fixedUrl = fullUrl.match(/^[a-z]+:/) ? fullUrl : `http://${fullUrl}`
        const parsedUrl = new URL(fixedUrl)

        // 同源检查：如果URL与当前页面同源，则直接返回原始URL
        if (parsedUrl.origin === currentPage.origin) {
          console.log('toProxyURL: same origin:', unescaped)
          return formatReturnURL(unescaped, isEscaped)
        }

        const targetUrl = new URL(
          parsedUrl.pathname + parsedUrl.search + parsedUrl.hash,
          currentPage.origin
        )
        potValue = encodePot(parsedUrl.origin)

        // 使用新的appendQueryParam函数替换原来的方法
        const resultUrl = appendQueryParam(targetUrl, '__pot', potValue)

        return formatReturnURL(resultUrl, isEscaped)
      }
    }

    // 相对路径 - 确保以/开头
    const path = unescaped.startsWith('/') ? unescaped : `/${unescaped}`
    const targetUrl = new URL(path, currentPage.origin)

    // 使用新的appendQueryParam函数替换原来的方法
    const resultUrl = appendQueryParam(targetUrl, '__pot', potValue)

    return formatReturnURL(resultUrl, isEscaped)
  } catch (error) {
    console.error('URL conversion error:', error, { urlString: unescaped, pageUrl })
    return formatReturnURL(unescaped, isEscaped)
  }
}

/**
 * 将原始 URL 转换为代理 URL (未缓存的实现)
 * @param requestUrl - 原始 URL (可以是相对路径或绝对路径)
 * @param currentPage - 当前页面的 URL
 * @returns 转换后的代理 URL
 */
function _toProxyURLForServerWorker(requestUrl: string, currentPage: string): string {
  // 检查URL是否在跳过列表中
  if (shouldSkipUrl(requestUrl)) {
    console.log('[SW URL] Skipping URL (in skip list):', requestUrl)
    return requestUrl
  }

  try {
    // 解析当前页面URL（复用URL对象，减少创建开销）
    const requestUrlObj = new URL(requestUrl)

    // 获取必要的代理信息
    let potValue = requestUrlObj.searchParams.get('__pot')
    if (potValue) {
      return requestUrl
    }

    const currentPageObj = new URL(currentPage)
    const resultUrl = new URL(
      requestUrlObj.pathname + requestUrlObj.search + requestUrlObj.hash,
      currentPageObj.origin
    )
    potValue = encodePot(requestUrlObj.origin)

    // 使用新的appendQueryParam函数替换原来的方法
    return appendQueryParam(resultUrl, '__pot', potValue)
  } catch (error) {
    console.error('URL conversion error:', error, { urlString: requestUrl })
    return requestUrl
  }
}

// 使用缓存装饰器包装函数
export const toProxyURL = withCache('PROXY', _toProxyURL)
export const toProxyURLForServerWorker = withCache('SW', _toProxyURLForServerWorker)
