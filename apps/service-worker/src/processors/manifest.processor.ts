import { toProxyURL } from '@/utils/url'
import { BaseProcessor } from './base.processor'

// Manifest 内容类型常量
export const MANIFEST_CONTENT_TYPES = ['application/manifest+json', 'application/json']

// Manifest URL 特征
export const MANIFEST_URL_PATTERNS = ['manifest.webmanifest', 'manifest.json']

interface WebManifest {
  start_url?: string
  scope?: string
  icons?: Array<{
    src: string
    [key: string]: unknown
  }>
  [key: string]: unknown
}

export class ProxyManifestProcessor implements BaseProcessor {
  constructor() {}

  canProcess(contentType: string, url: string): boolean {
    return (
      MANIFEST_CONTENT_TYPES.some((type) => contentType.includes(type)) ||
      MANIFEST_URL_PATTERNS.some((pattern) => url.includes(pattern))
    )
  }

  async process(content: string, requestUrl: string): Promise<string> {
    return this.processManifest(content, requestUrl)
  }

  processManifest(manifestText: string, requestUrl: string): string {
    try {
      const manifest = JSON.parse(manifestText) as WebManifest
      const processUrl = (url?: string) => url && toProxyURL(url, requestUrl)

      if (manifest.start_url) {
        manifest.start_url = processUrl(manifest.start_url) || manifest.start_url
      }

      if (manifest.scope) {
        manifest.scope = processUrl(manifest.scope) || manifest.scope
      }

      if (manifest.icons?.length) {
        manifest.icons = manifest.icons.map((icon) => ({
          ...icon,
          src: processUrl(icon.src) || icon.src
        }))
      }

      return JSON.stringify(manifest, null, 2)
    } catch {
      return manifestText
    }
  }
}
