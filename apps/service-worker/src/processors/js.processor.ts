import { processLocationReferences, processPostMessageCalls } from '@/utils/processor.utils'
import { BaseProcessor } from './base.processor'

export const JS_CONTENT_TYPES = [
  'application/javascript',
  'text/javascript',
  'application/x-javascript',
  'application/ecmascript',
  'text/ecmascript'
]

export class ProxyJSProcessor implements BaseProcessor {
  constructor() {}

  canProcess(contentType: string, url: string): boolean {
    return JS_CONTENT_TYPES.some((type) => contentType.includes(type))
  }

  async process(content: string, requestUrl: string): Promise<string> {
    // 1. 先处理 location 相关引用
    let processedContent = processLocationReferences(content)

    // 2. 检查是否是worker请求，worker请求不需要处理postMessage
    // try {
    //   const url = new URL(requestUrl)
    //   if (url.searchParams.get('__poww') === '1') {
    //     console.log('[JS Processor] Skip postMessage processing for worker script:', requestUrl)
    //     return processedContent
    //   }
    // } catch (e) {
    //   // URL 解析失败，继续处理
    // }

    // 3. 非worker请求再处理postMessage调用
    return processPostMessageCalls(processedContent)
  }
}
