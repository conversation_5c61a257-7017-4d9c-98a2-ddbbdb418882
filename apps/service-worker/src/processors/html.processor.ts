import { toProxyURL } from '@/utils/url'
import { decodePot } from '@/utils/base64'
import { BaseProcessor } from './base.processor'
import {
  processPostMessageCalls,
  processLocationReferences,
  processHTMLElementLinks
} from '@/utils/processor.utils'

// HTML 内容类型常量
export const HTML_CONTENT_TYPES = ['text/html']

export class ProxyHTMLProcessor implements BaseProcessor {
  constructor() {}

  canProcess(contentType: string, url: string): boolean {
    return HTML_CONTENT_TYPES.some((type) => contentType.includes(type))
  }

  async process(content: string, requestUrl: string): Promise<string> {
    return this.processHTML(content, requestUrl)
  }

  processHTML(content: string, requestUrl: string): string {
    try {
      const result = this.pipe(content, [
        (html) => this.injectInterceptor(html, requestUrl),
        (html) => this.injectBaseTag(html, requestUrl),
        (html) => this.processExistingIntegrity(html, requestUrl),
        (html) => this.processExistingMetaRefresh(html, requestUrl),
        (html) => this.processExistingPreloadLinks(html, requestUrl),
        (html) => this.processExistingHTMLElement(html, requestUrl), // 处理关键HTML元素的属性链接（a、area、form、input等）
        (html) => this.processExistingEventHandlers(html), // 处理HTML元素属性中的JavaScript代码
        (html) => this.processExistingScriptTags(html) // 处理script标签内的JavaScript代码，location和postMessage替换
      ])

      return result
    } catch (error) {
      console.error('[HTML] Processing failed:', error)
      return content
    }
  }

  private pipe(input: string, fns: Array<(str: string) => string>): string {
    return fns.reduce((result, fn) => fn(result), input)
  }

  private injectInterceptor(html: string, requestUrl: string): string {
    try {
      const target = new URL(requestUrl)
      const proxyOrigin = target.origin

      // 创建超强力的location拦截脚本，必须在任何其他脚本之前执行
      const intcpScript = `<script>
        // 立即拦截location，不等待任何其他代码
        (function() {
          'use strict';

          // 保存原始location对象的引用
          const originalLocation = window.location;
          const originalHref = originalLocation.href;

          console.log('[UltraLocationInterceptor] Starting immediate interception');

          // 创建强力代理对象
          const locationProxy = {
            // 基础属性
            get href() {
              console.log('[LocationProxy] href accessed');
              return originalLocation.href;
            },
            set href(v) {
              console.log('[LocationProxy] href set to:', v);
              if (typeof v === 'string') {
                console.warn('[LocationProxy] *** BLOCKED REDIRECT ATTEMPT ***:', v);

                // 显示拦截信息
                if (typeof document !== 'undefined') {
                  const alertDiv = document.createElement('div');
                  alertDiv.style.cssText = 'position:fixed;top:10px;left:10px;background:red;color:white;padding:10px;z-index:99999;border:2px solid white;';
                  alertDiv.textContent = 'REDIRECT BLOCKED: ' + v;
                  document.body?.appendChild(alertDiv);
                  setTimeout(() => alertDiv.remove(), 5000);
                }

                // 完全阻止跳转
                return;
              }
            },

            get host() { return originalLocation.host; },
            set host(v) { console.log('[LocationProxy] host set:', v); },

            get hostname() { return originalLocation.hostname; },
            set hostname(v) { console.log('[LocationProxy] hostname set:', v); },

            get pathname() { return originalLocation.pathname; },
            set pathname(v) { console.log('[LocationProxy] pathname set:', v); },

            get search() { return originalLocation.search; },
            set search(v) { console.log('[LocationProxy] search set:', v); },

            get hash() { return originalLocation.hash; },
            set hash(v) { console.log('[LocationProxy] hash set:', v); },

            get origin() { return originalLocation.origin; },
            get protocol() { return originalLocation.protocol; },
            set protocol(v) { console.log('[LocationProxy] protocol set:', v); },

            get port() { return originalLocation.port; },
            set port(v) { console.log('[LocationProxy] port set:', v); },

            // 方法
            assign: function(url) {
              console.warn('[LocationProxy] *** BLOCKED location.assign ***:', url);

              // 显示拦截信息
              if (typeof document !== 'undefined') {
                const alertDiv = document.createElement('div');
                alertDiv.style.cssText = 'position:fixed;top:50px;left:10px;background:orange;color:white;padding:10px;z-index:99999;border:2px solid white;';
                alertDiv.textContent = 'ASSIGN BLOCKED: ' + url;
                document.body?.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
              }

              // 完全阻止跳转
              return;
            },

            replace: function(url) {
              console.warn('[LocationProxy] *** BLOCKED location.replace ***:', url);

              // 显示拦截信息
              if (typeof document !== 'undefined') {
                const alertDiv = document.createElement('div');
                alertDiv.style.cssText = 'position:fixed;top:90px;left:10px;background:purple;color:white;padding:10px;z-index:99999;border:2px solid white;';
                alertDiv.textContent = 'REPLACE BLOCKED: ' + url;
                document.body?.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
              }

              // 完全阻止跳转
              return;
            },

            reload: function() {
              console.log('[LocationProxy] reload called');
              return originalLocation.reload();
            },

            toString: function() {
              return originalLocation.href;
            }
          };

          // 立即替换window.location
          try {
            Object.defineProperty(window, 'location', {
              get: function() {
                return locationProxy;
              },
              set: function(v) {
                console.warn('[LocationProxy] Direct window.location assignment:', v);
                if (typeof v === 'string') {
                  locationProxy.href = v;
                }
              },
              configurable: true,
              enumerable: true
            });

            console.log('[UltraLocationInterceptor] window.location replaced successfully');
          } catch(e) {
            console.error('[UltraLocationInterceptor] Failed to replace window.location:', e);
          }

          // 拦截所有可能的访问方式
          const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
          Object.getOwnPropertyDescriptor = function(obj, prop) {
            if (obj === window && (prop === 'location' || prop === 'loc' + 'ation')) {
              return {
                get: function() { return locationProxy; },
                set: function(v) {
                  if (typeof v === 'string') {
                    locationProxy.href = v;
                  }
                },
                configurable: true,
                enumerable: true
              };
            }
            return originalGetOwnPropertyDescriptor.call(this, obj, prop);
          };

          // 拦截动态属性访问
          const windowHandler = {
            get: function(target, prop) {
              if (prop === 'location' || prop === 'loc' + 'ation') {
                console.log('[LocationProxy] Dynamic access detected:', prop);
                return locationProxy;
              }
              return Reflect.get(target, prop);
            },
            set: function(target, prop, value) {
              if (prop === 'location' || prop === 'loc' + 'ation') {
                console.warn('[LocationProxy] Dynamic assignment detected:', prop, value);
                if (typeof value === 'string') {
                  locationProxy.href = value;
                  return true;
                }
              }
              return Reflect.set(target, prop, value);
            }
          };

          // 尝试代理window对象（可能在某些环境下有效）
          try {
            const windowProxy = new Proxy(window, windowHandler);
            // 注意：这个可能不会完全生效，但作为额外保护
          } catch(e) {
            console.debug('[UltraLocationInterceptor] Window proxy failed:', e);
          }

          // 标记拦截已完成
          window.__ultraLocationIntercepted = true;

          console.log('[UltraLocationInterceptor] All interceptions established');
        })();
      </script>
      <script>
        // 立即执行的location拦截，防止混淆代码绕过
        (function() {
          if (typeof window !== 'undefined' && !window.__locationIntercepted) {
            try {
              // 标记已拦截，防止重复
              window.__locationIntercepted = true;

              // 保存原始location引用
              const originalLocation = window.location;

              // 创建临时代理对象
              const tempProxy = {
                get href() { return originalLocation.href; },
                set href(v) {
                  if (typeof v === 'string') {
                    // 这里会被后续的完整拦截器替换
                    originalLocation.href = v;
                  }
                },
                assign: function(url) { originalLocation.assign(url); },
                replace: function(url) { originalLocation.replace(url); },
                reload: function() { originalLocation.reload(); },
                toString: function() { return originalLocation.href; }
              };

              // 立即替换location对象
              Object.defineProperty(window, 'location', {
                get: function() { return tempProxy; },
                set: function(v) {
                  if (typeof v === 'string') {
                    tempProxy.href = v;
                  }
                },
                configurable: true
              });

              console.log('[EarlyLocationInterceptor] Temporary interception established');
            } catch(e) {
              console.warn('[EarlyLocationInterceptor] Failed:', e);
            }
          }
        })();
      </script>
      <script src="${proxyOrigin}/__po.intcp.js?v=${Date.now()}"></script>`

      // 策略1: 尝试在第一个<script>标签之前注入
      const firstScriptRegex = /<script\b[^>]*>/i
      if (firstScriptRegex.test(html)) {
        return html.replace(firstScriptRegex, (match) => `${intcpScript}${match}`)
      }

      // 策略2: 在<head>标签之后立即注入
      const headTagRegex = /<head\b[^>]*>/i
      if (headTagRegex.test(html)) {
        return html.replace(headTagRegex, (match) => `${match}${intcpScript}`)
      }

      // 策略3: 在<html>标签之后立即注入
      const htmlTagRegex = /<html\b[^>]*>/i
      if (htmlTagRegex.test(html)) {
        return html.replace(htmlTagRegex, (match) => `${match}${intcpScript}`)
      }

      // 策略4: 在文档开头注入
      return intcpScript + html
    } catch (error) {
      console.error('[ProxyHTMLProcessor] Failed to inject interceptor:', error)
      return html
    }
  }

  private injectBaseTag(html: string, requestUrl: string): string {
    try {
      const url = new URL(requestUrl)
      const potValue = url.searchParams.get('__pot') || ''
      const domain = decodePot(potValue)
      url.searchParams.delete('__pot')
      const baseTag = `<base href="${domain}${url.pathname}${url.search}">`

      if (/<base\s+[^>]*>/i.test(html)) {
        return html.replace(/<base\s+[^>]*>/i, baseTag)
      }

      // 使用正则表达式匹配 head 标签，包括可能带有属性的情况
      const headTagRegex = /<head\b[^>]*>/i
      if (headTagRegex.test(html)) {
        return html.replace(headTagRegex, (match) => `${match}${baseTag}`)
      }

      return html
    } catch (error) {
      console.error('Failed to inject base tag:', error)
      return html
    }
  }

  /**
   * 处理带有integrity属性的标签
   * 对于非敏感资源，我们可以选择移除integrity属性来解决SRI验证失败问题
   */
  private processExistingIntegrity(html: string, requestUrl: string): string {
    // 定义正则表达式匹配开始标签
    const tagRegex = /<(script|link)(\s+[^>]*?)>/gi

    return html.replace(tagRegex, (match, tagName, attributes) => {
      // 检查属性中是否包含integrity
      if (attributes.includes('integrity=')) {
        console.log('[HTML] Found integrity in tag:', tagName)

        // 移除integrity属性 - 支持单引号和双引号
        const cleanedAttributes = attributes.replace(/\s+integrity=["'][^"']+["']/gi, '')

        return `<${tagName}${cleanedAttributes}>`
      }

      // 如果没有integrity属性，保持原样
      return match
    })
  }

  private processExistingMetaRefresh(html: string, requestUrl: string): string {
    return html.replace(
      /<meta\s+http-equiv=["']?refresh["']?\s+content=["']?\d*;\s*url=([^'">\s]+)["']?/gi,
      (match: string, url: string) => {
        const processedUrl = toProxyURL(url, requestUrl)
        return processedUrl ? match.replace(url, processedUrl) : match
      }
    )
  }

  /**
   * 处理预加载和预渲染链接标签
   * 匹配模式: <link rel="preload|prerender|prefetch|dns-prefetch" href="...">
   */
  private processExistingPreloadLinks(html: string, requestUrl: string): string {
    return html.replace(
      /<link\s+[^>]*rel=["']?(preload|prerender|prefetch|dns-prefetch)["']?[^>]*href=["']?([^'">\s]+)["']?[^>]*>/gi,
      (match: string, relType: string, url: string) => {
        try {
          console.log(`[HTML] Processing ${relType} link:`, url)
          const processedUrl = toProxyURL(url, requestUrl)
          return processedUrl ? match.replace(url, processedUrl) : match
        } catch (e) {
          console.warn(`[HTML] Failed to process ${relType} link:`, url, e)
          return match
        }
      }
    )
  }

  /**
   * 处理关键HTML元素的属性链接（a、area、form、input等）
   */
  private processExistingHTMLElement(html: string, requestUrl: string): string {
    return processHTMLElementLinks(html, requestUrl)
  }

  /**
   * 处理HTML元素事件处理属性中的JavaScript代码
   * 例如：onclick="location.href='xxx'" 替换为 onclick="__polct.href='xxx'"
   * @param html HTML内容
   * @returns 处理后的HTML内容
   */
  private processExistingEventHandlers(html: string): string {
    // 匹配具有事件处理属性的HTML元素
    // 匹配包含事件处理器（on*属性）的整个HTML标签
    return html.replace(/(<[^>]+?\s+on\w+\s*=\s*["'][^>]+?>)/gi, (match) => {
      // 如果匹配内容为空，直接返回原始匹配
      if (!match || !match.trim()) {
        return match
      }

      // 直接处理匹配内容中的 postMessage 和 location 引用
      match = processPostMessageCalls(match)
      match = processLocationReferences(match)

      return match
    })
  }

  /**
   * 处理 script 标签内的 JavaScript 代码，特别是 postMessage 调用
   * 只处理内联脚本（不处理外部脚本文件）
   * @param html HTML 内容
   * @returns 处理后的 HTML 内容
   */
  private processExistingScriptTags(html: string): string {
    // 使用正则表达式匹配所有内联脚本标签及其内容
    return html.replace(/(<script\b[^>]*>[\s\S]*?<\/script>)/gi, (match) => {
      // 如果脚本内容为空，直接返回原始匹配
      if (!match || !match.trim()) {
        return match
      }

      // 直接处理脚本内容中的 postMessage 和 location 引用
      match = processPostMessageCalls(match)
      match = processLocationReferences(match)

      return match
    })
  }
}
