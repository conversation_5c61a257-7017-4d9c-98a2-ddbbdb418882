import { toProxyURL } from '@/utils/url'
import { decodePot } from '@/utils/base64'
import { BaseProcessor } from './base.processor'
import {
  processPostMessageCalls,
  processLocationReferences,
  processHTMLElementLinks
} from '@/utils/processor.utils'

// HTML 内容类型常量
export const HTML_CONTENT_TYPES = ['text/html']

export class ProxyHTMLProcessor implements BaseProcessor {
  constructor() {}

  canProcess(contentType: string, url: string): boolean {
    return HTML_CONTENT_TYPES.some((type) => contentType.includes(type))
  }

  async process(content: string, requestUrl: string): Promise<string> {
    return this.processHTML(content, requestUrl)
  }

  processHTML(content: string, requestUrl: string): string {
    try {
      const result = this.pipe(content, [
        (html) => this.injectInterceptor(html, requestUrl),
        (html) => this.injectBaseTag(html, requestUrl),
        (html) => this.processExistingIntegrity(html, requestUrl),
        (html) => this.processExistingMetaRefresh(html, requestUrl),
        (html) => this.processExistingPreloadLinks(html, requestUrl),
        (html) => this.processExistingHTMLElement(html, requestUrl), // 处理关键HTML元素的属性链接（a、area、form、input等）
        (html) => this.processExistingEventHandlers(html), // 处理HTML元素属性中的JavaScript代码
        (html) => this.processExistingScriptTags(html) // 处理script标签内的JavaScript代码，location和postMessage替换
      ])

      return result
    } catch (error) {
      console.error('[HTML] Processing failed:', error)
      return content
    }
  }

  private pipe(input: string, fns: Array<(str: string) => string>): string {
    return fns.reduce((result, fn) => fn(result), input)
  }

  private injectInterceptor(html: string, requestUrl: string): string {
    try {
      const target = new URL(requestUrl)
      const proxyOrigin = target.origin
      const intcpScript = `<script src="${proxyOrigin}/__po.intcp.js?v=${Date.now()}"></script>`

      // 使用正则表达式匹配 head 标签，包括可能带有属性的情况
      const headTagRegex = /<head\b[^>]*>/i
      if (headTagRegex.test(html)) {
        return html.replace(headTagRegex, (match) => `${match}${intcpScript}`)
      }

      // 如果没有找到 head 标签，尝试在 HTML 开始处注入
      return html.replace(/(<html\b[^>]*>)/i, '$1<head>' + intcpScript + '</head>')
    } catch (error) {
      console.error('[ProxyHTMLProcessor] Failed to inject interceptor:', error)
      return html
    }
  }

  private injectBaseTag(html: string, requestUrl: string): string {
    try {
      const url = new URL(requestUrl)
      const potValue = url.searchParams.get('__pot') || ''
      const domain = decodePot(potValue)
      url.searchParams.delete('__pot')
      const baseTag = `<base href="${domain}${url.pathname}${url.search}">`

      if (/<base\s+[^>]*>/i.test(html)) {
        return html.replace(/<base\s+[^>]*>/i, baseTag)
      }

      // 使用正则表达式匹配 head 标签，包括可能带有属性的情况
      const headTagRegex = /<head\b[^>]*>/i
      if (headTagRegex.test(html)) {
        return html.replace(headTagRegex, (match) => `${match}${baseTag}`)
      }

      return html
    } catch (error) {
      console.error('Failed to inject base tag:', error)
      return html
    }
  }

  /**
   * 处理带有integrity属性的标签
   * 对于非敏感资源，我们可以选择移除integrity属性来解决SRI验证失败问题
   */
  private processExistingIntegrity(html: string, requestUrl: string): string {
    // 定义正则表达式匹配开始标签
    const tagRegex = /<(script|link)(\s+[^>]*?)>/gi

    return html.replace(tagRegex, (match, tagName, attributes) => {
      // 检查属性中是否包含integrity
      if (attributes.includes('integrity=')) {
        console.log('[HTML] Found integrity in tag:', tagName)

        // 移除integrity属性 - 支持单引号和双引号
        const cleanedAttributes = attributes.replace(/\s+integrity=["'][^"']+["']/gi, '')

        return `<${tagName}${cleanedAttributes}>`
      }

      // 如果没有integrity属性，保持原样
      return match
    })
  }

  private processExistingMetaRefresh(html: string, requestUrl: string): string {
    return html.replace(
      /<meta\s+http-equiv=["']?refresh["']?\s+content=["']?\d*;\s*url=([^'">\s]+)["']?/gi,
      (match: string, url: string) => {
        const processedUrl = toProxyURL(url, requestUrl)
        return processedUrl ? match.replace(url, processedUrl) : match
      }
    )
  }

  /**
   * 处理预加载和预渲染链接标签
   * 匹配模式: <link rel="preload|prerender|prefetch|dns-prefetch" href="...">
   */
  private processExistingPreloadLinks(html: string, requestUrl: string): string {
    return html.replace(
      /<link\s+[^>]*rel=["']?(preload|prerender|prefetch|dns-prefetch)["']?[^>]*href=["']?([^'">\s]+)["']?[^>]*>/gi,
      (match: string, relType: string, url: string) => {
        try {
          console.log(`[HTML] Processing ${relType} link:`, url)
          const processedUrl = toProxyURL(url, requestUrl)
          return processedUrl ? match.replace(url, processedUrl) : match
        } catch (e) {
          console.warn(`[HTML] Failed to process ${relType} link:`, url, e)
          return match
        }
      }
    )
  }

  /**
   * 处理关键HTML元素的属性链接（a、area、form、input等）
   */
  private processExistingHTMLElement(html: string, requestUrl: string): string {
    return processHTMLElementLinks(html, requestUrl)
  }

  /**
   * 处理HTML元素事件处理属性中的JavaScript代码
   * 例如：onclick="location.href='xxx'" 替换为 onclick="__polct.href='xxx'"
   * @param html HTML内容
   * @returns 处理后的HTML内容
   */
  private processExistingEventHandlers(html: string): string {
    // 匹配具有事件处理属性的HTML元素
    // 匹配包含事件处理器（on*属性）的整个HTML标签
    return html.replace(/(<[^>]+?\s+on\w+\s*=\s*["'][^>]+?>)/gi, (match) => {
      // 如果匹配内容为空，直接返回原始匹配
      if (!match || !match.trim()) {
        return match
      }

      // 直接处理匹配内容中的 postMessage 和 location 引用
      match = processPostMessageCalls(match)
      match = processLocationReferences(match)

      return match
    })
  }

  /**
   * 处理 script 标签内的 JavaScript 代码，特别是 postMessage 调用
   * 只处理内联脚本（不处理外部脚本文件）
   * @param html HTML 内容
   * @returns 处理后的 HTML 内容
   */
  private processExistingScriptTags(html: string): string {
    // 使用正则表达式匹配所有内联脚本标签及其内容
    return html.replace(/(<script\b[^>]*>[\s\S]*?<\/script>)/gi, (match) => {
      // 如果脚本内容为空，直接返回原始匹配
      if (!match || !match.trim()) {
        return match
      }

      // 直接处理脚本内容中的 postMessage 和 location 引用
      match = processPostMessageCalls(match)
      match = processLocationReferences(match)

      return match
    })
  }
}
