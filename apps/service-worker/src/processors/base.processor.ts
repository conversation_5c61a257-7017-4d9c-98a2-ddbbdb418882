/**
 * 内容处理器接口
 * 所有内容处理器需要实现此接口以便统一管理
 */
export interface BaseProcessor {
  /**
   * 判断是否可以处理指定的内容类型和URL
   * @param contentType - HTTP响应头中的Content-Type
   * @param url - 请求的URL
   * @returns 如果可以处理返回true，否则返回false
   */
  canProcess(contentType: string, url: string): boolean

  /**
   * 处理内容
   * @param content - 需要处理的内容字符串
   * @param url - 内容的来源URL，用于处理相对路径
   * @returns 处理后的内容
   */
  process(content: string, url: string): Promise<string>
}
