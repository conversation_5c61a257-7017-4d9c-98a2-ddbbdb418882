import { AbstractInterceptor } from './base.interceptor'
import { PROXY_RESERVED_COOKIES } from '../config/constants'

export class StorageInterceptor extends AbstractInterceptor {
  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    this.interceptStorage(localStorage)
    this.interceptStorage(sessionStorage)
  }

  private interceptStorage(storage: Storage): void {
    const originalGetItem = storage.getItem.bind(storage)
    const originalSetItem = storage.setItem.bind(storage)
    const originalRemoveItem = storage.removeItem.bind(storage)
    // const originalClear = storage.clear.bind(storage)
    const self = this

    storage.getItem = function (key: string): string | null {
      if (PROXY_RESERVED_COOKIES.includes(key)) {
        return originalGetItem(key)
      }
      const prefixedKey = `${self.config.target.hostname}:${key}`
      return originalGetItem(prefixedKey)
    }

    storage.setItem = function (key: string, value: string): void {
      if (PROXY_RESERVED_COOKIES.includes(key)) {
        return originalSetItem(key, value)
      }
      const prefixedKey = `${self.config.target.hostname}:${key}`
      return originalSetItem(prefixedKey, value)
    }

    storage.removeItem = function (key: string): void {
      if (PROXY_RESERVED_COOKIES.includes(key)) {
        return originalRemoveItem(key)
      }
      const prefixedKey = `${self.config.target.hostname}:${key}`
      return originalRemoveItem(prefixedKey)
    }

    storage.clear = function (): void {
      const keys = Object.keys(storage)
      const targetKeys = keys.filter((key) => key.startsWith(`${self.config.target.hostname}:`))
      targetKeys.forEach((key) => originalRemoveItem(key))
    }
  }
}
