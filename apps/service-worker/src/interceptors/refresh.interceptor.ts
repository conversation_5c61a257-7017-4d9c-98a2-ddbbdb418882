import { AbstractInterceptor } from './base.interceptor'

export class RefreshInterceptor extends AbstractInterceptor {
  private initialized = false

  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    if (this.initialized) return
    this.initialized = true

    try {
      this.interceptHardRefresh()
    } catch (error) {
      console.error('[Refresh] Failed to initialize refresh interceptor:', error)
    }
  }

  private interceptHardRefresh(): void {
    document.addEventListener(
      'keydown',
      (event: KeyboardEvent) => {
        const isHardRefresh =
          (event.ctrlKey && event.keyCode === 116) || // Ctrl + F5
          (event.shiftKey && event.keyCode === 116) || // Shift + F5
          (event.ctrlKey && event.shiftKey && event.keyCode === 82) || // Ctrl + Shift + R
          (event.metaKey && event.shiftKey && event.keyCode === 82) // Cmd + Shift + R (Mac)

        if (isHardRefresh) {
          console.info('[Refresh] Converting hard refresh to soft refresh')
          event.preventDefault()
          event.stopPropagation()

          // 使用软刷新替代强制刷新
          window.location.reload(false)
        }
      },
      true // 在捕获阶段处理,确保最先执行
    )
  }
}
