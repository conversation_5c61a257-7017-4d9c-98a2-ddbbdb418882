import { AbstractInterceptor } from './base.interceptor'

/**
 * History 拦截器
 * 用于劫持和重写 History API 相关操作，使其能够透明代理访问目标网站
 */
export class HistoryInterceptor extends AbstractInterceptor {
  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    this.interceptHistoryAPI()
    this.interceptWindowOpen()
  }

  private interceptHistoryAPI(): void {
    const originalPushState = history.pushState.bind(history)
    const originalReplaceState = history.replaceState.bind(history)
    const self = this

    history.pushState = (data: any, unused: string, url?: string | URL | null) => {
      if (url) {
        url = self.processUrl(url.toString())
      }
      return originalPushState(data, unused, url)
    }

    history.replaceState = (data: any, unused: string, url?: string | URL | null) => {
      if (url) {
        url = self.processUrl(url.toString())
      }
      return originalReplaceState(data, unused, url)
    }
  }

  private interceptWindowOpen(): void {
    const originalOpen = window.open
    const self = this
    window.open = (url?: string | URL | null, target?: string, features?: string) => {
      if (url) {
        url = self.processUrl(url.toString())
      }
      // @ts-ignore
      return originalOpen.call(window, url, target, features)
    }
  }
}
