import { toProxyURL } from '@/utils/url'

export interface BaseInterceptor {
  init(): void
}

export abstract class AbstractInterceptor implements BaseInterceptor {
  protected config: InterceptorConfig

  constructor(config: InterceptorConfig) {
    this.config = config
  }

  // 简化参数,只需要传入url
  protected processUrl(url: string | null | undefined): string {
    console.log('[Interceptor] Processing URL:', url, window.location.href)
    return toProxyURL(url || '', window.location.href)
  }

  abstract init(): void
}
