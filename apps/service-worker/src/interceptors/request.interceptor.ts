import { AbstractInterceptor } from './base.interceptor'

export class RequestInterceptor extends AbstractInterceptor {
  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    this.interceptXHR()
    this.interceptFetch()
    this.interceptFormSubmit()
    this.interceptWebSocket()
  }

  private interceptXHR(): void {
    const self = this
    const originalXHROpen = XMLHttpRequest.prototype.open
    const originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader
    const originalGetResponseURL = Object.getOwnPropertyDescriptor(
      XMLHttpRequest.prototype,
      'responseURL'
    )?.get

    // 拦截 open 方法，处理请求 URL
    XMLHttpRequest.prototype.open = function (
      method: string,
      url: string,
      async: boolean = true,
      username?: string | null,
      password?: string | null
    ) {
      this.__poOriginalUrl = url

      console.log('[XHR] Open with:', url)
      const processedUrl = self.processUrl(url)
      return originalXHROpen.call(this, method, processedUrl, async, username, password)
    }

    // 拦截 setRequestHeader 方法，添加额外的请求头
    XMLHttpRequest.prototype.setRequestHeader = function (name: string, value: string) {
      // 添加 referrer 相关头
      originalXHRSetRequestHeader.call(
        this,
        'X-PO-RF',
        document.__poOriginalReferrer || window.location.href
      )
      return originalXHRSetRequestHeader.call(this, name, value)
    }

    // 如果 responseURL 属性存在，拦截其获取
    if (originalGetResponseURL) {
      Object.defineProperty(XMLHttpRequest.prototype, 'responseURL', {
        get: function () {
          // 返回原始 URL
          console.log('[XHR] Response URL:', this.__poOriginalUrl)
          return this.__poOriginalUrl
        },
        enumerable: true,
        configurable: true
      })
    }
  }

  private interceptFetch(): void {
    const originalFetch = window.fetch
    const self = this

    window.fetch = (resource: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      // 创建请求头处理函数
      const createHeaders = (existingHeaders?: HeadersInit) => {
        const headers = new Headers(existingHeaders)
        headers.set('X-PO-RF', document.__poOriginalReferrer || window.location.href)
        return headers
      }

      // 处理 Request 对象
      if (resource instanceof Request) {
        resource = new Request(self.processUrl(resource.url), {
          ...resource,
          headers: createHeaders(resource.headers)
        })
      } else {
        // 处理 URL 字符串
        init = {
          ...init,
          headers: createHeaders(init?.headers)
        }
        resource = self.processUrl(resource.toString())
      }

      return originalFetch.call(window, resource, init)
    }
  }

  private interceptFormSubmit(): void {
    const originalSubmit = HTMLFormElement.prototype.submit

    HTMLFormElement.prototype.submit = function () {
      const pot = new URLSearchParams(window.location.search).get('__pot')
      if (pot && !this.querySelector('input[name="__pot"]')) {
        const input = document.createElement('input')
        input.type = 'hidden'
        input.name = '__pot'
        input.value = pot
        this.appendChild(input)
      }
      return originalSubmit.call(this)
    }
  }

  private interceptWebSocket(): void {
    const OriginalWebSocket = window.WebSocket
    const self = this

    window.WebSocket = new Proxy(OriginalWebSocket, {
      construct(target, args) {
        const [url, protocols] = args
        console.log('[WebSocket] Constructing with:', url, protocols)

        // Process the URL and add __pows prefix
        let processedUrl = self.processUrl(url.toString())
        if (processedUrl) {
          try {
            const wsUrl = new URL(processedUrl)
            // Add __pows prefix to the pathname
            wsUrl.pathname = '/__pows' + (wsUrl.pathname === '/' ? '' : wsUrl.pathname)
            processedUrl = wsUrl.toString()
          } catch (error) {
            console.error('[WebSocket] Failed to process URL:', error)
          }
        }

        return new target(processedUrl || url, protocols)
      }
    })
  }
}
