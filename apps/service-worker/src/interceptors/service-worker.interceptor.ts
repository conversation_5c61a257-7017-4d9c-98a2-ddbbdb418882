import { deletePot } from '@momo/utils'
import { AbstractInterceptor } from './base.interceptor'
import { SW_VERSION } from '../config/version'
import { appendQueryParam } from '@/utils/url'

export class ServiceWorkerInterceptor extends AbstractInterceptor {
  private checkInterval: number = 210
  private isChecking: boolean = false
  private initialized: boolean = false

  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    if (this.initialized) return
    this.initialized = true

    this.interceptServiceWorkerContainer()
    this.interceptProtocolHandler()
    this.initKeepAlive()
  }

  private interceptServiceWorkerContainer(): void {
    if (!('serviceWorker' in navigator)) return

    const self = this
    const swContainer = navigator.serviceWorker

    // 保存原始方法
    const originalRegister = swContainer.register
    const originalGetRegistration = swContainer.getRegistration
    const originalGetRegistrations = swContainer.getRegistrations

    // 拦截 register
    Object.defineProperty(swContainer, 'register', {
      value: async function (
        scriptURL: string | URL,
        options: RegistrationOptions = {}
      ): Promise<ServiceWorkerRegistration> {
        try {
          // 处理 URL
          let processedURL = self.processUrl(scriptURL.toString())

          // 添加标识，表示这是一个 service worker 请求
          try {
            const url = new URL(processedURL)
            processedURL = appendQueryParam(url, '__posw', '1')

            console.info('[SW] Added service worker identifier to URL:', processedURL)
          } catch (urlError) {
            console.error('[SW] Failed to add service worker identifier:', urlError)
          }

          options.scope = options.scope ? deletePot(self.processUrl(options.scope)) : '/'

          console.info('[SW] Registering service worker:', {
            url: processedURL,
            scope: options.scope
          })

          return await originalRegister.call(this, processedURL, options)
        } catch (error) {
          console.error('[SW] Registration failed:', error)
          throw error
        }
      },
      configurable: true,
      enumerable: true
    })

    // 拦截 getRegistration
    Object.defineProperty(swContainer, 'getRegistration', {
      value: async function (): Promise<ServiceWorkerRegistration | undefined> {
        try {
          return await originalGetRegistration.call(this, window.location.origin)
        } catch (error) {
          console.error('[SW] GetRegistration failed:', error)
          throw error
        }
      },
      configurable: true,
      enumerable: true
    })

    // 拦截 getRegistrations
    Object.defineProperty(swContainer, 'getRegistrations', {
      value: async function (): Promise<ServiceWorkerRegistration[]> {
        try {
          const registrations = await originalGetRegistrations.call(this)
          return registrations.filter((reg) => {
            const scope = new URL(reg.scope)
            return scope.origin === window.location.origin
          })
        } catch (error) {
          console.error('[SW] GetRegistrations failed:', error)
          return []
        }
      },
      configurable: true,
      enumerable: true
    })
  }

  // 禁止注册协议处理器
  private interceptProtocolHandler(): void {
    if ('registerProtocolHandler' in navigator) {
      Object.defineProperty(navigator, 'registerProtocolHandler', {
        value: () => {
          console.warn('[SW] Protocol handler registration is not allowed')
          return undefined
        },
        configurable: true,
        enumerable: true
      })
    }
  }

  private async initKeepAlive(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      console.warn('[SW] Service Worker is not supported')
      return
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration(window.location.origin)
      if (registration) {
        this.startKeepAliveCheck()
      }
    } catch (error) {
      console.error('[SW] Keep-alive initialization failed:', error)
    }
  }

  private startKeepAliveCheck(): void {
    setInterval(async () => {
      if (this.isChecking) return

      try {
        this.isChecking = true
        const registration = await navigator.serviceWorker.getRegistration(window.location.origin)

        if (!registration) {
          console.warn('[SW] Service worker was unregistered, attempting to re-register...')
          await this.reRegisterServiceWorker()
        } else if (registration.active?.state === 'redundant') {
          console.warn('[SW] Service worker is stopped/redundant, attempting to restart...')
          await registration.unregister()
          await this.reRegisterServiceWorker()
        }
      } catch (error) {
        console.error('[SW] Keep-alive check failed:', error)
      } finally {
        this.isChecking = false
      }
    }, this.checkInterval)
  }

  private async reRegisterServiceWorker(): Promise<ServiceWorkerRegistration> {
    try {
      const swUrl = `/__po.sw.js?v=${SW_VERSION}`
      console.log('[SW] Attempting to re-register service worker:', swUrl)

      const registration = await navigator.serviceWorker.register(swUrl, {
        scope: '/'
      })

      console.info('[SW] Successfully re-registered service worker')
      return registration
    } catch (error) {
      console.error('[SW] Failed to re-register service worker:', error)
      throw error
    }
  }
}
