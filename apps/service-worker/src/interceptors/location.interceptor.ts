import { AbstractInterceptor } from './base.interceptor'
import { deletePot } from '@momo/utils'
import { toProxyURL } from '../utils/url'

/**
 * Location 拦截器
 * 用于劫持和重写 window.location 相关操作，使其能够透明代理访问目标网站
 */
export class LocationInterceptor extends AbstractInterceptor {
  private polct: POLCT

  constructor(config: InterceptorConfig) {
    super(config)
    this.polct = new POLCT(config.target)
  }

  init(): void {
    this.initLocationMasking()
    this.interceptLocation()
  }

  private initLocationMasking(): void {
    try {
      Object.defineProperty(window, '__polct', {
        value: this.polct,
        writable: false,
        configurable: true,
        enumerable: false
      })

      Object.defineProperty(document, '__polct', {
        value: this.polct,
        writable: false,
        configurable: true,
        enumerable: false
      })
    } catch (e) {
      console.warn('[LocationMasking] Failed to define __polct:', e)
    }
  }

  /**
   * 拦截原生 location 赋值操作
   * 例如: window.location = 'https://example.com'
   */
  private interceptLocation(): void {
    const self = this
    try {
      const locationDescriptor =
        Object.getOwnPropertyDescriptor(Window.prototype, 'location') ||
        Object.getOwnPropertyDescriptor(window, 'location')

      if (locationDescriptor && locationDescriptor.configurable) {
        Object.defineProperty(window, 'location', {
          get: function () {
            return self.polct
          },
          set: function (url) {
            // 处理直接赋值的情况，如 window.location = 'https://example.com'
            if (typeof url === 'string') {
              self.polct.href = url
            } else {
              // 如果是对象赋值，则转发到原生 location
              locationDescriptor.set?.call(this, url)
            }
          },
          configurable: true
        })
      }
    } catch (e) {
      console.warn('[LocationInterceptor] Failed to intercept location:', e)
    }
  }
}

/**
 * 代理 Location 对象
 * 实现了所有 Location 接口的属性和方法
 */
class POLCT implements Location {
  readonly ancestorOrigins: DOMStringList = Object.freeze({
    length: 0,
    item: () => null,
    contains: () => false,
    [Symbol.iterator]: function* () {}
  }) as DOMStringList

  constructor(private target: TargetConfig) {}

  get host(): string {
    return this.target.port ? `${this.target.hostname}:${this.target.port}` : this.target.hostname
  }

  set host(v: string) {
    window.location.host = v
  }

  get hostname(): string {
    return this.target.hostname
  }

  set hostname(v: string) {
    window.location.hostname = v
  }

  get href(): string {
    const pathname = window.location.pathname || '/'
    const search = deletePot(window.location.search)
    const hash = window.location.hash || ''
    return `${this.target.protocol}//${this.target.hostname}${pathname}${search}${hash}`
  }

  set href(v: string) {
    window.location.href = toProxyURL(v, window.location.href)
  }

  get origin(): string {
    return `${this.target.protocol}//${
      this.target.port ? `${this.target.hostname}:${this.target.port}` : this.target.hostname
    }`
  }

  get port(): string {
    return this.target.port
  }

  set port(v: string) {
    window.location.port = v
  }

  get protocol(): string {
    return this.target.protocol
  }

  set protocol(v: string) {
    window.location.protocol = v
  }

  get search(): string {
    return deletePot(window.location.search)
  }

  set search(v: string) {
    window.location.search = v
  }

  get hash() {
    return window.location.hash
  }
  set hash(v) {
    window.location.hash = v
  }
  get pathname() {
    return window.location.pathname
  }
  set pathname(v) {
    window.location.pathname = v
  }
  reload() {
    window.location.reload()
  }
  toString() {
    return this.href
  }

  assign(url: string | URL): void {
    const proxyUrl = toProxyURL(url, window.location.href)
    window.location.assign(proxyUrl)
  }

  replace(url: string | URL): void {
    const proxyUrl = toProxyURL(url, window.location.href)
    window.location.replace(proxyUrl)
  }
}
