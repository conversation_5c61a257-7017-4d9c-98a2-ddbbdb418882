import { AbstractInterceptor } from './base.interceptor'
import { deletePot } from '@momo/utils'
import { toProxyURL } from '../utils/url'

/**
 * Location 拦截器
 * 用于劫持和重写 window.location 相关操作，使其能够透明代理访问目标网站
 * 增强版本：提供多层拦截，确保混淆代码无法绕过
 */
export class LocationInterceptor extends AbstractInterceptor {
  private polct: POLCT
  private isIntercepted = false

  constructor(config: InterceptorConfig) {
    super(config)
    this.polct = new POLCT(config.target)
  }

  init(): void {
    this.initEarlyInterception()
    this.initLocationMasking()
    this.interceptLocation()
    this.interceptLocationAccess()
    this.setupGlobalLocationProxy()
  }

  /**
   * 超早期拦截：在任何脚本执行前就建立基础拦截
   */
  private initEarlyInterception(): void {
    try {
      // 立即替换window.location，防止任何延迟
      if (!this.isIntercepted) {
        const descriptor = Object.getOwnPropertyDescriptor(window, 'location')
        if (descriptor && descriptor.configurable) {
          Object.defineProperty(window, 'location', {
            get: () => this.polct,
            set: (url: string) => {
              if (typeof url === 'string') {
                this.polct.href = url
              }
            },
            configurable: true,
            enumerable: true
          })
          this.isIntercepted = true
          console.log('[LocationInterceptor] Early interception established')
        }
      }
    } catch (e) {
      console.warn('[LocationInterceptor] Early interception failed:', e)
    }
  }

  private initLocationMasking(): void {
    try {
      Object.defineProperty(window, '__polct', {
        value: this.polct,
        writable: false,
        configurable: true,
        enumerable: false
      })

      Object.defineProperty(document, '__polct', {
        value: this.polct,
        writable: false,
        configurable: true,
        enumerable: false
      })
    } catch (e) {
      console.warn('[LocationMasking] Failed to define __polct:', e)
    }
  }

  /**
   * 拦截原生 location 赋值操作
   * 例如: window.location = 'https://example.com'
   */
  private interceptLocation(): void {
    const self = this
    try {
      // 如果早期拦截已经成功，跳过重复拦截
      if (this.isIntercepted) {
        console.log('[LocationInterceptor] Location already intercepted, skipping')
        return
      }

      const locationDescriptor =
        Object.getOwnPropertyDescriptor(Window.prototype, 'location') ||
        Object.getOwnPropertyDescriptor(window, 'location')

      if (locationDescriptor && locationDescriptor.configurable) {
        Object.defineProperty(window, 'location', {
          get: function () {
            return self.polct
          },
          set: function (url) {
            // 处理直接赋值的情况，如 window.location = 'https://example.com'
            if (typeof url === 'string') {
              self.polct.href = url
            } else {
              // 如果是对象赋值，则转发到原生 location
              locationDescriptor.set?.call(this, url)
            }
          },
          configurable: true
        })
        this.isIntercepted = true
      }
    } catch (e) {
      console.warn('[LocationInterceptor] Failed to intercept location:', e)
    }
  }

  /**
   * 拦截各种可能的location访问方式
   * 包括通过字符串拼接、动态属性访问等方式
   */
  private interceptLocationAccess(): void {
    const self = this
    try {
      // 拦截通过字符串访问的方式，如 window["location"]
      const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor
      Object.getOwnPropertyDescriptor = function(obj: any, prop: string | symbol) {
        if (obj === window && (prop === 'location' || prop === 'loc' + 'ation')) {
          return {
            get: () => self.polct,
            set: (url: string) => {
              if (typeof url === 'string') {
                self.polct.href = url
              }
            },
            configurable: true,
            enumerable: true
          }
        }
        return originalGetOwnPropertyDescriptor.call(this, obj, prop)
      }

      // 拦截 window 对象的属性访问
      const windowProxy = new Proxy(window, {
        get: (target, prop) => {
          if (prop === 'location' || prop === 'loc' + 'ation') {
            return this.polct
          }
          return Reflect.get(target, prop)
        },
        set: (target, prop, value) => {
          if (prop === 'location' || prop === 'loc' + 'ation') {
            if (typeof value === 'string') {
              this.polct.href = value
              return true
            }
          }
          return Reflect.set(target, prop, value)
        }
      })

      // 尝试替换全局window引用（在某些环境下可能有效）
      try {
        Object.defineProperty(globalThis, 'window', {
          get: () => windowProxy,
          configurable: true
        })
      } catch (e) {
        // 如果失败，不影响其他拦截
        console.debug('[LocationInterceptor] Global window proxy failed:', e)
      }

    } catch (e) {
      console.warn('[LocationInterceptor] Failed to setup advanced interception:', e)
    }
  }

  /**
   * 设置全局location代理，确保任何形式的访问都被拦截
   */
  private setupGlobalLocationProxy(): void {
    try {
      // 在全局作用域中定义location变量，覆盖可能的访问
      const script = document.createElement('script')
      script.textContent = `
        (function() {
          // 确保location始终指向我们的代理对象
          if (typeof window !== 'undefined' && window.__polct) {
            try {
              Object.defineProperty(window, 'location', {
                get: function() { return window.__polct; },
                set: function(v) {
                  if (typeof v === 'string') {
                    window.__polct.href = v;
                  }
                },
                configurable: true
              });

              // 也在全局作用域定义
              if (typeof globalThis !== 'undefined') {
                Object.defineProperty(globalThis, 'location', {
                  get: function() { return window.__polct; },
                  set: function(v) {
                    if (typeof v === 'string') {
                      window.__polct.href = v;
                    }
                  },
                  configurable: true
                });
              }
            } catch(e) {
              console.debug('[LocationProxy] Setup failed:', e);
            }
          }
        })();
      `

      // 立即执行脚本
      document.head?.appendChild(script)
      document.head?.removeChild(script)

    } catch (e) {
      console.warn('[LocationInterceptor] Failed to setup global proxy:', e)
    }
  }
}

/**
 * 代理 Location 对象
 * 实现了所有 Location 接口的属性和方法
 */
class POLCT implements Location {
  readonly ancestorOrigins: DOMStringList = Object.freeze({
    length: 0,
    item: () => null,
    contains: () => false,
    [Symbol.iterator]: function* () {}
  }) as DOMStringList

  constructor(private target: TargetConfig) {}

  get host(): string {
    return this.target.port ? `${this.target.hostname}:${this.target.port}` : this.target.hostname
  }

  set host(v: string) {
    window.location.host = v
  }

  get hostname(): string {
    return this.target.hostname
  }

  set hostname(v: string) {
    window.location.hostname = v
  }

  get href(): string {
    const pathname = window.location.pathname || '/'
    const search = deletePot(window.location.search)
    const hash = window.location.hash || ''
    return `${this.target.protocol}//${this.target.hostname}${pathname}${search}${hash}`
  }

  set href(v: string) {
    window.location.href = toProxyURL(v, window.location.href)
  }

  get origin(): string {
    return `${this.target.protocol}//${
      this.target.port ? `${this.target.hostname}:${this.target.port}` : this.target.hostname
    }`
  }

  get port(): string {
    return this.target.port
  }

  set port(v: string) {
    window.location.port = v
  }

  get protocol(): string {
    return this.target.protocol
  }

  set protocol(v: string) {
    window.location.protocol = v
  }

  get search(): string {
    return deletePot(window.location.search)
  }

  set search(v: string) {
    window.location.search = v
  }

  get hash() {
    return window.location.hash
  }
  set hash(v) {
    window.location.hash = v
  }
  get pathname() {
    return window.location.pathname
  }
  set pathname(v) {
    window.location.pathname = v
  }
  reload() {
    window.location.reload()
  }
  toString() {
    return this.href
  }

  assign(url: string | URL): void {
    const proxyUrl = toProxyURL(url, window.location.href)
    window.location.assign(proxyUrl)
  }

  replace(url: string | URL): void {
    const proxyUrl = toProxyURL(url, window.location.href)
    window.location.replace(proxyUrl)
  }
}
