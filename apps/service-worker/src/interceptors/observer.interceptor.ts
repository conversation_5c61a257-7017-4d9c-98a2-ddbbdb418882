import { SETTINGS_DOM_ATTRIBUTES } from '../config/constants'
import { AbstractInterceptor } from './base.interceptor'

// 定义属性处理类型的枚举
enum AttributeActionType {
  PROCESS_URL = 'process_url', // 处理URL
  REMOVE = 'remove' // 移除属性
}

// 扩展拦截器配置接口添加我们需要的字段
interface ObserverInterceptorConfig extends InterceptorConfig {
  throttleDelay?: number
}

type AttributeActionMap = Record<string, Record<string, AttributeActionType>>

export class ObserverInterceptor extends AbstractInterceptor {
  private observer: MutationObserver | null = null
  private attributeActions: AttributeActionMap = {}
  private throttleTimeout: number | null = null
  private pendingMutations: MutationRecord[] = []
  private throttleDelay = 100 // 节流延迟时间，可通过配置调整

  constructor(config: ObserverInterceptorConfig) {
    super(config)
    console.log('[Observer] Initializing observer interceptor')

    // 初始化配置
    this.initAttributeActions(config)
  }

  /**
   * 初始化属性处理配置
   */
  private initAttributeActions(config: ObserverInterceptorConfig): void {
    this.attributeActions = {}

    // 1. 添加默认的URL处理配置
    Object.entries(SETTINGS_DOM_ATTRIBUTES).forEach(([tagName, attrName]) => {
      this.addAttributeAction(tagName, attrName, AttributeActionType.PROCESS_URL)
    })

    // 2. 添加默认的integrity处理配置
    this.addAttributeAction('script', 'integrity', AttributeActionType.REMOVE)
    this.addAttributeAction('link', 'integrity', AttributeActionType.REMOVE)

    // 配置节流延迟
    if (config.throttleDelay) {
      this.throttleDelay = config.throttleDelay
    }

    console.log('[Observer] Attribute actions configured:', this.attributeActions)
  }

  /**
   * 添加属性处理配置
   */
  private addAttributeAction(
    tagName: string,
    attrName: string,
    actionType: AttributeActionType
  ): void {
    const tag = tagName.toLowerCase()

    // 确保标签对象存在
    if (!this.attributeActions[tag]) {
      this.attributeActions[tag] = {}
    }

    // 现在可以安全地设置属性操作
    this.attributeActions[tag][attrName] = actionType
  }

  init(): void {
    console.log('[Observer] Starting observer interception')
    this.setupDOMObserver()
  }

  /**
   * 设置 DOM 变化观察器
   */
  private setupDOMObserver(): void {
    if (this.observer) {
      this.observer.disconnect()
    }

    // 收集所有需要监听的属性名
    const attributesToWatch = this.collectAttributesToWatch()
    console.log('[Observer] Setting up observer for attributes:', attributesToWatch)

    // 创建 MutationObserver 实例
    this.observer = new MutationObserver(this.handleMutations.bind(this))

    // 开始观察整个文档
    this.observer.observe(document.documentElement, {
      childList: true, // 监听子节点的添加或删除
      subtree: true, // 监听整个子树
      attributes: true, // 监听属性变化
      attributeFilter: attributesToWatch // 只监听特定属性
    })
  }

  /**
   * 收集所有需要监听的属性
   */
  private collectAttributesToWatch(): string[] {
    const allAttributes = new Set<string>()

    Object.values(this.attributeActions).forEach((attrs) => {
      if (attrs) {
        Object.keys(attrs).forEach((attr) => allAttributes.add(attr))
      }
    })

    return [...allAttributes]
  }

  /**
   * 处理DOM变更
   */
  private handleMutations(mutations: MutationRecord[]): void {
    // 收集突变到缓冲区进行批处理
    this.pendingMutations.push(...mutations)

    // 使用节流来批量处理突变，提高性能
    if (this.throttleTimeout === null) {
      this.throttleTimeout = window.setTimeout(() => {
        this.processPendingMutations()
        this.throttleTimeout = null
      }, this.throttleDelay)
    }
  }

  /**
   * 批量处理挂起的突变
   */
  private processPendingMutations(): void {
    if (this.pendingMutations.length === 0) return

    const mutations = [...this.pendingMutations]
    this.pendingMutations = []

    console.log(`[Observer] Processing ${mutations.length} batched mutations`)

    // 使用 Set 来跟踪已处理的元素，避免重复处理
    const processedElements = new Set<Element>()

    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        // 处理新添加的节点
        this.processAddedNodes(mutation.addedNodes, processedElements)
      } else if (mutation.type === 'attributes' && mutation.target instanceof Element) {
        // 处理属性变更
        this.processAttributeChange(mutation, processedElements)
      }
    }
  }

  /**
   * 处理新添加的节点
   */
  private processAddedNodes(nodes: NodeList, processedElements: Set<Element>): void {
    nodes.forEach((node) => {
      if (node.nodeType === Node.ELEMENT_NODE) {
        this.processNewElement(node as Element, processedElements)
      }
    })
  }

  /**
   * 处理属性变更
   */
  private processAttributeChange(mutation: MutationRecord, processedElements: Set<Element>): void {
    const element = mutation.target as Element
    const tagName = element.tagName.toLowerCase()
    const attributeName = mutation.attributeName

    if (attributeName && this.shouldProcessAttribute(tagName, attributeName)) {
      if (!processedElements.has(element)) {
        this.processElementAttribute(element, attributeName)
        processedElements.add(element)
      }
    }
  }

  /**
   * 检查是否应该处理给定元素的属性
   */
  private shouldProcessAttribute(tagName: string, attributeName: string): boolean {
    const tag = tagName.toLowerCase()
    const tagConfig = this.attributeActions[tag]
    return !!(tagConfig && tagConfig[attributeName])
  }

  /**
   * 处理新添加的元素及其子元素
   */
  private processNewElement(element: Element, processedElements: Set<Element>): void {
    const tagName = element.tagName.toLowerCase()

    // 1. 处理元素本身
    this.processElementIfNeeded(element, tagName, processedElements)

    // 2. 处理子元素 - 只查询需要处理的标签类型
    this.processChildElements(element, processedElements)
  }

  /**
   * 处理元素（如果需要）
   */
  private processElementIfNeeded(
    element: Element,
    tagName: string,
    processedElements: Set<Element>
  ): void {
    const tagConfig = this.attributeActions[tagName]

    if (tagConfig && !processedElements.has(element)) {
      Object.keys(tagConfig).forEach((attrName) => {
        this.processElementAttribute(element, attrName)
      })
      processedElements.add(element)
    }
  }

  /**
   * 处理子元素
   */
  private processChildElements(element: Element, processedElements: Set<Element>): void {
    // 构建选择器，查找所有需要处理的子元素类型
    const tagSelectors = Object.keys(this.attributeActions)

    if (tagSelectors.length === 0) return

    const selector = tagSelectors.join(',')
    const children = element.querySelectorAll(selector)

    for (let i = 0; i < children.length; i++) {
      const child = children[i]
      if (child && !processedElements.has(child)) {
        const childTagName = child.tagName.toLowerCase()
        this.processElementIfNeeded(child, childTagName, processedElements)
      }
    }
  }

  /**
   * 处理单个元素的指定属性
   */
  private processElementAttribute(element: Element, attributeName: string): void {
    const tagName = element.tagName.toLowerCase()
    const tagConfig = this.attributeActions[tagName]

    if (!tagConfig) return

    const actionType = tagConfig[attributeName]
    if (!actionType) return

    try {
      // 根据处理类型执行不同操作
      switch (actionType) {
        case AttributeActionType.PROCESS_URL:
          this.processUrlAttribute(element, attributeName)
          break

        case AttributeActionType.REMOVE:
          this.removeAttribute(element, attributeName)
          break
      }
    } catch (e) {
      console.warn(`[Observer] Error processing ${attributeName}:`, e)
    }
  }

  /**
   * 处理URL类型的属性
   */
  private processUrlAttribute(element: Element, attributeName: string): void {
    const value = element.getAttribute(attributeName)
    if (!value) return

    console.log(`[Observer] Processing ${element.tagName.toLowerCase()}.${attributeName}: ${value}`)

    // 如果值中包含 __pot= 则不处理
    if (value.includes('__pot=')) {
      return
    }

    // 使用配置的处理器来处理 URL
    const processedValue = this.processUrl(value)

    // 只有在值发生变化时才更新属性
    if (processedValue && processedValue !== value) {
      element.setAttribute(attributeName, processedValue)
      console.log(`[Observer] Updated to: ${processedValue}`)
    }
  }

  /**
   * 移除属性
   */
  private removeAttribute(element: Element, attributeName: string): void {
    if (element.hasAttribute(attributeName)) {
      console.log(`[Observer] Removing ${attributeName} from ${element.tagName.toLowerCase()} tag`)
      element.removeAttribute(attributeName)
    }
  }

  /**
   * 停止观察器
   */
  public stop(): void {
    if (this.observer) {
      console.log('[Observer] Stopping observer')
      this.observer.disconnect()
      this.observer = null
    }

    if (this.throttleTimeout !== null) {
      clearTimeout(this.throttleTimeout)
      this.throttleTimeout = null
    }
  }
}
