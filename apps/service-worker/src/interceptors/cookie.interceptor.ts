import { AbstractInterceptor } from './base.interceptor'
import { PROXY_RESERVED_COOKIES } from '../config/constants'
import { decodePot } from '@/utils/base64'

export class CookieInterceptor extends AbstractInterceptor {
  private initialized = false
  private readonly cookieStore = new Map<string, string>()
  private readonly hostPrefix: string
  private readonly valuePrefix: string

  constructor(config: InterceptorConfig) {
    super(config)
    const pot = decodePot(config.pot)
    if (!pot) {
      throw new Error('Invalid pot parameter')
    }
    const url = new URL(pot)
    this.hostPrefix = `${url.hostname}#`

    // 从 cookie 获取时间戳或使用当前时间
    let timestamp = 0
    const sessionExpire = localStorage.getItem('__poex')
    if (sessionExpire) {
      timestamp = parseInt(sessionExpire, 10)
    }

    // 设置值前缀（时间戳加#号）
    this.valuePrefix = `${timestamp}#`
  }

  init(): void {
    if (this.initialized) return
    this.initialized = true

    try {
      this.interceptDocumentCookie()
      console.log('[Cookie Interceptor] Initialized with hostPrefix:', this.hostPrefix)
    } catch (error) {
      console.error('Failed to initialize cookie interceptor:', error)
    }
  }

  private interceptDocumentCookie(): void {
    const originalDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie')!
    const self = this

    // 添加原始cookie访问器属性到Document原型上，使其对所有文档都生效
    Object.defineProperty(Document.prototype, '__poOriginalCookie', {
      get: function (this: Document): string {
        return originalDescriptor.get!.call(this)
      },
      configurable: true,
      enumerable: true
    })

    // 重写Document.prototype.cookie的访问器属性
    Object.defineProperty(Document.prototype, 'cookie', {
      get: function (this: Document): string {
        return self.normalizeCookiesForReading(originalDescriptor.get!.call(this))
      },
      set: function (this: Document, value: string): void {
        const [cookieName, cookieValue, _] = self.parseCookieNameValue(value)

        // 先更新内存存储，避免不必要的处理
        if (cookieName && !PROXY_RESERVED_COOKIES.includes(cookieName)) {
          self.cookieStore.set(cookieName, cookieValue)
        }

        // 设置新的 cookie
        originalDescriptor.set!.call(this, self.formatCookieForSetting(value))
      },
      configurable: true,
      enumerable: true
    })
  }

  private parseCookieNameValue(cookieStr: string): [string, string, string[]] {
    // 分离 cookie 的名称值部分和指令部分
    const [nameValue = '', ...directiveParts] = cookieStr.split(';')
    const [name = '', ...valueParts] = nameValue.split('=')

    // 返回名称、值和指令部分
    return [name.trim(), valueParts.join('=').trim(), directiveParts.map((part) => part.trim())]
  }

  private normalizeCookiesForReading(cookieStr: string): string {
    if (!cookieStr) return ''

    const cookies = new Map<string, string>(this.cookieStore)

    for (const cookie of cookieStr.split(';')) {
      const [name, value, _] = this.parseCookieNameValue(cookie)
      if (!name) continue

      if (PROXY_RESERVED_COOKIES.includes(name)) {
        cookies.set(name, value)
      } else if (name.startsWith(this.hostPrefix)) {
        // 处理新格式cookie：域名#名称=时间戳#值
        // 直接使用hostPrefix提取原始名称
        const baseName = name.substring(this.hostPrefix.length)

        // 从值中提取真实值 (时间戳#值 -> 值)
        let realValue = value || ''
        const valueHashIndex = realValue.indexOf('#')
        if (valueHashIndex !== -1) {
          realValue = realValue.substring(valueHashIndex + 1)
        }

        cookies.set(baseName, realValue)
      }
    }

    return Array.from(cookies)
      .map(([name, value]) => `${name}=${value}`)
      .join('; ')
  }

  private formatCookieForSetting(cookieStr: string): string {
    const [name, value, directiveParts] = this.parseCookieNameValue(cookieStr)
    if (!name || PROXY_RESERVED_COOKIES.includes(name)) {
      return cookieStr
    }

    // 提取所有原始指令，过滤掉 httponly 和 domain 指令
    const directives = directiveParts.filter(
      (part) =>
        !part.toLowerCase().includes('httponly') && !part.toLowerCase().startsWith('domain=')
    )

    // 确保至少有 path=/ 指令
    if (!directives.some((d) => d.toLowerCase().startsWith('path='))) {
      directives.push('path=/')
    }

    // 构建并返回代理 cookie
    return [`${this.hostPrefix}${name}=${this.valuePrefix}${value}`, ...directives].join('; ')
  }
}
