import { AbstractInterceptor } from './base.interceptor'
export class DomainInterceptor extends AbstractInterceptor {
  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    this.interceptDomain()
  }

  private interceptDomain(): void {
    try {
      Object.defineProperty(document, 'domain', {
        get: () => this.config.target.hostname,
        set: () => this.config.target.hostname,
        configurable: true
      })
    } catch (e) {
      console.warn('[DomainMasking] Failed to redefine document.domain:', e)
    }
  }
}
