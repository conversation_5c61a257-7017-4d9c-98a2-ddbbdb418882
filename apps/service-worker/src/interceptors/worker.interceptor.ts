import { AbstractInterceptor } from './base.interceptor'

export class WorkerInterceptor extends AbstractInterceptor {
  private originalWorker: typeof Worker
  private originalSharedWorker: typeof SharedWorker | undefined

  constructor(config: InterceptorConfig) {
    super(config)
    this.originalWorker = window.Worker
    if ('SharedWorker' in window) {
      this.originalSharedWorker = window.SharedWorker
    }
  }

  init(): void {
    this.interceptWorkerConstructors()
  }

  /**
   * 拦截 Worker 和 SharedWorker 构造函数
   */
  private interceptWorkerConstructors(): void {
    const self = this
    const poSwUrl = this.config.serviceWorkerUrl

    // 拦截标准 Worker
    const WrappedWorker = function (scriptURL: string | URL, options?: WorkerOptions): Worker {
      const originalUrl = scriptURL.toString()

      // 如果是我们自己的 Service Worker 脚本，不做处理
      if (originalUrl === poSwUrl || originalUrl.includes('/__po.sw.js')) {
        return new self.originalWorker(originalUrl, options)
      }

      // 处理 URL
      const processedURL = self.processUrl(originalUrl)

      // 创建包含初始化代码的 blob
      const initCode = self.generateWorkerInitCode(poSwUrl, processedURL)
      const blob = new Blob([initCode], { type: 'application/javascript' })
      const blobUrl = URL.createObjectURL(blob)

      // 使用新的 blob URL 创建 worker
      const worker = new self.originalWorker(blobUrl, options)

      //   // 在 worker 启动后，我们可以安全地撤销 blob URL
      //   setTimeout(() => {
      //     URL.revokeObjectURL(blobUrl)
      //   }, 1000)

      return worker
    }

    // 使用类型断言处理类型问题
    window.Worker = WrappedWorker as unknown as typeof Worker

    // 保持原型链完整
    window.Worker.prototype = this.originalWorker.prototype

    // 如果支持 SharedWorker，也做类似处理
    if (this.originalSharedWorker) {
      const WrappedSharedWorker = function (
        scriptURL: string | URL,
        options?: string | WorkerOptions
      ): SharedWorker {
        const originalUrl = scriptURL.toString()

        // 如果是我们自己的 Service Worker 脚本，不做处理
        if (originalUrl === poSwUrl || originalUrl.includes('/__po.sw.js')) {
          return new self.originalSharedWorker!(originalUrl, options)
        }

        // 处理普通 URL
        const processedURL = self.processUrl(originalUrl)

        // 创建包含初始化代码的 blob
        const initCode = self.generateWorkerInitCode(poSwUrl, processedURL)
        const blob = new Blob([initCode], { type: 'application/javascript' })
        const blobUrl = URL.createObjectURL(blob)

        // 使用新的 blob URL 创建 worker
        const worker = new self.originalSharedWorker!(blobUrl, options)

        // 在 worker 启动后，我们可以安全地撤销 blob URL
        setTimeout(() => {
          URL.revokeObjectURL(blobUrl)
        }, 1000)

        return worker
      }

      // 使用类型断言处理类型问题
      window.SharedWorker = WrappedSharedWorker as unknown as typeof SharedWorker

      // 保持原型链完整
      window.SharedWorker.prototype = this.originalSharedWorker.prototype
    }

    console.log('[Worker] Intercepted Worker constructors')
  }

  /**
   * 生成 Worker 初始化代码，包含加载 service worker 脚本和原始脚本的逻辑
   */
  private generateWorkerInitCode(serviceWorkerUrl: string, originalScriptUrl: string): string {
    return `
importScripts('${window.location.origin}/__po.pm.js');

try {
  importScripts('${originalScriptUrl}');
} catch (e) {
  if (e.name === 'NetworkError' && '${originalScriptUrl}'.startsWith('blob:')) {
    console.warn('[PO Worker Error] ' + e.message + '. Trying the eval method...');
    fetch('${originalScriptUrl}')
      .then(function(response) {
        if (response.ok) {
          response.text().then(function(body) {
            eval.call(self, body);
          });
        }
      })
      .catch(function(e) {
        console.warn('[PO Worker Error] ' + e.message + '. Failed to fetch blob script ${originalScriptUrl}');
      });
  } else {
    throw e;
  }
}`
  }

  /**
   * 添加Worker标识参数到URL
   */
  private addWorkerFlag(url: string): string {
    try {
      const urlObj = new URL(url)
      urlObj.searchParams.set('__poww', '1')
      return urlObj.toString()
    } catch (e) {
      // URL解析失败，返回原始URL
      console.warn('[Worker] Failed to add worker flag to URL:', url, e)
      return url
    }
  }

  /**
   * 特殊处理 Worker 脚本 URL
   */
  protected override processUrl(url: string | null | undefined): string {
    if (!url) return ''

    // 处理特殊协议URL - 直接返回，无需处理
    if (
      url.startsWith('blob:') ||
      url.startsWith('data:') ||
      url.startsWith('javascript:') ||
      url.startsWith('about:')
    ) {
      return url
    }

    try {
      // 尝试将 URL 解析为绝对 URL
      new URL(url)
      // 如果没有抛出错误，说明是绝对 URL，使用基类处理
      const processedUrl = super.processUrl(url)
      return this.addWorkerFlag(processedUrl)
    } catch (e) {
      // 不是有效的绝对 URL，说明是相对路径
      // 这里我们需要将其解析为相对于当前页面的完整 URL

      // 获取当前页面的 URL（包含路径）
      const pageUrl = window.__polct.href

      // 直接使用 URL 构造函数处理相对路径
      // URL 构造函数的第二个参数是 base URL，可以自动处理相对路径
      const fullUrl = new URL(url, pageUrl).toString()

      // 传递给基类处理，添加代理参数等
      const processedUrl = super.processUrl(fullUrl)
      return this.addWorkerFlag(processedUrl)
    }
  }
}
