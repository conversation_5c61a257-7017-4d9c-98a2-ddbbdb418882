import { AbstractInterceptor } from './base.interceptor'
import { toOriginalUrl } from '@momo/utils'

export class ReferrerInterceptor extends AbstractInterceptor {
  private originalReferrer: string
  private initialized = false

  constructor(config: InterceptorConfig) {
    super(config)
    this.originalReferrer = document.referrer
  }

  init(): void {
    if (this.initialized) return
    this.initialized = true

    try {
      this.preserveOriginalReferrer()
      this.interceptReferrer()
    } catch (error) {
      console.error('[ReferrerInterceptor] Failed to initialize:', error)
    }
  }

  private preserveOriginalReferrer(): void {
    try {
      Object.defineProperty(Document.prototype, '__poOriginalReferrer', {
        value: this.originalReferrer,
        writable: false,
        configurable: true,
        enumerable: false
      })
    } catch (e) {
      console.error('[ReferrerInterceptor] Failed to preserve original referrer:', e)
    }
  }

  private processReferrer(ref: string): string {
    if (!ref) return ''

    try {
      const referrerUrl = new URL(ref)
      const pot = referrerUrl.searchParams.get('__pot')

      if (pot) {
        const realReferrer = toOriginalUrl(ref)
        if (new URL(realReferrer).origin === this.config.proxyOrigin) {
          return ''
        }
        return realReferrer
      }

      return ref
    } catch (e) {
      return ''
    }
  }

  private interceptReferrer(): void {
    try {
      Object.defineProperty(Document.prototype, 'referrer', {
        get: () => this.processReferrer(this.originalReferrer),
        configurable: true
      })
    } catch (e) {
      console.error('[ReferrerInterceptor] Failed to redefine document.referrer:', e)
    }
  }
}
