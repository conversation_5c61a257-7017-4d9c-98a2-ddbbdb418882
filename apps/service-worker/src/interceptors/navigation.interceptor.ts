import { AbstractInterceptor } from './base.interceptor'

/**
 * Navigation 拦截器
 * 使用现代浏览器的Navigation API拦截所有导航事件，包括location.href赋值
 * 在导航发生前修改目标地址，解决混淆代码绕过问题
 */
export class NavigationInterceptor extends AbstractInterceptor {
  private maliciousDomains: string[] = [
    '123av.com',
    '123av.ws', 
    '123av.gg',
    '1av.to',
    'njav.tv'
  ]

  constructor(config: InterceptorConfig) {
    super(config)
  }

  init(): void {
    this.interceptNavigationAPI()
    this.fallbackLocationInterception()
  }

  /**
   * 使用Navigation API拦截导航事件
   */
  private interceptNavigationAPI(): void {
    // 检查浏览器是否支持Navigation API
    if (typeof window !== 'undefined' && 'navigation' in window) {
      console.log('[NavigationInterceptor] Navigation API supported, setting up interception')
      
      window.navigation.addEventListener('navigate', (event: any) => {
        try {
          const destinationUrl = new URL(event.destination.url)
          
          console.log('[NavigationInterceptor] Navigation detected:', destinationUrl.href)
          
          // 检查是否是恶意重定向
          if (this.isMaliciousRedirect(destinationUrl)) {
            console.warn('[NavigationInterceptor] *** BLOCKED MALICIOUS NAVIGATION ***:', destinationUrl.href)
            
            // 阻止默认导航行为
            event.preventDefault()
            
            // 显示拦截通知
            this.showBlockedNotification(destinationUrl.href)
            
            return
          }
          
          // 检查是否需要代理处理
          if (this.shouldProxy(destinationUrl)) {
            console.log('[NavigationInterceptor] Proxying navigation:', destinationUrl.href)
            
            // 阻止默认导航
            event.preventDefault()
            
            // 使用代理URL进行导航
            const proxyUrl = this.processUrl(destinationUrl.href)
            window.navigation.navigate(proxyUrl)
            
            return
          }
          
          // 其他情况允许正常导航
          console.log('[NavigationInterceptor] Allowing normal navigation:', destinationUrl.href)
          
        } catch (error) {
          console.error('[NavigationInterceptor] Error handling navigation:', error)
          // 出错时允许正常导航
        }
      })
      
    } else {
      console.warn('[NavigationInterceptor] Navigation API not supported, using fallback')
    }
  }

  /**
   * 降级方案：拦截location相关操作
   */
  private fallbackLocationInterception(): void {
    const self = this
    
    try {
      // 拦截location.href setter
      const originalLocationDescriptor = Object.getOwnPropertyDescriptor(window, 'location')
      
      if (originalLocationDescriptor && originalLocationDescriptor.configurable) {
        const originalLocation = window.location
        
        Object.defineProperty(window, 'location', {
          get: function() {
            return originalLocation
          },
          set: function(url: string) {
            if (typeof url === 'string') {
              const targetUrl = new URL(url, window.location.href)
              
              // 检查是否是恶意重定向
              if (self.isMaliciousRedirect(targetUrl)) {
                console.warn('[NavigationInterceptor] *** BLOCKED MALICIOUS LOCATION ASSIGNMENT ***:', url)
                self.showBlockedNotification(url)
                return
              }
              
              // 检查是否需要代理
              if (self.shouldProxy(targetUrl)) {
                const proxyUrl = self.processUrl(url)
                originalLocation.href = proxyUrl
                return
              }
              
              // 正常赋值
              originalLocation.href = url
            }
          },
          configurable: true
        })
        
        console.log('[NavigationInterceptor] Fallback location interception established')
      }
      
    } catch (error) {
      console.warn('[NavigationInterceptor] Fallback interception failed:', error)
    }
  }

  /**
   * 检查是否是恶意重定向
   */
  private isMaliciousRedirect(url: URL): boolean {
    return this.maliciousDomains.some(domain => 
      url.hostname === domain || 
      url.hostname.endsWith('.' + domain)
    )
  }

  /**
   * 检查是否需要代理处理
   */
  private shouldProxy(url: URL): boolean {
    const currentOrigin = window.location.origin
    
    // 如果是外部URL且不是恶意域名，可能需要代理
    if (url.origin !== currentOrigin && !this.isMaliciousRedirect(url)) {
      // 这里可以根据项目需求决定是否代理外部链接
      // 暂时只记录，不自动代理
      console.log('[NavigationInterceptor] External URL detected, not auto-proxying:', url.href)
      return false
    }
    
    return false
  }

  /**
   * 显示拦截通知
   */
  private showBlockedNotification(blockedUrl: string): void {
    try {
      // 创建通知元素
      const notification = document.createElement('div')
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4757;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 999999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        font-weight: bold;
        max-width: 400px;
        word-break: break-word;
      `
      
      notification.innerHTML = `
        <div style="margin-bottom: 8px;">🛡️ 恶意重定向已被阻止</div>
        <div style="font-size: 12px; opacity: 0.9;">目标: ${blockedUrl}</div>
      `
      
      document.body.appendChild(notification)
      
      // 5秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 5000)
      
      // 点击关闭
      notification.addEventListener('click', () => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      })
      
    } catch (error) {
      console.error('[NavigationInterceptor] Failed to show notification:', error)
    }
  }

  /**
   * 添加恶意域名到黑名单
   */
  public addMaliciousDomain(domain: string): void {
    if (!this.maliciousDomains.includes(domain)) {
      this.maliciousDomains.push(domain)
      console.log('[NavigationInterceptor] Added malicious domain:', domain)
    }
  }

  /**
   * 移除恶意域名
   */
  public removeMaliciousDomain(domain: string): void {
    const index = this.maliciousDomains.indexOf(domain)
    if (index > -1) {
      this.maliciousDomains.splice(index, 1)
      console.log('[NavigationInterceptor] Removed malicious domain:', domain)
    }
  }
}
