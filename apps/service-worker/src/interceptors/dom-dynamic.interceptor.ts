import { AbstractInterceptor } from './base.interceptor'
import { SETTINGS_DOM_ATTRIBUTES } from '../config/constants'
import {
  processLocationReferences,
  processPostMessageCalls,
  processHTMLElementLinks
} from '@/utils/processor.utils'

export class DOMDynamicInterceptor extends AbstractInterceptor {
  constructor(config: InterceptorConfig) {
    super(config)
    console.log('[DOMDynamic] Initializing dom dynamic interceptor')
  }

  init(): void {
    console.log('[DOMDynamic] Starting dom dynamic interception')
    // this.interceptCreateElement()
    // this.interceptInnerHTML()
    // this.interceptInsertAdjacentHTML()
    // this.interceptOuterHTML()
    // this.interceptIntegrityAttributes()

    // 拦截 HTMLScriptElement.prototype.textContent setter
    this.interceptScriptTextContent()

    // 拦截 HTMLScriptElement.prototype.text setter
    this.interceptScriptText()

    // 拦截 document.createTextNode 方法
    this.interceptCreateTextNode()
  }

  private interceptCreateElement(): void {
    const originalCreateElement = document.createElement
    const self = this

    document.createElement = function (
      tagName: string,
      options?: ElementCreationOptions
    ): HTMLElement {
      const element = originalCreateElement.call(document, tagName, options)
      const tag = tagName.toLowerCase() as keyof typeof SETTINGS_DOM_ATTRIBUTES
      const attributeToHandle = SETTINGS_DOM_ATTRIBUTES[tag]

      if (attributeToHandle) {
        console.log(
          `[DOM] Intercepting element creation: ${tagName} with attribute ${attributeToHandle}`
        )
        self.interceptElementAttributes(element, attributeToHandle)
      }
      return element
    }
  }

  private interceptElementAttributes(element: Element, attributeToHandle: string): void {
    const originalSetAttribute = element.setAttribute.bind(element)
    const originalGetAttribute = element.getAttribute.bind(element)

    element.setAttribute = (name: string, value: string) => {
      if (name === attributeToHandle && value) {
        const processedValue = this.processUrl(value)
        if (processedValue !== value) {
          console.log(`[DOM] Processing setAttribute: ${name}=${value} -> ${processedValue}`)
          value = processedValue || value
        }
      }

      // 特殊处理 script 和 link 标签的 integrity 属性
      const tagName = element.tagName.toLowerCase()
      if ((tagName === 'script' || tagName === 'link') && name.toLowerCase() === 'integrity') {
        console.log(`[DOM] Removing integrity attribute from ${tagName} tag`)
        return
      }

      return originalSetAttribute(name, value)
    }

    element.getAttribute = (name: string) => {
      const value = originalGetAttribute(name)
      if (name === attributeToHandle && value) {
        try {
          const url = new URL(value)
          if (url.searchParams.has('__pot')) {
            const decodedValue = decodeURIComponent(url.searchParams.get('__pot') || '')
            console.log(`[DOM] Processing getAttribute: ${name}=${value} -> ${decodedValue}`)
            return decodedValue
          }
        } catch (e) {
          console.warn(`[DOM] Error processing getAttribute for ${name}:`, e)
        }
      }
      return value
    }

    this.interceptPropertyDescriptor(element, attributeToHandle)
  }

  private interceptPropertyDescriptor(element: Element, attributeToHandle: string): void {
    const self = this
    const originalPropDesc = Object.getOwnPropertyDescriptor(
      Object.getPrototypeOf(element),
      attributeToHandle
    )

    if (originalPropDesc && originalPropDesc.get && originalPropDesc.set) {
      console.log(`[DOM] Intercepting property descriptor for ${attributeToHandle}`)
      Object.defineProperty(element, attributeToHandle, {
        get: function (this: Element) {
          const getter = originalPropDesc.get as (this: Element) => string
          const value = getter.call(this)
          if (value) {
            try {
              const url = new URL(value)
              if (url.searchParams.has('__pot')) {
                const decodedValue = decodeURIComponent(url.searchParams.get('__pot') || '')
                console.log(
                  `[DOM] Processing property get: ${attributeToHandle}=${value} -> ${decodedValue}`
                )
                return decodedValue
              }
            } catch (e) {
              console.warn(`[DOM] Error processing property get for ${attributeToHandle}:`, e)
            }
          }
          return value
        },
        set: function (this: Element, value: string) {
          if (value) {
            const processedValue = self.processUrl(value)
            if (processedValue !== value) {
              console.log(
                `[DOM] Processing property set: ${attributeToHandle}=${value} -> ${processedValue}`
              )
              value = processedValue || value
            }
          }
          const setter = originalPropDesc.set as (this: Element, value: string) => void
          return setter.call(this, value)
        },
        enumerable: true,
        configurable: true
      })
    }
  }

  private interceptInnerHTML(): void {
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML')!
    const self = this

    Object.defineProperty(Element.prototype, 'innerHTML', {
      set: function (value: string) {
        if (typeof value === 'string') {
          const processedValue = self.processHTMLString(value)
          if (processedValue !== value) {
            console.log('[DOM] Processing innerHTML update')
            value = processedValue
          }
        }
        return originalInnerHTML.set?.call(this, value)
      },
      get: originalInnerHTML.get
    })
  }

  private processHTMLString(html: string): string {
    return processHTMLElementLinks(html, window.location.href)
  }

  private interceptInsertAdjacentHTML(): void {
    const originalInsertAdjacentHTML = Element.prototype.insertAdjacentHTML
    const self = this

    Element.prototype.insertAdjacentHTML = function (position: InsertPosition, text: string) {
      if (typeof text === 'string') {
        const processedText = self.processHTMLString(text)
        if (processedText !== text) {
          console.log('[DOM] Processing insertAdjacentHTML')
          text = processedText
        }
      }
      return originalInsertAdjacentHTML.call(this, position, text)
    }
  }

  private interceptOuterHTML(): void {
    const originalOuterHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'outerHTML')!
    const self = this

    Object.defineProperty(Element.prototype, 'outerHTML', {
      set: function (value: string) {
        if (typeof value === 'string') {
          const processedValue = self.processHTMLString(value)
          if (processedValue !== value) {
            console.log('[DOM] Processing outerHTML update')
            value = processedValue
          }
        }
        return originalOuterHTML.set?.call(this, value)
      },
      get: originalOuterHTML.get
    })
  }

  private interceptIntegrityAttributes(): void {
    console.log('[DOM] Setting up integrity attribute interception')

    // 需要处理 integrity 属性的标签
    const tagsWithIntegrity = ['SCRIPT', 'LINK']

    // 处理页面上已存在的带有 integrity 属性的元素
    document.querySelectorAll('script[integrity], link[integrity]').forEach((element) => {
      console.log(
        `[DOM] Removing integrity attribute from existing ${element.tagName.toLowerCase()} tag`
      )
      element.removeAttribute('integrity')
    })

    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver((mutations: MutationRecord[]) => {
      for (const mutation of mutations) {
        // 处理新添加的节点
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node: Node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              if (
                tagsWithIntegrity.includes(element.tagName) &&
                element.hasAttribute('integrity')
              ) {
                console.log(
                  `[DOM] Removing integrity from newly added ${element.tagName.toLowerCase()} tag`
                )
                element.removeAttribute('integrity')
              }
            }
          })
        }

        // 处理属性变更
        if (
          mutation.type === 'attributes' &&
          mutation.attributeName === 'integrity' &&
          mutation.target instanceof Element &&
          tagsWithIntegrity.includes(mutation.target.tagName)
        ) {
          console.log(
            `[DOM] Removing integrity attribute after it was added to ${mutation.target.tagName.toLowerCase()} tag`
          )
          mutation.target.removeAttribute('integrity')
        }
      }
    })

    observer.observe(document.documentElement, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['integrity']
    })
  }

  /**
   * 拦截脚本 textContent 属性设置
   */
  private interceptScriptTextContent(): void {
    const originalDescriptor = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent')
    if (!originalDescriptor || !originalDescriptor.set) return

    const originalSetter = originalDescriptor.set

    Object.defineProperty(HTMLScriptElement.prototype, 'textContent', {
      set(this: HTMLScriptElement, value: string) {
        if (value && value.trim()) {
          // 处理脚本内容
          const processedWithPostMessage = processPostMessageCalls(value)
          const processedContent = processLocationReferences(processedWithPostMessage)

          if (processedContent !== value) {
            console.log('[ContentRewrite] Processing dynamically set script textContent')
            value = processedContent
          }
        }
        return originalSetter.call(this, value)
      },
      get: originalDescriptor.get,
      configurable: true,
      enumerable: true
    })
  }

  /**
   * 拦截脚本 text 属性设置
   */
  private interceptScriptText(): void {
    // 只有在存在这个属性时才拦截
    if ('text' in HTMLScriptElement.prototype) {
      const originalDescriptor = Object.getOwnPropertyDescriptor(
        HTMLScriptElement.prototype,
        'text'
      )
      if (!originalDescriptor || !originalDescriptor.set) return

      const originalSetter = originalDescriptor.set

      Object.defineProperty(HTMLScriptElement.prototype, 'text', {
        set(this: HTMLScriptElement, value: string) {
          if (value && value.trim()) {
            // 处理脚本内容
            const processedWithPostMessage = processPostMessageCalls(value)
            const processedContent = processLocationReferences(processedWithPostMessage)

            if (processedContent !== value) {
              console.log('[ContentRewrite] Processing dynamically set script text')
              value = processedContent
            }
          }
          return originalSetter.call(this, value)
        },
        get: originalDescriptor.get,
        configurable: true,
        enumerable: true
      })
    }
  }

  /**
   * 拦截 document.createTextNode，用于处理动态添加到脚本的文本节点
   */
  private interceptCreateTextNode(): void {
    const originalCreateTextNode = document.createTextNode

    document.createTextNode = function (data: string): Text {
      // 创建原始文本节点
      const textNode = originalCreateTextNode.call(document, data)

      // 重写 textNode.nodeValue setter 以在节点添加到脚本元素时处理内容
      Object.defineProperty(textNode, 'nodeValue', {
        get: function () {
          return this.data
        },
        set: function (value: string) {
          // 原始设置逻辑
          this.data = value

          // 检查父节点是否为脚本元素
          if (this.parentNode && this.parentNode.nodeName === 'SCRIPT') {
            // 检查内容并处理
            if (value && value.trim()) {
              const processedWithPostMessage = processPostMessageCalls(value)
              const processedContent = processLocationReferences(processedWithPostMessage)

              if (processedContent !== value) {
                console.log('[ContentRewrite] Processing text node value in script')
                this.data = processedContent
              }
            }
          }
        },
        configurable: true,
        enumerable: true
      })

      return textNode
    }
  }
}
