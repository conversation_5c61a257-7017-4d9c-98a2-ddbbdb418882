import { ServiceWorkerManager } from './utils/service-worker.manager'
import { decodePot } from './utils/base64'

interface ProxyConfig {
  pot: string
  session?: string
  serverTs?: number
  targetDomain?: string
}

class LoadingUI {
  private static instance: LoadingUI
  private loadingText: HTMLDivElement | null = null
  private config: ProxyConfig

  private constructor(config: ProxyConfig) {
    this.config = config
  }

  static getInstance(config: ProxyConfig): LoadingUI {
    if (!LoadingUI.instance) {
      LoadingUI.instance = new LoadingUI(config)
    }
    return LoadingUI.instance
  }

  private createElements(): void {
    // 创建加载器容器
    const loader = document.createElement('div')
    loader.className = 'loader'

    // 创建文本容器
    this.loadingText = document.createElement('div')
    this.loadingText.className = 'loading-text'
    this.loadingText.textContent = 'Establishing Encrypted Connection...'

    // 确保body存在
    if (!document.body) {
      document.documentElement.appendChild(document.createElement('body'))
    }

    // 添加到页面
    document.body.appendChild(loader)
    document.body.appendChild(this.loadingText)
  }

  private injectStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      body {
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f8fafc;
        background-image:
          linear-gradient(30deg, #f1f5f9 12%, transparent 12.5%, transparent 87%, #f1f5f9 87.5%, #f1f5f9),
          linear-gradient(150deg, #f1f5f9 12%, transparent 12.5%, transparent 87%, #f1f5f9 87.5%, #f1f5f9),
          linear-gradient(30deg, #f1f5f9 12%, transparent 12.5%, transparent 87%, #f1f5f9 87.5%, #f1f5f9),
          linear-gradient(150deg, #f1f5f9 12%, transparent 12.5%, transparent 87%, #f1f5f9 87.5%, #f1f5f9),
          linear-gradient(60deg, #f1f5f977 25%, transparent 25.5%, transparent 75%, #f1f5f977 75%, #f1f5f977),
          linear-gradient(60deg, #f1f5f977 25%, transparent 25.5%, transparent 75%, #f1f5f977 75%, #f1f5f977);
        background-size: 80px 140px;
        background-position: 0 0, 0 0, 40px 70px, 40px 70px, 0 0, 40px 70px;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      .loader {
        border: 6px solid rgba(226, 232, 240, 0.8);
        border-radius: 50%;
        border-top: 6px solid #2563eb;
        width: 76px;
        height: 76px;
        animation: spin 1s linear infinite;
        margin-bottom: 28px;
        backdrop-filter: blur(4px);
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      .loading-text {
        color: #1e293b;
        font-size: 20px;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      }
    `
    // 确保head存在
    if (!document.head) {
      document.documentElement.appendChild(document.createElement('head'))
    }
    document.head.appendChild(style)
  }

  private async registerServiceWorker(): Promise<void> {
    try {
      const swManager = ServiceWorkerManager.getInstance()
      await swManager.register({ immediate: true })
      await swManager.waitForControl()
      console.log('[Client] Service Worker registered and activated')
    } catch (error) {
      console.error('[Client] Service Worker registration failed:', error)
      throw error
    }
  }

  private async verifySession(): Promise<boolean> {
    try {
      if (!this.config.session) {
        console.error('[Client] No session available for verification')
        this.showError('Session Expired')
        return false
      }

      const response = await fetch(`/__poa?__poss=${encodeURIComponent(this.config.session)}`, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      if (response.status !== 200) {
        throw new Error('Session Expired')
      }

      // 获取服务器的当前时间戳响应头
      const serverTs = response.headers.get('X-PO-TS')
      if (serverTs) {
        this.config.serverTs = parseInt(serverTs, 10)
      }

      // 获取服务器的会话过期时间响应头，并设置到localStorage
      const sessionExpire = response.headers.get('X-PO-EX')
      if (sessionExpire) {
        localStorage.setItem('__poex', sessionExpire)
      }

      console.log('[Client] Session verification successful')
      return true
    } catch (error) {
      console.error('[Client] Session verification failed:', error)
      this.showError('Session Expired')
      return false
    }
  }

  private buildCleanUrl(): string {
    const url = new URL(window.location.href)
    url.searchParams.delete('__poss')
    return url.toString()
  }

  private cleanCookies(): void {
    try {
      const targetDomain = this.config.targetDomain
      if (!targetDomain) {
        console.log('[Client] No target domain found, skipping target domain cookies')
      }

      // 获取当前时间戳，优先使用服务器时间戳
      const currentTs = this.config.serverTs || 1
      // 8小时前的时间戳 (8 * 60 * 60 = 28800秒)
      const eightHoursAgo = currentTs - 28800

      const cookies = (document.__poOriginalCookie || document.cookie).split(';')
      let targetDomainCount = 0
      let expiredCount = 0

      for (const cookieStr of cookies) {
        if (!cookieStr.trim()) continue

        // 解析cookie
        const [nameRaw, valueRaw] = cookieStr.split('=').map((part) => part.trim())
        if (!nameRaw || nameRaw.startsWith('__po')) continue // 跳过__po开头的cookie

        let shouldDelete = false

        // 情况1: 目标域名的所有cookie
        if (targetDomain && nameRaw.startsWith(`${targetDomain}#`)) {
          shouldDelete = true
          targetDomainCount++
        }

        // 情况2: 8小时前的所有cookie（基于时间戳）
        if (!shouldDelete && valueRaw && valueRaw.includes('#')) {
          const timestampMatch = valueRaw.match(/^(\d+)#/)
          if (timestampMatch && timestampMatch[1]) {
            const cookieTimestamp = parseInt(timestampMatch[1], 10)
            if (cookieTimestamp < eightHoursAgo) {
              shouldDelete = true
              expiredCount++
            }
          }
        }

        if (shouldDelete) {
          // 删除cookie
          document.cookie = `${nameRaw}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`

          // 同时删除没有domain前缀的相同cookie
          const cookieNameWithoutPrefix = nameRaw.substring(`${targetDomain}#`.length)
          document.cookie = `${cookieNameWithoutPrefix}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
        }
      }

      console.log(
        `[Client] Cleaned ${targetDomainCount} target domain cookies and ${expiredCount} expired cookies`
      )
    } catch (error) {
      console.error('[Client] Failed to clean cookies:', error)
    }
  }

  private showError(message: string): void {
    if (this.loadingText) {
      this.loadingText.style.color = '#dc2626'
      this.loadingText.textContent = message
    }
    const loader = document.querySelector('.loader')
    if (loader) {
      loader.remove()
    }
  }

  public async initialize(): Promise<void> {
    try {
      // 确保DOM已经准备好
      if (document.readyState === 'loading') {
        await new Promise<void>((resolve) => {
          document.addEventListener('DOMContentLoaded', () => resolve(), { once: true })
        })
      }

      // 注入样式和加载动画
      this.injectStyles()
      this.createElements()

      // 验证 session
      if (this.config.session) {
        const verified = await this.verifySession()
        if (!verified) {
          return
        }
      }

      // 在一切就绪后，清理过期cookie
      this.cleanCookies()

      // 注册 Service Worker
      await this.registerServiceWorker()

      // 跳转到清理后的URL
      const cleanUrl = this.buildCleanUrl()
      window.location.replace(cleanUrl)
    } catch (error) {
      console.error('[Client] Initialization failed:', error)
      this.showError('Session Expired')
    }
  }
}

function init(): void {
  const url = new URL(window.location.href)
  const pot = url.searchParams.get('__pot')
  const sessionParam = url.searchParams.get('__poss')

  if (!pot) {
    console.error('[Client] POT not found')
    // 确保body存在
    if (!document.body) {
      document.documentElement.appendChild(document.createElement('body'))
    }
    document.body.innerHTML =
      '<div style="text-align:center;color:#dc2626;padding:2rem;">Error: Missing destination parameter</div>'
    return
  }

  // 防止页面被嵌入iframe
  if (window.top !== window.self && window.top) {
    window.top.location.href = window.location.href
    return
  }

  // 从pot解析目标域名
  let targetDomain = ''
  try {
    const targetUrl = decodePot(pot)
    targetDomain = targetUrl ? new URL(targetUrl).hostname : ''
  } catch (error) {
    console.error('[Client] Failed to decode pot or extract domain:', error)
  }

  const ui = LoadingUI.getInstance({
    pot,
    session: sessionParam || undefined,
    targetDomain
  })

  // 立即初始化
  void ui.initialize()
}

// 执行初始化
init()
