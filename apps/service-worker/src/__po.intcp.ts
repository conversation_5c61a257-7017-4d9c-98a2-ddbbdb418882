import { decodePot } from '@/utils/base64'
import { DOMDynamicInterceptor } from './interceptors/dom-dynamic.interceptor'
import { DomainInterceptor } from './interceptors/domain.interceptor'
import { LocationInterceptor } from './interceptors/location.interceptor'
import { NavigationInterceptor } from './interceptors/navigation.interceptor'
import { HistoryInterceptor } from './interceptors/history.interceptor'
import { PostMessageInterceptor } from './interceptors/post-message.interceptor'
import { RequestInterceptor } from './interceptors/request.interceptor'
import { ServiceWorkerInterceptor } from './interceptors/service-worker.interceptor'
import { StorageInterceptor } from './interceptors/storage.interceptor'
import { CookieInterceptor } from './interceptors/cookie.interceptor'
import { RefreshInterceptor } from './interceptors/refresh.interceptor'
import { ReferrerInterceptor } from './interceptors/referrer.interceptor'
import { WorkerInterceptor } from './interceptors/worker.interceptor'
import { SW_VERSION } from './config/version'
import { ObserverInterceptor } from './interceptors/observer.interceptor'

class ProxyInterceptor {
  private config: InterceptorConfig

  constructor() {
    this.config = this.getProxyConfig()

    // 设置全局代理标识，用于iframe检测
    window.__proxyInjected = true

    // 初始化拦截器
    this.initializeInterceptors()
  }

  private getProxyConfig(): InterceptorConfig {
    const searchParams = new URLSearchParams(window.location.search)
    const pot = searchParams.get('__pot') || ''
    const targetUrlStr = pot ? decodePot(pot) : ''
    const targetUrl = new URL(targetUrlStr)

    return {
      proxyOrigin: window.location.origin,
      pot,
      targetUrl: targetUrlStr,
      target: {
        protocol: targetUrl.protocol,
        hostname: targetUrl.hostname,
        port: targetUrl.port
      },
      serviceWorkerUrl: `/__po.sw.js?v=${SW_VERSION}`
    }
  }

  private initializeInterceptors(): void {
    const interceptors = [
      new NavigationInterceptor(this.config),
      new ServiceWorkerInterceptor(this.config),
      new LocationInterceptor(this.config),
      new HistoryInterceptor(this.config),
      new DomainInterceptor(this.config),
      new StorageInterceptor(this.config),
      new CookieInterceptor(this.config),
      new ReferrerInterceptor(this.config),
      new PostMessageInterceptor(this.config),
      new RefreshInterceptor(this.config),
      new DOMDynamicInterceptor(this.config),
      new RequestInterceptor(this.config),
      new WorkerInterceptor(this.config),
      new ObserverInterceptor(this.config)
    ]

    // 初始化所有拦截器
    interceptors.forEach((interceptor) => interceptor.init())

    console.log('[ProxyOrb] All interceptors initialized successfully')
  }
}

// 启动代理拦截器
new ProxyInterceptor()
