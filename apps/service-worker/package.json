{"private": true, "name": "@momo/service-worker", "version": "0.0.0", "scripts": {"build": "NODE_ENV=production tsx scripts/build-script.ts", "dev": "BUILD_WATCH=true NODE_ENV=development tsx scripts/build-script.ts", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest watch", "type-check": "tsc --noEmit"}, "dependencies": {}, "devDependencies": {"@momo/env": "workspace:*", "@momo/eslint-config": "workspace:*", "@momo/tailwind-config": "workspace:*", "@momo/tsconfig": "workspace:*", "@momo/utils": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@types/http-proxy": "^1.17.15", "@types/javascript-obfuscator": "^0.17.0", "@types/node": "^22.10.7", "@types/terser": "^3.12.0", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "concurrently": "^8.2.2", "javascript-obfuscator": "^4.1.1", "terser": "^5.37.0", "tsup": "8.3.0", "vitest": "^1.6.1"}, "lint-staged": {"*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix", "**/*": "prettier --write --ignore-unknown"}, "turbo": {"build": {"inputs": ["src/**/*"], "outputs": ["../nginx-lua/html/__po*.js"]}}}