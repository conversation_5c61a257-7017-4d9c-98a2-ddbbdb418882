{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"baseUrl": ".", "lib": ["dom", "dom.iterable", "esnext", "webworker"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["src/*"]}, "sourceMap": false, "typeRoots": ["src/types", "./node_modules/@types"]}, "exclude": ["node_modules", "dist"], "extends": "@momo/tsconfig/base.json", "include": ["src/**/*.ts", "scripts/**/*.ts"]}