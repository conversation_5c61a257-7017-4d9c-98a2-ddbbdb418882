import { readFileSync, writeFileSync, createWriteStream } from 'fs'
import { promises as fs } from 'fs'
import * as https from 'https'
import * as JavaScriptObfuscator from 'javascript-obfuscator'
import { join } from 'path'
import { minify } from 'terser'
import { build } from 'tsup'
import type { Options } from 'tsup'

const isDev = process.env.NODE_ENV === 'development'
const isWatch = process.env.BUILD_WATCH === 'true'
// 生成随机版本号
const SW_VERSION = Math.floor(Math.random() * 1000000).toString()

// 构建配置
const BUILD_CONFIG = {
  outDir: isDev ? 'dist' : '../nginx-lua/html',
  tsconfig: './tsconfig.json',
  entries: {
    sw: ['src/__po.sw.ts', '__po.sw.js'],
    postMessage: ['src/__po.pm.ts', '__po.pm.js'],
    interceptor: ['src/__po.intcp.ts', '__po.intcp.js'],
    loading: ['src/__po.loading.ts', '__po.loading.js']
  }
} as const

const MINIFY_OPTIONS = {
  sourceMap: false,
  compress: {
    dead_code: true,
    drop_console: true,
    drop_debugger: true,
    keep_fnames: false,
    keep_classnames: false,
    passes: 3,
    unsafe: true,
    unsafe_math: true,
    unsafe_methods: true,
    pure_getters: true,
    reduce_vars: true,
    sequences: true
  },
  mangle: {
    toplevel: true,
    safari10: true,
    properties: {
      regex: /^_(?!_po)/
    }
  },
  format: {
    comments: false,
    beautify: false
  }
} as const

const OBFUSCATE_OPTIONS = {
  // 基础混淆
  compact: true,
  simplify: true,
  sourceMap: false,
  transformObjectKeys: false,

  // 控制流扁平化
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.5,

  // 死代码注入
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.2,

  // 调试保护
  debugProtection: false,
  debugProtectionInterval: 2000,
  disableConsoleOutput: true,

  // 标识符处理
  identifierNamesGenerator: 'hexadecimal',
  renameProperties: true,
  renamePropertiesMode: 'safe',
  reservedNames: ['^__po'],

  // 字符串处理
  splitStrings: true,
  splitStringsChunkLength: 5,
  stringArray: true,
  stringArrayEncoding: ['base64'],
  stringArrayThreshold: 0.8,
  stringArrayWrappersCount: 1,
  stringArrayWrappersType: 'variable',

  // 其他选项
  numbersToExpressions: false,
  unicodeEscapeSequence: false
} as const

// 构建配置生成器
const createBuildConfig = (): Options => ({
  format: ['iife'],
  platform: 'browser',
  target: ['es2020'],
  minify: false,
  clean: false,
  watch: isWatch,
  outDir: BUILD_CONFIG.outDir,
  tsconfig: BUILD_CONFIG.tsconfig,
  splitting: false,
  treeshake: false,
  dts: false,
  sourcemap: isDev ? 'inline' : false,
  noExternal: [/.*/],
  outExtension: () => ({ js: '.js' }),
  define: {
    'process.env.SW_VERSION': `'${SW_VERSION}'`
  }
})

// 代码编码器
class CodeEncoder {
  private static generateKey(): string {
    return Array.from(crypto.getRandomValues(new Uint8Array(16)))
      .map((b) => b.toString(16).padStart(2, '0'))
      .join('')
      .slice(0, 16)
  }

  private static safeEncode(str: string): string {
    return Buffer.from(str)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  static encode(code: string): string {
    const key = this.generateKey()
    const encodedCode = this.safeEncode(code)

    return `(function(){try{const k='${key}',c='${encodedCode}',d=s=>{s=s.replace(/-/g,'+').replace(/_/g,'/');const p=(4-s.length%4)%4;return atob(s+'='.repeat(p))};return new Function(d(c))()}catch(e){return null}})();`
  }
}

// 代码处理器
async function processOutput(code: string, outPath: string): Promise<void> {
  let minified: any = null

  try {
    // 压缩
    minified = await minify(code, MINIFY_OPTIONS)
    if (!minified.code) {
      throw new Error('Minification failed')
    }

    // 编码
    const processedCode = CodeEncoder.encode(minified.code)

    // 混淆
    const fileSize = Buffer.from(processedCode).length
    const obfuscateConfig = {
      ...OBFUSCATE_OPTIONS,
      controlFlowFlatteningThreshold: fileSize > 50000 ? 0.3 : 0.5,
      deadCodeInjectionThreshold: fileSize > 50000 ? 0.1 : 0.2,
      stringArrayThreshold: fileSize > 50000 ? 0.5 : 0.8
    }

    const obfuscated = JavaScriptObfuscator.obfuscate(processedCode, obfuscateConfig as any)

    // 输出
    writeFileSync(outPath, obfuscated.getObfuscatedCode())
  } catch (error) {
    console.error('Processing error:', error)
    writeFileSync(outPath, minified?.code || code)
    throw error
  }
}

// 文件构建器
async function buildFile(entry: string, outFile: string, name: string): Promise<void> {
  const outPath = join(process.cwd(), BUILD_CONFIG.outDir, outFile)

  try {
    await build({
      ...createBuildConfig(),
      entry: [entry],
      name,
      async onSuccess() {
        if (!isDev) {
          const code = readFileSync(outPath, 'utf8')
          await processOutput(code, outPath)
        }
      }
    })
  } catch (error) {
    throw new Error(
      `Failed to build ${name}: ${error instanceof Error ? error.message : String(error)}`
    )
  }
}

// 下载指纹识别库
async function downloadFingerprintJS(outDir: string): Promise<void> {
  const fpPath = join(process.cwd(), outDir, '__po.fp.js')

  try {
    // 检查文件是否存在
    try {
      await fs.access(fpPath)
      console.log('Fingerprint JS already exists, skipping download')
      return
    } catch {
      // 文件不存在，继续下载
    }

    // 确保目录存在
    await fs.mkdir(join(process.cwd(), outDir), { recursive: true })

    // 下载文件
    await new Promise<void>((resolve, reject) => {
      https
        .get(
          'https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@4/dist/fp.min.js',
          (response) => {
            if (response.statusCode !== 200) {
              reject(new Error(`Failed to download: ${response.statusCode}`))
              return
            }

            const fileStream = createWriteStream(fpPath)
            response.pipe(fileStream)

            fileStream.on('finish', () => {
              fileStream.close()
              console.log('Successfully downloaded FingerprintJS')
              resolve()
            })
          }
        )
        .on('error', reject)
    })
  } catch (error) {
    console.error('Error downloading FingerprintJS:', error)
    throw error
  }
}

// 主函数
async function main() {
  try {
    // 下载指纹识别库
    await downloadFingerprintJS(BUILD_CONFIG.outDir)

    await Promise.all(
      Object.entries(BUILD_CONFIG.entries).map(([name, [entry, outFile]]) =>
        buildFile(entry, outFile, name)
      )
    )
    console.log('--- Script Build completed successfully --- ')
  } catch (error) {
    console.error('Build failed:', error)
    process.exit(1)
  }
}

// 启动构建
main().catch((error) => {
  console.error('Fatal error:', error)
  process.exit(1)
})
