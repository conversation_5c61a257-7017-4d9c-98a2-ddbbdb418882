# 设置 worker 进程数
worker_processes auto;

# 设置默认日志级别为 info
error_log /usr/local/openresty/nginx/logs/error.log info;

pid /var/run/nginx.pid;

# 引入Stream模块配置
include /usr/local/openresty/nginx/conf/stream.conf;

events {
    # 每个 worker 进程的最大连接数
    worker_connections 65535;

    # 使用 epoll 事件模型
    use epoll;

    # 启用多个接受连接的系统调用
    multi_accept on;
}

http {
    include mime.types;
    default_type application/octet-stream;

    # 优化基础配置

    # 优化基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 300; # 增加到300s以支持长连接
    keepalive_requests 10000; # 增加到10000以提高性能
    keepalive_disable none; # 禁用keepalive_disable

    # 优化缓冲区配置
    client_body_buffer_size 256k; # 增加到256k以处理较大的请求体
    client_max_body_size 100m; # 增加到100m以支持大文件上传
    client_header_buffer_size 4k; # 增加到4k以处理较长的请求头
    large_client_header_buffers 8 16k; # 增加到8 16k以处理更多的请求头
    proxy_buffers 32 128k; # 增加到32 128k以提高代理性能
    proxy_buffer_size 128k; # 增加到128k以处理较大的响应头
    proxy_busy_buffers_size 256k; # 增加到256k
    proxy_temp_file_write_size 256k; # 增加到256k以提高写入性能

    # Docker 设置的 DNS 解析器
    resolver ******* ******* ******* ******* valid=30s ipv6=off;
    resolver_timeout 15s;

    # 同时输出到 stdout 和文件
    access_log /usr/local/openresty/nginx/logs/access.log;

    include /usr/local/openresty/nginx/conf/conf.d/*.conf;
}
