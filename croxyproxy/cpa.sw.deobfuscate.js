;(function f() {
  ;((p3, p4) => {
    if (typeof module == 'object' && module.exports) {
      module.exports = p4(require('./punycode'), require('./IPv6'), require('./SecondLevelDomains'))
    } else if (typeof define == 'function' && define.amd) {
      define(['./punycode', './IPv6', './SecondLevelDomains'], p4)
    } else {
      p3.URI = p4(p3.punycode, p3.IPv6, p3.SecondLevelDomains, p3)
    }
  })(this, function (p5, p6, p7, p8) {
    var v4 = p8 && p8.URI
    function f2(p9, p10) {
      var v5 = arguments.length >= 1
      if (!(this instanceof f2)) {
        if (v5) {
          if (arguments.length >= 2) {
            return new f2(p9, p10)
          } else {
            return new f2(p9)
          }
        } else {
          return new f2()
        }
      }
      if (p9 === undefined) {
        if (v5) {
          throw new TypeError('undefined is not a valid argument for URI')
        }
        p9 = typeof location != 'undefined' ? location.href + '' : ''
      }
      if (p9 === null && v5) {
        throw new TypeError('null is not a valid argument for URI')
      }
      this.href(p9)
      if (p10 !== undefined) {
        return this.absoluteTo(p10)
      } else {
        return this
      }
    }
    f2.version = '1.19.11'
    var v6 = f2.prototype
    var v7 = Object.prototype.hasOwnProperty
    function f3(p11) {
      return p11.replace(/([.*+?^=!:${}()|[\]\/\\])/g, '\\$1')
    }
    function f4(p12) {
      if (p12 === undefined) {
        return 'Undefined'
      } else {
        return String(Object.prototype.toString.call(p12)).slice(8, -1)
      }
    }
    function f5(p13) {
      return f4(p13) === 'Array'
    }
    function f6(p14, p15) {
      var v8
      var v9
      var v10 = {}
      if (f4(p15) === 'RegExp') {
        v10 = null
      } else if (f5(p15)) {
        v8 = 0
        v9 = p15.length
        for (; v8 < v9; v8++) {
          v10[p15[v8]] = true
        }
      } else {
        v10[p15] = true
      }
      v8 = 0
      v9 = p14.length
      for (; v8 < v9; v8++) {
        if ((v10 && v10[p14[v8]] !== undefined) || (!v10 && p15.test(p14[v8]))) {
          p14.splice(v8, 1)
          v9--
          v8--
        }
      }
      return p14
    }
    function f7(p16, p17) {
      if (f5(p17)) {
        v11 = 0
        v12 = p17.length
        for (; v11 < v12; v11++) {
          if (!f7(p16, p17[v11])) {
            return false
          }
        }
        return true
      }
      var vF4 = f4(p17)
      for (var v11 = 0, v12 = p16.length; v11 < v12; v11++) {
        if (vF4 === 'RegExp') {
          if (typeof p16[v11] == 'string' && p16[v11].match(p17)) {
            return true
          }
        } else if (p16[v11] === p17) {
          return true
        }
      }
      return false
    }
    function f8(p18, p19) {
      if (!f5(p18) || !f5(p19)) {
        return false
      }
      if (p18.length !== p19.length) {
        return false
      }
      p18.sort()
      p19.sort()
      for (var v13 = 0, v14 = p18.length; v13 < v14; v13++) {
        if (p18[v13] !== p19[v13]) {
          return false
        }
      }
      return true
    }
    function f9(p20) {
      return p20.replace(/^\/+|\/+$/g, '')
    }
    function f10(p21) {
      return escape(p21)
    }
    function f11(p22) {
      return encodeURIComponent(p22)
        .replace(/[!'()*]/g, f10)
        .replace(/\*/g, '%2A')
    }
    f2._parts = function () {
      return {
        protocol: null,
        username: null,
        password: null,
        hostname: null,
        urn: null,
        port: null,
        path: null,
        query: null,
        fragment: null,
        preventInvalidHostname: f2.preventInvalidHostname,
        duplicateQueryParameters: f2.duplicateQueryParameters,
        escapeQuerySpace: f2.escapeQuerySpace
      }
    }
    f2.preventInvalidHostname = false
    f2.duplicateQueryParameters = false
    f2.escapeQuerySpace = true
    f2.protocol_expression = /^[a-z][a-z0-9.+-]*$/i
    f2.idn_expression = /[^a-z0-9\._-]/i
    f2.punycode_expression = /(xn--)/i
    f2.ip4_expression = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/
    f2.ip6_expression =
      /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/
    f2.find_uri_expression =
      /\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?Â«Â»ââââ]))/gi
    f2.findUri = {
      start: /\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,
      end: /[\s\r\n]|$/,
      trim: /[`!()\[\]{};:'".,<>?Â«Â»âââââ]+$/,
      parens: /(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g
    }
    f2.leading_whitespace_expression =
      /^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/
    f2.ascii_tab_whitespace = /[\u0009\u000A\u000D]+/g
    f2.defaultPorts = {
      http: '80',
      https: '443',
      ftp: '21',
      gopher: '70',
      ws: '80',
      wss: '443'
    }
    f2.hostProtocols = ['http', 'https']
    f2.invalid_hostname_characters = /[^a-zA-Z0-9\.\-:_]/
    f2.domAttributes = {
      a: 'href',
      blockquote: 'cite',
      link: 'href',
      base: 'href',
      script: 'src',
      form: 'action',
      img: 'src',
      area: 'href',
      iframe: 'src',
      embed: 'src',
      source: 'src',
      track: 'src',
      input: 'src',
      audio: 'src',
      video: 'src'
    }
    f2.getDomAttribute = function (p23) {
      if (p23 && p23.nodeName) {
        var v15 = p23.nodeName.toLowerCase()
        if (v15 !== 'input' || p23.type === 'image') {
          return f2.domAttributes[v15]
        }
      }
    }
    f2.encode = f11
    f2.decode = decodeURIComponent
    f2.iso8859 = function () {
      f2.encode = escape
      f2.decode = unescape
    }
    f2.unicode = function () {
      f2.encode = f11
      f2.decode = decodeURIComponent
    }
    f2.characters = {
      pathname: {
        encode: {
          expression: /%(24|26|2B|2C|3B|3D|3A|40)/gi,
          map: {
            '%24': '$',
            '%26': '&',
            '%2B': '+',
            '%2C': ',',
            '%3B': ';',
            '%3D': '=',
            '%3A': ':',
            '%40': '@'
          }
        },
        decode: {
          expression: /[\/\?#]/g,
          map: {
            '/': '%2F',
            '?': '%3F',
            '#': '%23'
          }
        }
      },
      reserved: {
        encode: {
          expression: /%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,
          map: {
            '%3A': ':',
            '%2F': '/',
            '%3F': '?',
            '%23': '#',
            '%5B': '[',
            '%5D': ']',
            '%40': '@',
            '%21': '!',
            '%24': '$',
            '%26': '&',
            '%27': "'",
            '%28': '(',
            '%29': ')',
            '%2A': '*',
            '%2B': '+',
            '%2C': ',',
            '%3B': ';',
            '%3D': '='
          }
        }
      },
      urnpath: {
        encode: {
          expression: /%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,
          map: {
            '%21': '!',
            '%24': '$',
            '%27': "'",
            '%28': '(',
            '%29': ')',
            '%2A': '*',
            '%2B': '+',
            '%2C': ',',
            '%3B': ';',
            '%3D': '=',
            '%40': '@'
          }
        },
        decode: {
          expression: /[\/\?#:]/g,
          map: {
            '/': '%2F',
            '?': '%3F',
            '#': '%23',
            ':': '%3A'
          }
        }
      }
    }
    f2.encodeQuery = function (p24, p25) {
      p24 = f2.encode(p24 + '')
      if ((p25 = p25 === undefined ? f2.escapeQuerySpace : p25)) {
        return p24.replace(/%20/g, '+')
      } else {
        return p24
      }
    }
    f2.decodeQuery = function (p26, p27) {
      p26 += ''
      if (p27 === undefined) {
        p27 = f2.escapeQuerySpace
      }
      try {
        return f2.decode(p27 ? p26.replace(/\+/g, '%20') : p26)
      } catch (_0x5dabc7) {
        return p26
      }
    }
    function f12(p28, p29) {
      return function (p30) {
        try {
          return f2[p29](p30 + '').replace(f2.characters[p28][p29].expression, function (p31) {
            return f2.characters[p28][p29].map[p31]
          })
        } catch (_0x10729f) {
          return p30
        }
      }
    }
    var v16
    var v17 = {
      encode: 'encode',
      decode: 'decode'
    }
    for (v16 in v17) {
      f2[v16 + 'PathSegment'] = f12('pathname', v17[v16])
      f2[v16 + 'UrnPathSegment'] = f12('urnpath', v17[v16])
    }
    function f13(p32, p33, p34) {
      return function (p35) {
        var v18 = p34
          ? function (p36) {
              return f2[p33](f2[p34](p36))
            }
          : f2[p33]
        var v19 = (p35 + '').split(p32)
        for (var v20 = 0, v21 = v19.length; v20 < v21; v20++) {
          v19[v20] = v18(v19[v20])
        }
        return v19.join(p32)
      }
    }
    function f14(p37) {
      return function (p38, p39) {
        if (p38 === undefined) {
          return this._parts[p37] || ''
        } else {
          this._parts[p37] = p38 || null
          this.build(!p39)
          return this
        }
      }
    }
    function f15(p40, p41) {
      return function (p42, p43) {
        if (p42 === undefined) {
          return this._parts[p40] || ''
        } else {
          if (p42 !== null && (p42 += '').charAt(0) === p41) {
            p42 = p42.substring(1)
          }
          this._parts[p40] = p42
          this.build(!p43)
          return this
        }
      }
    }
    f2.decodePath = f13('/', 'decodePathSegment')
    f2.decodeUrnPath = f13(':', 'decodeUrnPathSegment')
    f2.recodePath = f13('/', 'encodePathSegment', 'decode')
    f2.recodeUrnPath = f13(':', 'encodeUrnPathSegment', 'decode')
    f2.encodeReserved = f12('reserved', 'encode')
    f2.parse = function (p44, p45) {
      var v22
      p45 = p45 || {
        preventInvalidHostname: f2.preventInvalidHostname
      }
      if (
        (v22 = (p44 = (p44 = p44.replace(f2.leading_whitespace_expression, '')).replace(
          f2.ascii_tab_whitespace,
          ''
        )).indexOf('#')) > -1
      ) {
        p45.fragment = p44.substring(v22 + 1) || null
        p44 = p44.substring(0, v22)
      }
      if ((v22 = p44.indexOf('?')) > -1) {
        p45.query = p44.substring(v22 + 1) || null
        p44 = p44.substring(0, v22)
      }
      if (
        (p44 = (p44 = p44.replace(/^(https?|ftp|wss?)?:+[/\\]*/i, '$1://')).replace(
          /^[/\\]{2,}/i,
          '//'
        )).substring(0, 2) === '//'
      ) {
        p45.protocol = null
        p44 = p44.substring(2)
        p44 = f2.parseAuthority(p44, p45)
      } else if ((v22 = p44.indexOf(':')) > -1) {
        p45.protocol = p44.substring(0, v22) || null
        if (p45.protocol && !p45.protocol.match(f2.protocol_expression)) {
          p45.protocol = undefined
        } else if (p44.substring(v22 + 1, v22 + 3).replace(/\\/g, '/') === '//') {
          p44 = p44.substring(v22 + 3)
          p44 = f2.parseAuthority(p44, p45)
        } else {
          p44 = p44.substring(v22 + 1)
          p45.urn = true
        }
      }
      p45.path = p44
      return p45
    }
    f2.parseHost = function (p46, p47) {
      var v23
      var v24
      var v25 = (p46 = (p46 = p46 || '').replace(/\\/g, '/')).indexOf('/')
      if (v25 === -1) {
        v25 = p46.length
      }
      if (p46.charAt(0) === '[') {
        v24 = p46.indexOf(']')
        p47.hostname = p46.substring(1, v24) || null
        p47.port = p46.substring(v24 + 2, v25) || null
        if (p47.port === '/') {
          p47.port = null
        }
      } else {
        v24 = p46.indexOf(':')
        v23 = p46.indexOf('/')
        if ((v24 = p46.indexOf(':', v24 + 1)) !== -1 && (v23 === -1 || v24 < v23)) {
          p47.hostname = p46.substring(0, v25) || null
          p47.port = null
        } else {
          v24 = p46.substring(0, v25).split(':')
          p47.hostname = v24[0] || null
          p47.port = v24[1] || null
        }
      }
      if (p47.hostname && p46.substring(v25).charAt(0) !== '/') {
        v25++
        p46 = '/' + p46
      }
      if (p47.preventInvalidHostname) {
        f2.ensureValidHostname(p47.hostname, p47.protocol)
      }
      if (p47.port) {
        f2.ensureValidPort(p47.port)
      }
      return p46.substring(v25) || '/'
    }
    f2.parseAuthority = function (p48, p49) {
      p48 = f2.parseUserinfo(p48, p49)
      return f2.parseHost(p48, p49)
    }
    f2.parseUserinfo = function (p50, p51) {
      var vP50 = p50
      var v26 = (p50 = p50.indexOf('\\') !== -1 ? p50.replace(/\\/g, '/') : p50).indexOf('/')
      var v27 = p50.lastIndexOf('@', v26 > -1 ? v26 : p50.length - 1)
      if (v27 > -1 && (v26 === -1 || v27 < v26)) {
        v26 = p50.substring(0, v27).split(':')
        p51.username = v26[0] ? f2.decode(v26[0]) : null
        v26.shift()
        p51.password = v26[0] ? f2.decode(v26.join(':')) : null
        p50 = vP50.substring(v27 + 1)
      } else {
        p51.username = null
        p51.password = null
      }
      return p50
    }
    f2.parseQuery = function (p52, p53) {
      if (!p52) {
        return {}
      }
      if (!(p52 = p52.replace(/&+/g, '&').replace(/^\?*&*|&+$/g, ''))) {
        return {}
      }
      var v28
      var v29
      var v30 = {}
      var v31 = p52.split('&')
      for (var v32 = v31.length, v33 = 0; v33 < v32; v33++) {
        v29 = v31[v33].split('=')
        v28 = f2.decodeQuery(v29.shift(), p53)
        v29 = v29.length ? f2.decodeQuery(v29.join('='), p53) : null
        if (v28 !== '__proto__') {
          if (v7.call(v30, v28)) {
            if (typeof v30[v28] == 'string' || v30[v28] === null) {
              v30[v28] = [v30[v28]]
            }
            v30[v28].push(v29)
          } else {
            v30[v28] = v29
          }
        }
      }
      return v30
    }
    f2.build = function (p54) {
      var v34 = ''
      var v35 = false
      if (p54.protocol) {
        v34 += p54.protocol + ':'
      }
      if (!p54.urn && (!!v34 || !!p54.hostname)) {
        v34 += '//'
        v35 = true
      }
      v34 += f2.buildAuthority(p54) || ''
      if (typeof p54.path == 'string') {
        if (p54.path.charAt(0) !== '/' && v35) {
          v34 += '/'
        }
        v34 += p54.path
      }
      if (typeof p54.query == 'string' && p54.query) {
        v34 += '?' + p54.query
      }
      if (typeof p54.fragment == 'string' && p54.fragment) {
        v34 += '#' + p54.fragment
      }
      return v34
    }
    f2.buildHost = function (p55) {
      var v36 = ''
      if (p55.hostname) {
        if (f2.ip6_expression.test(p55.hostname)) {
          v36 += '[' + p55.hostname + ']'
        } else {
          v36 += p55.hostname
        }
        if (p55.port) {
          v36 += ':' + p55.port
        }
        return v36
      } else {
        return ''
      }
    }
    f2.buildAuthority = function (p56) {
      return f2.buildUserinfo(p56) + f2.buildHost(p56)
    }
    f2.buildUserinfo = function (p57) {
      var v37 = ''
      if (p57.username) {
        v37 += f2.encode(p57.username)
      }
      if (p57.password) {
        v37 += ':' + f2.encode(p57.password)
      }
      if (v37) {
        v37 += '@'
      }
      return v37
    }
    f2.buildQuery = function (p58, p59, p60) {
      var v38
      var v39
      var v40
      var v41
      var v42 = ''
      for (v39 in p58) {
        if (v39 !== '__proto__' && v7.call(p58, v39)) {
          if (f5(p58[v39])) {
            v38 = {}
            v40 = 0
            v41 = p58[v39].length
            for (; v40 < v41; v40++) {
              if (
                p58[v39][v40] !== undefined &&
                v38[p58[v39][v40] + ''] === undefined &&
                ((v42 += '&' + f2.buildQueryParameter(v39, p58[v39][v40], p60)), p59 !== true)
              ) {
                v38[p58[v39][v40] + ''] = true
              }
            }
          } else if (p58[v39] !== undefined) {
            v42 += '&' + f2.buildQueryParameter(v39, p58[v39], p60)
          }
        }
      }
      return v42.substring(1)
    }
    f2.buildQueryParameter = function (p61, p62, p63) {
      return f2.encodeQuery(p61, p63) + (p62 !== null ? '=' + f2.encodeQuery(p62, p63) : '')
    }
    f2.addQuery = function (p64, p65, p66) {
      if (typeof p65 == 'object') {
        for (var v43 in p65) {
          if (v7.call(p65, v43)) {
            f2.addQuery(p64, v43, p65[v43])
          }
        }
      } else {
        if (typeof p65 != 'string') {
          throw new TypeError('URI.addQuery() accepts an object, string as the name parameter')
        }
        if (p64[p65] === undefined) {
          p64[p65] = p66
        } else {
          if (typeof p64[p65] == 'string') {
            p64[p65] = [p64[p65]]
          }
          if (!f5(p66)) {
            p66 = [p66]
          }
          p64[p65] = (p64[p65] || []).concat(p66)
        }
      }
    }
    f2.setQuery = function (p67, p68, p69) {
      if (typeof p68 == 'object') {
        for (var v44 in p68) {
          if (v7.call(p68, v44)) {
            f2.setQuery(p67, v44, p68[v44])
          }
        }
      } else {
        if (typeof p68 != 'string') {
          throw new TypeError('URI.setQuery() accepts an object, string as the name parameter')
        }
        p67[p68] = p69 === undefined ? null : p69
      }
    }
    f2.removeQuery = function (p70, p71, p72) {
      var v45
      var v46
      var v47
      if (f5(p71)) {
        v45 = 0
        v46 = p71.length
        for (; v45 < v46; v45++) {
          p70[p71[v45]] = undefined
        }
      } else if (f4(p71) === 'RegExp') {
        for (v47 in p70) {
          if (p71.test(v47)) {
            p70[v47] = undefined
          }
        }
      } else if (typeof p71 == 'object') {
        for (v47 in p71) {
          if (v7.call(p71, v47)) {
            f2.removeQuery(p70, v47, p71[v47])
          }
        }
      } else {
        if (typeof p71 != 'string') {
          throw new TypeError(
            'URI.removeQuery() accepts an object, string, RegExp as the first parameter'
          )
        }
        if (p72 !== undefined) {
          if (f4(p72) === 'RegExp') {
            if (!f5(p70[p71]) && p72.test(p70[p71])) {
              p70[p71] = undefined
            } else {
              p70[p71] = f6(p70[p71], p72)
            }
          } else if (p70[p71] !== String(p72) || (f5(p72) && p72.length !== 1)) {
            if (f5(p70[p71])) {
              p70[p71] = f6(p70[p71], p72)
            }
          } else {
            p70[p71] = undefined
          }
        } else {
          p70[p71] = undefined
        }
      }
    }
    f2.hasQuery = function (p73, p74, p75, p76) {
      switch (f4(p74)) {
        case 'String':
          break
        case 'RegExp':
          for (var v48 in p73) {
            if (
              v7.call(p73, v48) &&
              p74.test(v48) &&
              (p75 === undefined || f2.hasQuery(p73, v48, p75))
            ) {
              return true
            }
          }
          return false
        case 'Object':
          for (var v49 in p74) {
            if (v7.call(p74, v49) && !f2.hasQuery(p73, v49, p74[v49])) {
              return false
            }
          }
          return true
        default:
          throw new TypeError(
            'URI.hasQuery() accepts a string, regular expression or object as the name parameter'
          )
      }
      switch (f4(p75)) {
        case 'Undefined':
          return p74 in p73
        case 'Boolean':
          return p75 === Boolean(f5(p73[p74]) ? p73[p74].length : p73[p74])
        case 'Function':
          return !!p75(p73[p74], p74, p73)
        case 'Array':
          if (f5(p73[p74])) {
            return (p76 ? f7 : f8)(p73[p74], p75)
          } else {
            return false
          }
        case 'RegExp':
          if (f5(p73[p74])) {
            return !!p76 && f7(p73[p74], p75)
          } else {
            return Boolean(p73[p74] && p73[p74].match(p75))
          }
        case 'Number':
          p75 = String(p75)
        case 'String':
          if (f5(p73[p74])) {
            return !!p76 && f7(p73[p74], p75)
          } else {
            return p73[p74] === p75
          }
        default:
          throw new TypeError(
            'URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter'
          )
      }
    }
    f2.joinPaths = function () {
      var v50
      var v51 = []
      var v52 = []
      var v53 = 0
      for (var v54 = 0; v54 < arguments.length; v54++) {
        var v55 = new f2(arguments[v54])
        for (var v56 = (v51.push(v55), v55.segment()), v57 = 0; v57 < v56.length; v57++) {
          if (typeof v56[v57] == 'string') {
            v52.push(v56[v57])
          }
          if (v56[v57]) {
            v53++
          }
        }
      }
      if (v52.length && v53) {
        v50 = new f2('').segment(v52)
        if (v51[0].path() === '' || v51[0].path().slice(0, 1) === '/') {
          v50.path('/' + v50.path())
        }
        return v50.normalize()
      } else {
        return new f2('')
      }
    }
    f2.commonPath = function (p77, p78) {
      for (var v58 = Math.min(p77.length, p78.length), v59 = 0; v59 < v58; v59++) {
        if (p77.charAt(v59) !== p78.charAt(v59)) {
          v59--
          break
        }
      }
      if (v59 < 1) {
        if (p77.charAt(0) === p78.charAt(0) && p77.charAt(0) === '/') {
          return '/'
        } else {
          return ''
        }
      } else {
        if (p77.charAt(v59) !== '/' || p78.charAt(v59) !== '/') {
          v59 = p77.substring(0, v59).lastIndexOf('/')
        }
        return p77.substring(0, v59 + 1)
      }
    }
    f2.withinString = function (p79, p80, p81) {
      var v60 = (p81 = p81 || {}).start || f2.findUri.start
      var v61 = p81.end || f2.findUri.end
      var v62 = p81.trim || f2.findUri.trim
      var v63 = p81.parens || f2.findUri.parens
      var v64 = /[a-z0-9-]=["']?$/i
      for (v60.lastIndex = 0; ; ) {
        var v65 = v60.exec(p79)
        if (!v65) {
          break
        }
        var v66 = v65.index
        if (p81.ignoreHtml) {
          var v68 = p79.slice(Math.max(v66 - 3, 0), v66)
          if (v68 && v64.test(v68)) {
            continue
          }
        }
        var v68 = v66 + p79.slice(v66).search(v61)
        var v69 = p79.slice(v66, v68)
        var v70 = -1
        while (true) {
          var v71 = v63.exec(v69)
          if (!v71) {
            break
          }
          v71 = v71.index + v71[0].length
          v70 = Math.max(v70, v71)
        }
        if (
          !(
            (v69 =
              v70 > -1 ? v69.slice(0, v70) + v69.slice(v70).replace(v62, '') : v69.replace(v62, ''))
              .length <= v65[0].length
          ) &&
          (!p81.ignore || !p81.ignore.test(v69))
        ) {
          if ((v65 = p80(v69, v66, (v68 = v66 + v69.length), p79)) === undefined) {
            v60.lastIndex = v68
          } else {
            v65 = String(v65)
            p79 = p79.slice(0, v66) + v65 + p79.slice(v68)
            v60.lastIndex = v66 + v65.length
          }
        }
      }
      v60.lastIndex = 0
      return p79
    }
    f2.ensureValidHostname = function (p82, p83) {
      var v72 = !!p82
      var v73 = false
      if ((v73 = p83 ? f7(f2.hostProtocols, p83) : v73) && !v72) {
        throw new TypeError('Hostname cannot be empty, if protocol is ' + p83)
      }
      if (p82 && p82.match(f2.invalid_hostname_characters)) {
        if (!p5) {
          throw new TypeError(
            'Hostname "' +
              p82 +
              '" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available'
          )
        }
        if (p5.toASCII(p82).match(f2.invalid_hostname_characters)) {
          throw new TypeError('Hostname "' + p82 + '" contains characters other than [A-Z0-9.-:_]')
        }
      }
    }
    f2.ensureValidPort = function (p84) {
      if (p84) {
        var vNumber = Number(p84)
        if (!/^[0-9]+$/.test(vNumber) || !(vNumber > 0) || !(vNumber < 65536)) {
          throw new TypeError('Port "' + p84 + '" is not a valid port')
        }
      }
    }
    f2.noConflict = function (p85) {
      if (p85) {
        p85 = {
          URI: this.noConflict()
        }
        if (p8.URITemplate && typeof p8.URITemplate.noConflict == 'function') {
          p85.URITemplate = p8.URITemplate.noConflict()
        }
        if (p8.IPv6 && typeof p8.IPv6.noConflict == 'function') {
          p85.IPv6 = p8.IPv6.noConflict()
        }
        if (p8.SecondLevelDomains && typeof p8.SecondLevelDomains.noConflict == 'function') {
          p85.SecondLevelDomains = p8.SecondLevelDomains.noConflict()
        }
        return p85
      } else {
        if (p8.URI === this) {
          p8.URI = v4
        }
        return this
      }
    }
    v6.build = function (p86) {
      if (p86 === true) {
        this._deferred_build = true
      } else if (p86 === undefined || !!this._deferred_build) {
        this._string = f2.build(this._parts)
        this._deferred_build = false
      }
      return this
    }
    v6.clone = function () {
      return new f2(this)
    }
    v6.valueOf = v6.toString = function () {
      return this.build(false)._string
    }
    v6.protocol = f14('protocol')
    v6.username = f14('username')
    v6.password = f14('password')
    v6.hostname = f14('hostname')
    v6.port = f14('port')
    v6.query = f15('query', '?')
    v6.fragment = f15('fragment', '#')
    v6.search = function (p87, p88) {
      p87 = this.query(p87, p88)
      if (typeof p87 == 'string' && p87.length) {
        return '?' + p87
      } else {
        return p87
      }
    }
    v6.hash = function (p89, p90) {
      p89 = this.fragment(p89, p90)
      if (typeof p89 == 'string' && p89.length) {
        return '#' + p89
      } else {
        return p89
      }
    }
    v6.pathname = function (p91, p92) {
      var v74
      if (p91 === undefined || p91 === true) {
        v74 = this._parts.path || (this._parts.hostname ? '/' : '')
        if (p91) {
          return (this._parts.urn ? f2.decodeUrnPath : f2.decodePath)(v74)
        } else {
          return v74
        }
      } else {
        if (this._parts.urn) {
          this._parts.path = p91 ? f2.recodeUrnPath(p91) : ''
        } else {
          this._parts.path = p91 ? f2.recodePath(p91) : '/'
        }
        this.build(!p92)
        return this
      }
    }
    v6.path = v6.pathname
    v6.href = function (p93, p94) {
      if (p93 === undefined) {
        return this.toString()
      }
      this._string = ''
      this._parts = f2._parts()
      var v75 = p93 instanceof f2
      var v76 = typeof p93 == 'object' && (p93.hostname || p93.path || p93.pathname)
      if (p93.nodeName) {
        p93 = p93[f2.getDomAttribute(p93)] || ''
        v76 = false
      }
      if (
        typeof (p93 = !v75 && v76 && p93.pathname !== undefined ? p93.toString() : p93) ==
          'string' ||
        p93 instanceof String
      ) {
        this._parts = f2.parse(String(p93), this._parts)
      } else {
        if (!v75 && !v76) {
          throw new TypeError('invalid input')
        }
        var v77 = v75 ? p93._parts : p93
        for (var v78 in v77) {
          if (v78 !== 'query' && v7.call(this._parts, v78)) {
            this._parts[v78] = v77[v78]
          }
        }
        if (v77.query) {
          this.query(v77.query, false)
        }
      }
      this.build(!p94)
      return this
    }
    v6.is = function (p95) {
      var v79 = false
      var v80 = false
      var v81 = false
      var v82 = false
      var v83 = false
      var v84 = false
      var v85 = false
      var v86 = !this._parts.urn
      if (this._parts.hostname) {
        v86 = false
        v80 = f2.ip4_expression.test(this._parts.hostname)
        v81 = f2.ip6_expression.test(this._parts.hostname)
        v83 = (v82 = !(v79 = v80 || v81)) && p7 && p7.has(this._parts.hostname)
        v84 = v82 && f2.idn_expression.test(this._parts.hostname)
        v85 = v82 && f2.punycode_expression.test(this._parts.hostname)
      }
      switch (p95.toLowerCase()) {
        case 'relative':
          return v86
        case 'absolute':
          return !v86
        case 'domain':
        case 'name':
          return v82
        case 'sld':
          return v83
        case 'ip':
          return v79
        case 'ip4':
        case 'ipv4':
        case 'inet4':
          return v80
        case 'ip6':
        case 'ipv6':
        case 'inet6':
          return v81
        case 'idn':
          return v84
        case 'url':
          return !this._parts.urn
        case 'urn':
          return !!this._parts.urn
        case 'punycode':
          return v85
      }
      return null
    }
    var v87 = v6.protocol
    var v88 = v6.port
    var v89 = v6.hostname
    v6.protocol = function (p96, p97) {
      if (p96 && !(p96 = p96.replace(/:(\/\/)?$/, '')).match(f2.protocol_expression)) {
        throw new TypeError(
          'Protocol "' +
            p96 +
            '" contains characters other than [A-Z0-9.+-] or doesn\'t start with [A-Z]'
        )
      }
      return v87.call(this, p96, p97)
    }
    v6.scheme = v6.protocol
    v6.port = function (p98, p99) {
      if (this._parts.urn) {
        if (p98 === undefined) {
          return ''
        } else {
          return this
        }
      } else {
        if (p98 !== undefined && (p98 = p98 === 0 ? null : p98)) {
          if ((p98 += '').charAt(0) === ':') {
            p98 = p98.substring(1)
          }
          f2.ensureValidPort(p98)
        }
        return v88.call(this, p98, p99)
      }
    }
    v6.hostname = function (p100, p101) {
      if (this._parts.urn) {
        if (p100 === undefined) {
          return ''
        } else {
          return this
        }
      }
      if (p100 !== undefined) {
        var v90 = {
          preventInvalidHostname: this._parts.preventInvalidHostname
        }
        if (f2.parseHost(p100, v90) !== '/') {
          throw new TypeError('Hostname "' + p100 + '" contains characters other than [A-Z0-9.-]')
        }
        p100 = v90.hostname
        if (this._parts.preventInvalidHostname) {
          f2.ensureValidHostname(p100, this._parts.protocol)
        }
      }
      return v89.call(this, p100, p101)
    }
    v6.origin = function (p102, p103) {
      var v91
      if (this._parts.urn) {
        if (p102 === undefined) {
          return ''
        } else {
          return this
        }
      } else if (p102 === undefined) {
        v91 = this.protocol()
        if (this.authority()) {
          return (v91 ? v91 + '://' : '') + this.authority()
        } else {
          return ''
        }
      } else {
        v91 = f2(p102)
        this.protocol(v91.protocol()).authority(v91.authority()).build(!p103)
        return this
      }
    }
    v6.host = function (p104, p105) {
      if (this._parts.urn) {
        if (p104 === undefined) {
          return ''
        } else {
          return this
        }
      }
      if (p104 === undefined) {
        if (this._parts.hostname) {
          return f2.buildHost(this._parts)
        } else {
          return ''
        }
      }
      if (f2.parseHost(p104, this._parts) !== '/') {
        throw new TypeError('Hostname "' + p104 + '" contains characters other than [A-Z0-9.-]')
      }
      this.build(!p105)
      return this
    }
    v6.authority = function (p106, p107) {
      if (this._parts.urn) {
        if (p106 === undefined) {
          return ''
        } else {
          return this
        }
      }
      if (p106 === undefined) {
        if (this._parts.hostname) {
          return f2.buildAuthority(this._parts)
        } else {
          return ''
        }
      }
      if (f2.parseAuthority(p106, this._parts) !== '/') {
        throw new TypeError('Hostname "' + p106 + '" contains characters other than [A-Z0-9.-]')
      }
      this.build(!p107)
      return this
    }
    v6.userinfo = function (p108, p109) {
      var v92
      if (this._parts.urn) {
        if (p108 === undefined) {
          return ''
        } else {
          return this
        }
      } else if (p108 === undefined) {
        return (v92 = f2.buildUserinfo(this._parts)) && v92.substring(0, v92.length - 1)
      } else {
        if (p108[p108.length - 1] !== '@') {
          p108 += '@'
        }
        f2.parseUserinfo(p108, this._parts)
        this.build(!p109)
        return this
      }
    }
    v6.resource = function (p110, p111) {
      if (p110 === undefined) {
        return this.path() + this.search() + this.hash()
      } else {
        p110 = f2.parse(p110)
        this._parts.path = p110.path
        this._parts.query = p110.query
        this._parts.fragment = p110.fragment
        this.build(!p111)
        return this
      }
    }
    v6.subdomain = function (p112, p113) {
      if (this._parts.urn) {
        if (p112 === undefined) {
          return ''
        } else {
          return this
        }
      }
      if (p112 === undefined) {
        return (
          (this._parts.hostname &&
            !this.is('IP') &&
            ((v95 = this._parts.hostname.length - this.domain().length - 1),
            this._parts.hostname.substring(0, v95))) ||
          ''
        )
      }
      var v95 = this._parts.hostname.length - this.domain().length
      var v95 = this._parts.hostname.substring(0, v95)
      var v95 = new RegExp('^' + f3(v95))
      if (p112 && p112.charAt(p112.length - 1) !== '.') {
        p112 += '.'
      }
      if (p112.indexOf(':') !== -1) {
        throw new TypeError('Domains cannot contain colons')
      }
      if (p112) {
        f2.ensureValidHostname(p112, this._parts.protocol)
      }
      this._parts.hostname = this._parts.hostname.replace(v95, p112)
      this.build(!p113)
      return this
    }
    v6.domain = function (p114, p115) {
      if (this._parts.urn) {
        if (p114 === undefined) {
          return ''
        } else {
          return this
        }
      }
      var v96
      if (typeof p114 == 'boolean') {
        p115 = p114
        p114 = undefined
      }
      if (p114 === undefined) {
        if (!this._parts.hostname || this.is('IP')) {
          return ''
        } else if ((v96 = this._parts.hostname.match(/\./g)) && v96.length < 2) {
          return this._parts.hostname
        } else {
          v96 = this._parts.hostname.length - this.tld(p115).length - 1
          v96 = this._parts.hostname.lastIndexOf('.', v96 - 1) + 1
          return this._parts.hostname.substring(v96) || ''
        }
      }
      if (!p114) {
        throw new TypeError('cannot set domain empty')
      }
      if (p114.indexOf(':') !== -1) {
        throw new TypeError('Domains cannot contain colons')
      }
      f2.ensureValidHostname(p114, this._parts.protocol)
      if (!this._parts.hostname || this.is('IP')) {
        this._parts.hostname = p114
      } else {
        v96 = new RegExp(f3(this.domain()) + '$')
        this._parts.hostname = this._parts.hostname.replace(v96, p114)
      }
      this.build(!p115)
      return this
    }
    v6.tld = function (p116, p117) {
      if (this._parts.urn) {
        if (p116 === undefined) {
          return ''
        } else {
          return this
        }
      }
      var v97
      if (typeof p116 == 'boolean') {
        p117 = p116
        p116 = undefined
      }
      if (p116 === undefined) {
        if (!this._parts.hostname || this.is('IP')) {
          return ''
        } else {
          v97 = this._parts.hostname.lastIndexOf('.')
          v97 = this._parts.hostname.substring(v97 + 1)
          return (
            (p117 !== true && p7 && p7.list[v97.toLowerCase()] && p7.get(this._parts.hostname)) ||
            v97
          )
        }
      }
      if (!p116) {
        throw new TypeError('cannot set TLD empty')
      }
      if (p116.match(/[^a-zA-Z0-9-]/)) {
        if (!p7 || !p7.is(p116)) {
          throw new TypeError('TLD "' + p116 + '" contains characters other than [A-Z0-9]')
        }
      } else if (!this._parts.hostname || this.is('IP')) {
        throw new ReferenceError('cannot set TLD on non-domain host')
      }
      v97 = new RegExp(f3(this.tld()) + '$')
      this._parts.hostname = this._parts.hostname.replace(v97, p116)
      this.build(!p117)
      return this
    }
    v6.directory = function (p118, p119) {
      var v98
      if (this._parts.urn) {
        if (p118 === undefined) {
          return ''
        } else {
          return this
        }
      } else if (p118 === undefined || p118 === true) {
        if (this._parts.path || this._parts.hostname) {
          if (this._parts.path === '/') {
            return '/'
          } else {
            v98 = this._parts.path.length - this.filename().length - 1
            v98 = this._parts.path.substring(0, v98) || (this._parts.hostname ? '/' : '')
            if (p118) {
              return f2.decodePath(v98)
            } else {
              return v98
            }
          }
        } else {
          return ''
        }
      } else {
        v98 = this._parts.path.length - this.filename().length
        v98 = this._parts.path.substring(0, v98)
        v98 = new RegExp('^' + f3(v98))
        if (!this.is('relative')) {
          if ((p118 = p118 || '/').charAt(0) !== '/') {
            p118 = '/' + p118
          }
        }
        if (p118 && p118.charAt(p118.length - 1) !== '/') {
          p118 += '/'
        }
        p118 = f2.recodePath(p118)
        this._parts.path = this._parts.path.replace(v98, p118)
        this.build(!p119)
        return this
      }
    }
    v6.filename = function (p120, p121) {
      var v99
      var v100
      if (this._parts.urn) {
        if (p120 === undefined) {
          return ''
        } else {
          return this
        }
      } else if (typeof p120 != 'string') {
        if (this._parts.path && this._parts.path !== '/') {
          v99 = this._parts.path.lastIndexOf('/')
          v99 = this._parts.path.substring(v99 + 1)
          if (p120) {
            return f2.decodePathSegment(v99)
          } else {
            return v99
          }
        } else {
          return ''
        }
      } else {
        v99 = false
        if ((p120 = p120.charAt(0) === '/' ? p120.substring(1) : p120).match(/\.?\//)) {
          v99 = true
        }
        v100 = new RegExp(f3(this.filename()) + '$')
        p120 = f2.recodePath(p120)
        this._parts.path = this._parts.path.replace(v100, p120)
        if (v99) {
          this.normalizePath(p121)
        } else {
          this.build(!p121)
        }
        return this
      }
    }
    v6.suffix = function (p122, p123) {
      if (this._parts.urn) {
        if (p122 === undefined) {
          return ''
        } else {
          return this
        }
      }
      var v101
      if (p122 === undefined || p122 === true) {
        if (
          !this._parts.path ||
          this._parts.path === '/' ||
          (v101 = (v103 = this.filename()).lastIndexOf('.')) === -1
        ) {
          return ''
        } else {
          v103 = v103.substring(v101 + 1)
          v101 = /^[a-z0-9%]+$/i.test(v103) ? v103 : ''
          if (p122) {
            return f2.decodePathSegment(v101)
          } else {
            return v101
          }
        }
      }
      if (p122.charAt(0) === '.') {
        p122 = p122.substring(1)
      }
      var v102
      var v103 = this.suffix()
      if (v103) {
        v102 = p122 ? new RegExp(f3(v103) + '$') : new RegExp(f3('.' + v103) + '$')
      } else {
        if (!p122) {
          return this
        }
        this._parts.path += '.' + f2.recodePath(p122)
      }
      if (v102) {
        p122 = f2.recodePath(p122)
        this._parts.path = this._parts.path.replace(v102, p122)
      }
      this.build(!p123)
      return this
    }
    v6.segment = function (p124, p125, p126) {
      var v104 = this._parts.urn ? ':' : '/'
      var v105 = this.path()
      var v106 = v105.substring(0, 1) === '/'
      var v108 = v105.split(v104)
      if (p124 !== undefined && typeof p124 != 'number') {
        p126 = p125
        p125 = p124
        p124 = undefined
      }
      if (p124 !== undefined && typeof p124 != 'number') {
        throw new Error('Bad segment "' + p124 + '", must be 0-based integer')
      }
      if (v106) {
        v108.shift()
      }
      if (p124 < 0) {
        p124 = Math.max(v108.length + p124, 0)
      }
      if (p125 === undefined) {
        if (p124 === undefined) {
          return v108
        } else {
          return v108[p124]
        }
      }
      if (p124 === null || v108[p124] === undefined) {
        if (f5(p125)) {
          var v108 = []
          for (var v109 = 0, v110 = p125.length; v109 < v110; v109++) {
            if (p125[v109].length || (v108.length && v108[v108.length - 1].length)) {
              if (v108.length && !v108[v108.length - 1].length) {
                v108.pop()
              }
              v108.push(f9(p125[v109]))
            }
          }
        } else if (!!p125 || typeof p125 == 'string') {
          p125 = f9(p125)
          if (v108[v108.length - 1] === '') {
            v108[v108.length - 1] = p125
          } else {
            v108.push(p125)
          }
        }
      } else if (p125) {
        v108[p124] = f9(p125)
      } else {
        v108.splice(p124, 1)
      }
      if (v106) {
        v108.unshift('')
      }
      return this.path(v108.join(v104), p126)
    }
    v6.segmentCoded = function (p127, p128, p129) {
      var v111
      var v112
      var v113
      if (typeof p127 != 'number') {
        p129 = p128
        p128 = p127
        p127 = undefined
      }
      if (p128 === undefined) {
        if (f5((v111 = this.segment(p127, p128, p129)))) {
          v112 = 0
          v113 = v111.length
          for (; v112 < v113; v112++) {
            v111[v112] = f2.decode(v111[v112])
          }
        } else {
          v111 = v111 !== undefined ? f2.decode(v111) : undefined
        }
        return v111
      }
      if (f5(p128)) {
        v112 = 0
        v113 = p128.length
        for (; v112 < v113; v112++) {
          p128[v112] = f2.encode(p128[v112])
        }
      } else {
        p128 = typeof p128 == 'string' || p128 instanceof String ? f2.encode(p128) : p128
      }
      return this.segment(p127, p128, p129)
    }
    var v114 = v6.query
    v6.query = function (p130, p131) {
      var v115
      var v116
      if (p130 === true) {
        return f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace)
      } else if (typeof p130 == 'function') {
        v115 = f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace)
        v116 = p130.call(this, v115)
        this._parts.query = f2.buildQuery(
          v116 || v115,
          this._parts.duplicateQueryParameters,
          this._parts.escapeQuerySpace
        )
        this.build(!p131)
        return this
      } else if (p130 !== undefined && typeof p130 != 'string') {
        this._parts.query = f2.buildQuery(
          p130,
          this._parts.duplicateQueryParameters,
          this._parts.escapeQuerySpace
        )
        this.build(!p131)
        return this
      } else {
        return v114.call(this, p130, p131)
      }
    }
    v6.setQuery = function (p132, p133, p134) {
      var v117 = f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace)
      if (typeof p132 == 'string' || p132 instanceof String) {
        v117[p132] = p133 !== undefined ? p133 : null
      } else {
        if (typeof p132 != 'object') {
          throw new TypeError('URI.addQuery() accepts an object, string as the name parameter')
        }
        for (var v118 in p132) {
          if (v7.call(p132, v118)) {
            v117[v118] = p132[v118]
          }
        }
      }
      this._parts.query = f2.buildQuery(
        v117,
        this._parts.duplicateQueryParameters,
        this._parts.escapeQuerySpace
      )
      this.build(!(p134 = typeof p132 != 'string' ? p133 : p134))
      return this
    }
    v6.addQuery = function (p135, p136, p137) {
      var v119 = f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace)
      f2.addQuery(v119, p135, p136 === undefined ? null : p136)
      this._parts.query = f2.buildQuery(
        v119,
        this._parts.duplicateQueryParameters,
        this._parts.escapeQuerySpace
      )
      this.build(!(p137 = typeof p135 != 'string' ? p136 : p137))
      return this
    }
    v6.removeQuery = function (p138, p139, p140) {
      var v120 = f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace)
      f2.removeQuery(v120, p138, p139)
      this._parts.query = f2.buildQuery(
        v120,
        this._parts.duplicateQueryParameters,
        this._parts.escapeQuerySpace
      )
      this.build(!(p140 = typeof p138 != 'string' ? p139 : p140))
      return this
    }
    v6.hasQuery = function (p141, p142, p143) {
      var v121 = f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace)
      return f2.hasQuery(v121, p141, p142, p143)
    }
    v6.setSearch = v6.setQuery
    v6.addSearch = v6.addQuery
    v6.removeSearch = v6.removeQuery
    v6.hasSearch = v6.hasQuery
    v6.normalize = function () {
      return (
        this._parts.urn
          ? this.normalizeProtocol(false)
          : this.normalizeProtocol(false).normalizeHostname(false).normalizePort(false)
      )
        .normalizePath(false)
        .normalizeQuery(false)
        .normalizeFragment(false)
        .build()
    }
    v6.normalizeProtocol = function (p144) {
      if (typeof this._parts.protocol == 'string') {
        this._parts.protocol = this._parts.protocol.toLowerCase()
        this.build(!p144)
      }
      return this
    }
    v6.normalizeHostname = function (p145) {
      if (this._parts.hostname) {
        if (this.is('IDN') && p5) {
          this._parts.hostname = p5.toASCII(this._parts.hostname)
        } else if (this.is('IPv6') && p6) {
          this._parts.hostname = p6.best(this._parts.hostname)
        }
        this._parts.hostname = this._parts.hostname.toLowerCase()
        this.build(!p145)
      }
      return this
    }
    v6.normalizePort = function (p146) {
      if (
        typeof this._parts.protocol == 'string' &&
        this._parts.port === f2.defaultPorts[this._parts.protocol]
      ) {
        this._parts.port = null
        this.build(!p146)
      }
      return this
    }
    v6.normalizePath = function (p147) {
      if ((v123 = this._parts.path)) {
        if (this._parts.urn) {
          this._parts.path = f2.recodeUrnPath(this._parts.path)
          this.build(!p147)
        } else if (this._parts.path !== '/') {
          var v122
          var v123
          var v124
          var v125
          var v126 = ''
          if ((v123 = f2.recodePath(v123)).charAt(0) !== '/') {
            v122 = true
            v123 = '/' + v123
          }
          if (v123.slice(-3) === '/..' || v123.slice(-2) === '/.') {
            v123 += '/'
          }
          v123 = v123.replace(/(\/(\.\/)+)|(\/\.$)/g, '/').replace(/\/{2,}/g, '/')
          if (v122) {
            v126 = (v126 = v123.substring(1).match(/^(\.\.\/)+/) || '') && v126[0]
          }
          while (true) {
            if ((v124 = v123.search(/\/\.\.(\/|$)/)) === -1) {
              break
            }
            if (v124 === 0) {
              v123 = v123.substring(3)
            } else {
              if ((v125 = v123.substring(0, v124).lastIndexOf('/')) === -1) {
                v125 = v124
              }
              v123 = v123.substring(0, v125) + v123.substring(v124 + 3)
            }
          }
          if (v122 && this.is('relative')) {
            v123 = v126 + v123.substring(1)
          }
          this._parts.path = v123
          this.build(!p147)
        }
      }
      return this
    }
    v6.normalizePathname = v6.normalizePath
    v6.normalizeQuery = function (p148) {
      if (typeof this._parts.query == 'string') {
        if (this._parts.query.length) {
          this.query(f2.parseQuery(this._parts.query, this._parts.escapeQuerySpace))
        } else {
          this._parts.query = null
        }
        this.build(!p148)
      }
      return this
    }
    v6.normalizeFragment = function (p149) {
      if (!this._parts.fragment) {
        this._parts.fragment = null
        this.build(!p149)
      }
      return this
    }
    v6.normalizeSearch = v6.normalizeQuery
    v6.normalizeHash = v6.normalizeFragment
    v6.iso8859 = function () {
      var v127 = f2.encode
      var v128 = f2.decode
      f2.encode = escape
      f2.decode = decodeURIComponent
      try {
        this.normalize()
      } finally {
        f2.encode = v127
        f2.decode = v128
      }
      return this
    }
    v6.unicode = function () {
      var v129 = f2.encode
      var v130 = f2.decode
      f2.encode = f11
      f2.decode = unescape
      try {
        this.normalize()
      } finally {
        f2.encode = v129
        f2.decode = v130
      }
      return this
    }
    v6.readable = function () {
      var v131 = this.clone()
      v131.username('').password('').normalize()
      var v132 = ''
      if (v131._parts.protocol) {
        v132 += v131._parts.protocol + '://'
      }
      if (v131._parts.hostname) {
        if (v131.is('punycode') && p5) {
          v132 += p5.toUnicode(v131._parts.hostname)
          if (v131._parts.port) {
            v132 += ':' + v131._parts.port
          }
        } else {
          v132 += v131.host()
        }
      }
      if (v131._parts.hostname && v131._parts.path && v131._parts.path.charAt(0) !== '/') {
        v132 += '/'
      }
      v132 += v131.path(true)
      if (v131._parts.query) {
        var v133 = ''
        for (
          var v134 = 0, v135 = v131._parts.query.split('&'), v136 = v135.length;
          v134 < v136;
          v134++
        ) {
          var v137 = (v135[v134] || '').split('=')
          v133 += '&' + f2.decodeQuery(v137[0], this._parts.escapeQuerySpace).replace(/&/g, '%26')
          if (v137[1] !== undefined) {
            v133 += '=' + f2.decodeQuery(v137[1], this._parts.escapeQuerySpace).replace(/&/g, '%26')
          }
        }
        v132 += '?' + v133.substring(1)
      }
      return (v132 += f2.decodeQuery(v131.hash(), true))
    }
    v6.absoluteTo = function (p150) {
      var v138
      var v139
      var v140
      var v141 = this.clone()
      var v142 = ['protocol', 'username', 'password', 'hostname', 'port']
      if (this._parts.urn) {
        throw new Error('URNs do not have any generally defined hierarchical components')
      }
      if (!(p150 instanceof f2)) {
        p150 = new f2(p150)
      }
      if (
        !v141._parts.protocol &&
        ((v141._parts.protocol = p150._parts.protocol), !this._parts.hostname)
      ) {
        for (v139 = 0; (v140 = v142[v139]); v139++) {
          v141._parts[v140] = p150._parts[v140]
        }
        if (v141._parts.path) {
          if (v141._parts.path.substring(-2) === '..') {
            v141._parts.path += '/'
          }
          if (v141.path().charAt(0) !== '/') {
            v138 = p150.directory() || (p150.path().indexOf('/') === 0 ? '/' : '')
            v141._parts.path = (v138 ? v138 + '/' : '') + v141._parts.path
            v141.normalizePath()
          }
        } else {
          v141._parts.path = p150._parts.path
          v141._parts.query ||= p150._parts.query
        }
        v141.build()
      }
      return v141
    }
    v6.relativeTo = function (p151) {
      var v143
      var v144
      var v145
      var v146 = this.clone().normalize()
      if (v146._parts.urn) {
        throw new Error('URNs do not have any generally defined hierarchical components')
      }
      p151 = new f2(p151).normalize()
      v143 = v146._parts
      v144 = p151._parts
      v145 = v146.path()
      p151 = p151.path()
      if (v145.charAt(0) !== '/') {
        throw new Error('URI is already relative')
      }
      if (p151.charAt(0) !== '/') {
        throw new Error('Cannot calculate a URI relative to another relative URI')
      }
      if (v143.protocol === v144.protocol) {
        v143.protocol = null
      }
      if (
        v143.username === v144.username &&
        v143.password === v144.password &&
        v143.protocol === null &&
        v143.username === null &&
        v143.password === null &&
        v143.hostname === v144.hostname &&
        v143.port === v144.port
      ) {
        v143.hostname = null
        v143.port = null
        if (v145 === p151) {
          v143.path = ''
        } else if ((v145 = f2.commonPath(v145, p151))) {
          p151 = v144.path
            .substring(v145.length)
            .replace(/[^\/]*$/, '')
            .replace(/.*?\//g, '../')
          v143.path = p151 + v143.path.substring(v145.length) || './'
        }
      }
      return v146.build()
    }
    v6.equals = function (v153) {
      var v147
      var v148
      var v149
      var v150
      var v151
      var v152 = this.clone()
      var v153 = new f2(v153)
      var v154 = {}
      v152.normalize()
      v153.normalize()
      if (v152.toString() !== v153.toString()) {
        v149 = v152.query()
        v150 = v153.query()
        v152.query('')
        v153.query('')
        if (v152.toString() !== v153.toString()) {
          return false
        }
        if (v149.length !== v150.length) {
          return false
        }
        v147 = f2.parseQuery(v149, this._parts.escapeQuerySpace)
        v148 = f2.parseQuery(v150, this._parts.escapeQuerySpace)
        for (v151 in v147) {
          if (v7.call(v147, v151)) {
            if (f5(v147[v151])) {
              if (!f8(v147[v151], v148[v151])) {
                return false
              }
            } else if (v147[v151] !== v148[v151]) {
              return false
            }
            v154[v151] = true
          }
        }
        for (v151 in v148) {
          if (v7.call(v148, v151) && !v154[v151]) {
            return false
          }
        }
      }
      return true
    }
    v6.preventInvalidHostname = function (p153) {
      this._parts.preventInvalidHostname = !!p153
      return this
    }
    v6.duplicateQueryParameters = function (p154) {
      this._parts.duplicateQueryParameters = !!p154
      return this
    }
    v6.escapeQuerySpace = function (p155) {
      this._parts.escapeQuerySpace = !!p155
      return this
    }
    return f2
  })
  ;((p156) => {
    function f16(p157) {
      function f17() {
        setTimeout(function () {
          if (vThis._options.checkOnLoad === true) {
            if (vThis._options.debug === true) {
              vThis._log('onload->eventCallback', 'A check loading is launched')
            }
            if (vThis._var.bait === null) {
              vThis._creatBait()
            }
            setTimeout(function () {
              vThis.check()
            }, 1)
          }
        }, 1)
      }
      this._options = {
        checkOnLoad: false,
        resetOnEnd: false,
        loopCheckTime: 50,
        loopMaxNumber: 5,
        baitClass:
          'pub_300x250 pub_300x250m pub_728x90 text-ad textAd text_ad text_ads text-ads text-ad-links',
        baitStyle:
          'width: 1px !important; height: 1px !important; position: absolute !important; left: -10000px !important; top: -1000px !important;',
        debug: false
      }
      this._var = {
        version: '3.2.1',
        bait: null,
        checking: false,
        loop: null,
        loopNumber: 0,
        event: {
          detected: [],
          notDetected: []
        }
      }
      if (p157 !== undefined) {
        this.setOption(p157)
      }
      var vThis = this
      if (p156.addEventListener !== undefined) {
        p156.addEventListener('load', f17, false)
      } else {
        p156.attachEvent('onload', f17)
      }
    }
    f16.prototype._options = null
    f16.prototype._var = null
    f16.prototype._bait = null
    f16.prototype._log = function (p158, p159) {
      console.log('[FuckAdBlock][' + p158 + '] ' + p159)
    }
    f16.prototype.setOption = function (p160, p161) {
      var v155
      var v156
      if (p161 !== undefined) {
        v155 = p160
        ;(p160 = {})[v155] = p161
      }
      for (v156 in p160) {
        this._options[v156] = p160[v156]
        if (this._options.debug === true) {
          this._log(
            'setOption',
            'The option "' + v156 + '" he was assigned to "' + p160[v156] + '"'
          )
        }
      }
      return this
    }
    f16.prototype._creatBait = function () {
      var v157 = document.createElement('div')
      v157.setAttribute('class', this._options.baitClass)
      v157.setAttribute('style', this._options.baitStyle)
      this._var.bait = p156.document.body.appendChild(v157)
      this._var.bait.offsetParent
      this._var.bait.offsetHeight
      this._var.bait.offsetLeft
      this._var.bait.offsetTop
      this._var.bait.offsetWidth
      this._var.bait.clientHeight
      this._var.bait.clientWidth
      if (this._options.debug === true) {
        this._log('_creatBait', 'Bait has been created')
      }
    }
    f16.prototype._destroyBait = function () {
      p156.document.body.removeChild(this._var.bait)
      if (!(this._var.bait = null) === this._options.debug) {
        this._log('_destroyBait', 'Bait has been removed')
      }
    }
    f16.prototype.check = function (p162 = true) {
      if (this._options.debug === true) {
        this._log(
          'check',
          'An audit was requested ' + (p162 === true ? 'with a' : 'without') + ' loop'
        )
      }
      if (this._var.checking === true) {
        if (this._options.debug === true) {
          this._log('check', 'A check was canceled because there is already an ongoing')
        }
        return false
      }
      this._var.checking = true
      if (this._var.bait === null) {
        this._creatBait()
      }
      var vThis2 = this
      if (!(this._var.loopNumber = 0) === p162) {
        this._var.loop = setInterval(function () {
          vThis2._checkBait(p162)
        }, this._options.loopCheckTime)
      }
      setTimeout(function () {
        vThis2._checkBait(p162)
      }, 1)
      if (this._options.debug === true) {
        this._log('check', 'A check is in progress ...')
      }
      return true
    }
    f16.prototype._checkBait = function (p163) {
      var v158
      var v159 = false
      if (this._var.bait === null) {
        this._creatBait()
      }
      if (
        p156.document.body.getAttribute('abp') !== null ||
        this._var.bait.offsetParent === null ||
        this._var.bait.offsetHeight == 0 ||
        this._var.bait.offsetLeft == 0 ||
        this._var.bait.offsetTop == 0 ||
        this._var.bait.offsetWidth == 0 ||
        this._var.bait.clientHeight == 0 ||
        this._var.bait.clientWidth == 0
      ) {
        v159 = true
      }
      if (
        p156.getComputedStyle !== undefined &&
        !!(v158 = p156.getComputedStyle(this._var.bait, null)) &&
        (v158.getPropertyValue('display') == 'none' ||
          v158.getPropertyValue('visibility') == 'hidden')
      ) {
        v159 = true
      }
      if (this._options.debug === true) {
        this._log(
          '_checkBait',
          'A check (' +
            (this._var.loopNumber + 1) +
            '/' +
            this._options.loopMaxNumber +
            ' ~' +
            (1 + this._var.loopNumber * this._options.loopCheckTime) +
            'ms) was conducted and detection is ' +
            (v159 === true ? 'positive' : 'negative')
        )
      }
      if (
        p163 === true &&
        (this._var.loopNumber++, this._var.loopNumber >= this._options.loopMaxNumber)
      ) {
        this._stopLoop()
      }
      if (v159 === true) {
        this._stopLoop()
        this._destroyBait()
        this.emitEvent(true)
        if (p163 === true) {
          this._var.checking = false
        }
      } else if (this._var.loop === null || p163 === false) {
        this._destroyBait()
        this.emitEvent(false)
        if (p163 === true) {
          this._var.checking = false
        }
      }
    }
    f16.prototype._stopLoop = function (p164) {
      clearInterval(this._var.loop)
      this._var.loop = null
      if (!(this._var.loopNumber = 0) === this._options.debug) {
        this._log('_stopLoop', 'A loop has been stopped')
      }
    }
    f16.prototype.emitEvent = function (p165) {
      if (this._options.debug === true) {
        this._log(
          'emitEvent',
          'An event with a ' + (p165 === true ? 'positive' : 'negative') + ' detection was called'
        )
      }
      var v160
      var v161 = this._var.event[p165 === true ? 'detected' : 'notDetected']
      for (v160 in v161) {
        if (this._options.debug === true) {
          this._log('emitEvent', 'Call function ' + (parseInt(v160) + 1) + '/' + v161.length)
        }
        if (v161.hasOwnProperty(v160)) {
          v161[v160]()
        }
      }
      if (this._options.resetOnEnd === true) {
        this.clearEvent()
      }
      return this
    }
    f16.prototype.clearEvent = function () {
      this._var.event.detected = []
      this._var.event.notDetected = []
      if (this._options.debug === true) {
        this._log('clearEvent', 'The event list has been cleared')
      }
    }
    f16.prototype.on = function (p166, p167) {
      this._var.event[p166 === true ? 'detected' : 'notDetected'].push(p167)
      if (this._options.debug === true) {
        this._log(
          'on',
          'A type of event "' + (p166 === true ? 'detected' : 'notDetected') + '" was added'
        )
      }
      return this
    }
    f16.prototype.onDetected = function (p168) {
      return this.on(true, p168)
    }
    f16.prototype.onNotDetected = function (p169) {
      return this.on(false, p169)
    }
    p156.FuckAdBlock = f16
    if (p156.fuckAdBlock === undefined) {
      p156.fuckAdBlock = new f16({
        checkOnLoad: true,
        resetOnEnd: true
      })
    }
  })(window)
  ;((p170, p171) => {
    var v162
    var v163
    if (typeof exports == 'object' && typeof module != 'undefined') {
      module.exports = p171()
    } else if (typeof define == 'function' && define.amd) {
      define(p171)
    } else {
      v162 = p170.Base64
      ;(v163 = p171()).noConflict = function () {
        p170.Base64 = v162
        return v163
      }
      if (p170.Meteor) {
        Base64 = v163
      }
      p170.Base64 = v163
    }
  })(
    typeof self != 'undefined'
      ? self
      : typeof window != 'undefined'
        ? window
        : typeof global != 'undefined'
          ? global
          : this,
    function () {
      function f18(p172) {
        return p172.replace(/=/g, '').replace(/[+\/]/g, function (p173) {
          if (p173 == '+') {
            return '-'
          } else {
            return '_'
          }
        })
      }
      function f19(p174) {
        var v164
        var v165
        var v166
        var v167 = ''
        var v168 = p174.length % 3
        for (var v169 = 0; v169 < p174.length; ) {
          if (
            (v164 = p174.charCodeAt(v169++)) > 255 ||
            (v165 = p174.charCodeAt(v169++)) > 255 ||
            (v166 = p174.charCodeAt(v169++)) > 255
          ) {
            throw new TypeError('invalid character found')
          }
          v167 +=
            v183[((v164 = (v164 << 16) | (v165 << 8) | v166) >> 18) & 63] +
            v183[(v164 >> 12) & 63] +
            v183[(v164 >> 6) & 63] +
            v183[v164 & 63]
        }
        if (v168) {
          return v167.slice(0, v168 - 3) + '==='.substring(v168)
        } else {
          return v167
        }
      }
      function f20(p175, p176) {
        if ((p176 = p176 === undefined ? false : p176)) {
          return f18(v188(p175))
        } else {
          return v188(p175)
        }
      }
      function f21(p177) {
        var v170
        if (p177.length < 2) {
          if ((v170 = p177.charCodeAt(0)) < 128) {
            return p177
          } else if (v170 < 2048) {
            return v185((v170 >>> 6) | 192) + v185((v170 & 63) | 128)
          } else {
            return (
              v185(((v170 >>> 12) & 15) | 224) +
              v185(((v170 >>> 6) & 63) | 128) +
              v185((v170 & 63) | 128)
            )
          }
        } else {
          v170 = 65536 + (p177.charCodeAt(0) - 55296) * 1024 + (p177.charCodeAt(1) - 56320)
          return (
            v185(((v170 >>> 18) & 7) | 240) +
            v185(((v170 >>> 12) & 63) | 128) +
            v185(((v170 >>> 6) & 63) | 128) +
            v185((v170 & 63) | 128)
          )
        }
      }
      function f22(p178) {
        return p178.replace(v192, f21)
      }
      function f23(p179, p180) {
        if ((p180 = p180 === undefined ? false : p180)) {
          return f18(v193(p179))
        } else {
          return v193(p179)
        }
      }
      function f24(p181) {
        return f23(p181, true)
      }
      function f25(p182) {
        switch (p182.length) {
          case 4:
            var v171 =
              (((p182.charCodeAt(0) & 7) << 18) |
                ((p182.charCodeAt(1) & 63) << 12) |
                ((p182.charCodeAt(2) & 63) << 6) |
                (p182.charCodeAt(3) & 63)) -
              65536
            return v185(55296 + (v171 >>> 10)) + v185(56320 + (v171 & 1023))
          case 3:
            return v185(
              ((p182.charCodeAt(0) & 15) << 12) |
                ((p182.charCodeAt(1) & 63) << 6) |
                (p182.charCodeAt(2) & 63)
            )
          default:
            return v185(((p182.charCodeAt(0) & 31) << 6) | (p182.charCodeAt(1) & 63))
        }
      }
      function f26(p183) {
        return p183.replace(v194, f25)
      }
      function f27(p184) {
        p184 = p184.replace(/\s+/g, '')
        if (!v184.test(p184)) {
          throw new TypeError('malformed base64.')
        }
        p184 += '=='.slice(2 - (p184.length & 3))
        var v172
        var v173
        var v174
        var v175 = ''
        for (var v176 = 0; v176 < p184.length; ) {
          v172 =
            (vV177[p184.charAt(v176++)] << 18) |
            (vV177[p184.charAt(v176++)] << 12) |
            ((v173 = vV177[p184.charAt(v176++)]) << 6) |
            (v174 = vV177[p184.charAt(v176++)])
          v175 +=
            v173 === 64
              ? v185((v172 >> 16) & 255)
              : v174 === 64
                ? v185((v172 >> 16) & 255, (v172 >> 8) & 255)
                : v185((v172 >> 16) & 255, (v172 >> 8) & 255, v172 & 255)
        }
        return v175
      }
      function f28(p185) {
        return v196(f29(p185))
      }
      function f29(p186) {
        return f36(
          p186.replace(/[-_]/g, function (p187) {
            if (p187 == '-') {
              return '+'
            } else {
              return '/'
            }
          })
        )
      }
      function f30(p188) {
        return v197(f29(p188))
      }
      function f31(p189) {
        return {
          value: p189,
          enumerable: false,
          writable: true,
          configurable: true
        }
      }
      function f32() {
        function f33(p190, p191) {
          Object.defineProperty(String.prototype, p190, f31(p191))
        }
        f33('fromBase64', function () {
          return f30(this)
        })
        f33('toBase64', function (p192) {
          return f23(this, p192)
        })
        f33('toBase64URI', function () {
          return f23(this, true)
        })
        f33('toBase64URL', function () {
          return f23(this, true)
        })
        f33('toUint8Array', function () {
          return f28(this)
        })
      }
      function f34() {
        function f35(p193, p194) {
          Object.defineProperty(Uint8Array.prototype, p193, f31(p194))
        }
        f35('toBase64', function (p195) {
          return f20(this, p195)
        })
        f35('toBase64URI', function () {
          return f20(this, true)
        })
        f35('toBase64URL', function () {
          return f20(this, true)
        })
      }
      var v177
      var v178 = typeof atob == 'function'
      var v179 = typeof btoa == 'function'
      var v180 = typeof Buffer == 'function'
      var v181 = typeof TextDecoder == 'function' ? new TextDecoder() : undefined
      var v182 = typeof TextEncoder == 'function' ? new TextEncoder() : undefined
      var v183 = Array.prototype.slice.call(
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
      )
      v177 = {}
      v183.forEach(function (p196, p197) {
        return (v177[p196] = p197)
      })
      var vV177 = v177
      var v184 = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/
      var v185 = String.fromCharCode.bind(String)
      var v186 =
        typeof Uint8Array.from == 'function'
          ? Uint8Array.from.bind(Uint8Array)
          : function (p198) {
              return new Uint8Array(Array.prototype.slice.call(p198, 0))
            }
      function f36(p199) {
        return p199.replace(/[^A-Za-z0-9\+\/]/g, '')
      }
      var v187 = v179
        ? function (p200) {
            return btoa(p200)
          }
        : v180
          ? function (p201) {
              return Buffer.from(p201, 'binary').toString('base64')
            }
          : f19
      var v188 = v180
        ? function (p202) {
            return Buffer.from(p202).toString('base64')
          }
        : function (p203) {
            var v189 = []
            for (var v190 = 0, v191 = p203.length; v190 < v191; v190 += 4096) {
              v189.push(v185.apply(null, p203.subarray(v190, v190 + 4096)))
            }
            return v187(v189.join(''))
          }
      var v192 = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g
      var v193 = v180
        ? function (p204) {
            return Buffer.from(p204, 'utf8').toString('base64')
          }
        : v182
          ? function (p205) {
              return v188(v182.encode(p205))
            }
          : function (p206) {
              return v187(f22(p206))
            }
      var v194 = /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g
      var v195 = v178
        ? function (p207) {
            return atob(f36(p207))
          }
        : v180
          ? function (p208) {
              return Buffer.from(p208, 'base64').toString('binary')
            }
          : f27
      var v196 = v180
        ? function (p209) {
            return v186(Buffer.from(p209, 'base64'))
          }
        : function (p210) {
            return v186(
              v195(p210)
                .split('')
                .map(function (p211) {
                  return p211.charCodeAt(0)
                })
            )
          }
      var v197 = v180
        ? function (p212) {
            return Buffer.from(p212, 'base64').toString('utf8')
          }
        : v181
          ? function (p213) {
              return v181.decode(v196(p213))
            }
          : function (p214) {
              return f26(v195(p214))
            }
      var v198 = {
        version: '3.7.5',
        VERSION: '3.7.5',
        atob: v195,
        atobPolyfill: f27,
        btoa: v187,
        btoaPolyfill: f19,
        fromBase64: f30,
        toBase64: f23,
        encode: f23,
        encodeURI: f24,
        encodeURL: f24,
        utob: f22,
        btou: f26,
        decode: f30,
        isValid: function (p215) {
          return (
            typeof p215 == 'string' &&
            ((p215 = p215.replace(/\s+/g, '').replace(/={0,2}$/, '')),
            !/[^\s0-9a-zA-Z\+/]/.test(p215) || !/[^\s0-9a-zA-Z\-_]/.test(p215))
          )
        },
        fromUint8Array: f20,
        toUint8Array: f28,
        extendString: f32,
        extendUint8Array: f34,
        extendBuiltins: function () {
          f32()
          f34()
        },
        Base64: {}
      }
      Object.keys(v198).forEach(function (p216) {
        return (v198.Base64[p216] = v198[p216])
      })
      return v198
    }
  )
  __Cpn.prototype.initPostedMessageOverride =
    __Cpn.prototype.initPostedMessageOverride ||
    function (p217, p218) {
      this.PostedMessageOverride = class {
        static create() {
          return new this()
        }
        constructor() {
          this.t = '__data'
          this.i = '__origin'
        }
        h() {
          let vThis3 = this
          p217.__cpPreparePostMessageData = function (p219) {
            var v199
            if ('Window' in p217) {
              ;(v199 = new p217.Object())[vThis3.t] = vThis3.o(p219)
              v199[vThis3.i] = p218.u.origin
              return v199
            } else {
              return p219
            }
          }
          p217.__cpPreparePostMessageOrigin = function (p220) {
            if ('Window' in p217 && (typeof p220 == 'string' || p220 instanceof String)) {
              return '*'
            } else {
              return p220
            }
          }
          function f37(p221) {
            p221 = p221()
            if (vThis3.l(p221)) {
              return p221[vThis3.t]
            } else {
              return p221
            }
          }
          function f38(p222) {
            var v200 = this.__cpOriginalData
            if (vThis3.l(v200)) {
              return v200[vThis3.i]
            } else if (this.source && this.source.location) {
              v200 = this.source.location.href
              v200 = p218.Uri.create(v200).p()
              return new p218.URI(v200).origin()
            } else {
              return p222()
            }
          }
          if ('MessageEvent' in p217) {
            try {
              p218.v(p217.MessageEvent.prototype, 'data', f37, function () {})
            } catch (_0x2d77ab) {
              p218.m(_0x2d77ab)
            }
            try {
              p218.v(p217.MessageEvent.prototype, 'origin', f38, function () {})
            } catch (_0x2dc98c) {
              p218.m(_0x2dc98c)
            }
          }
          if ('ExtendableMessageEvent' in p217) {
            try {
              p218.v(p217.ExtendableMessageEvent.prototype, 'data', f37, function () {})
            } catch (_0x1909b2) {
              p218.m(_0x1909b2)
            }
            try {
              p218.v(p217.ExtendableMessageEvent.prototype, 'origin', f38, function () {})
            } catch (_0x3d622c) {
              p218.m(_0x3d622c)
            }
          }
          return this
        }
        l(p223) {
          return !!p223 && typeof p223 == 'object' && !!(this.t in p223) && !!(this.i in p223)
        }
        o(p224) {
          if (p224) {
            if (this.l(p224)) {
              return p224[this.t]
            }
            if (p217.Array.isArray(p224)) {
              for (var v201 = 0; v201 < p224.length; v201++) {
                if (this.l(p224[v201])) {
                  p224[v201] = p224[v201][this.t]
                } else {
                  this.o(p224[v201])
                }
              }
            } else if (typeof p224 == 'object') {
              for (var v202 in p224) {
                if (this.l(p224[v202])) {
                  p224[v202] = p224[v202][this.t]
                } else {
                  this.o(p224[v202])
                }
              }
            }
          }
          return p224
        }
      }
      return this
    }
  __Cpn.prototype.initCpn =
    __Cpn.prototype.initCpn ||
    function (p225, p226, p227, p228) {
      var v203
      var v204
      var v205
      this.g = '__cp'
      this._ = '__cpp'
      this.$ = '__cpOriginal'
      this.A = '__cpOriginalValueOf'
      this.k = '__cpo'
      this.C = '__cpc'
      this.S = '/__cpi.php'
      this.B = 'cp'
      this.P = 'property'
      this.T = 'attribute'
      this.R = '__cpGenerated'
      this.F = '__cpLocation'
      this.U = new p225.Array()
      this.O = new p225.Array('#__cpsHeaderZapper', '#__cpsFooter')
      this.D = p225
      this.I = p226
      this.j = p227
      this.u = p228
      v204 = (v203 = this).URI.prototype.toString
      v203.URI.prototype.valueOf = v203.URI.prototype.toString = function () {
        return v204.call(this).replace(/##$/, '#')
      }
      v205 = v203.URI
      v203.URI = function (p229, p230) {
        if (!(p229 = (p229 += '').trim())) {
          return v205('', p230)
        }
        let v206
        var v207 = p229.match(/^([a-z0-9+-.]+):\/\//i)
        if (!(v206 = v207 && v207[1] ? v207[1] : v206) || !!v206.match(/^(http|https)/i)) {
          if ((p229 = p229.replace(/(^[a-z]*:?)\/{3,}/i, '$1//')).match(/(%[^0-9a-f%])|(%$)/i)) {
            v203.H('Invalid url ' + p229 + ' fixed')
            p229 = p225.encodeURI(p229)
          }
          if (p229.match(/#$/)) {
            v203.H('Empty hash ' + p229 + ' fixed')
            p229 += '#'
          }
        }
        return v205(p229, p230)
      }
      this.M = function () {
        if ('permalink' in this && this.permalink) {
          return this.permalink
        }
        this.L('No permalink defined for this window')
      }
      this.N = function () {
        return (
          (!!p225.location &&
            !!p225.location.hostname &&
            !!p225.location.hostname.match(/(proxy|localhost|local)$/i)) ||
          !!this.debugMode
        )
      }
      this.W = function (p231) {
        if (p225.closed) {
          console.log('[CP CLOSED WINDOW]', p231)
        } else if (this.N()) {
          p225.console.log('[CP]', p231)
        }
        return this
      }
      this.H = function (p232) {
        var v208
        if (p225.closed) {
          v208 = '[CP CLOSED WINDOW]'
          if (p232 instanceof Error) {
            console.warn(v208, p232.message)
            if (p232.stack) {
              console.warn(p232.stack)
            }
          } else {
            console.warn(v208, p232)
          }
        } else if (this.N()) {
          v208 = '[CP ' + p225.location.href + ']'
          if (p232 instanceof p225.Error) {
            p225.console.warn(v208, p232.message)
            if (p232.stack) {
              p225.console.warn(p232.stack)
            }
          } else {
            p225.console.warn(v208, p232)
          }
        }
        return this
      }
      this.m = function (p233) {
        return this.H(p233)
      }
      this.L = function (p234) {
        throw new p225.Error('[CP Error] ' + p234)
      }
      this.G = function (p235, p236 = '') {
        this.H((p236 ? p236 + '; ' : '') + p235.message)
        return this
      }
      this.Z = function () {
        try {
          return p225.self !== p225.top
        } catch (_0x2e0f02) {
          return true
        }
      }
      this.q = function (p237) {
        return p237.charAt(0).toUpperCase() + p237.slice(1)
      }
      this.V = function (p238) {
        return p238 instanceof p225.Element
      }
      this.Y = function (p239) {
        return this.V(p239) && p225.document.documentElement.contains(p239)
      }
      this.X = function (p240) {
        var v209
        var v210 = 0
        if (p240.length === 0) {
          return v210
        }
        for (v209 = 0; v209 < p240.length; v209++) {
          v210 = (v210 << 5) - v210 + p240.charCodeAt(v209)
          v210 |= 0
        }
        return Math.abs(v210)
      }
      this.J = function (p241, p242) {
        return p241 + this.q(p242)
      }
      this.K = function (p243, p244 = null) {
        if (Object.getOwnPropertyDescriptor(p243, 'url')) {
          return Promise.resolve(p243)
        } else {
          return p243.blob().then((p245) => {
            var v211 = ''
            var v212 = p243.url
            try {
              v212 = this.Uri.create(v212).tt(new p225.Object(), p244)
            } catch (_0x5cf334) {
              this.H(_0x5cf334.message + ' (url)')
            }
            try {
              if (p243.referrer && (v213 = this.Uri.create(p243.referrer)).it() !== '1') {
                v211 = v213.tt(new p225.Object(), p244)
              }
            } catch (_0x4b2c2a) {
              this.H(_0x4b2c2a.message + ' (referrer)')
            }
            var v213 = new p225.Request(
              v212,
              new p225.Object({
                method: p243.method,
                keepalive: p243.keepalive,
                headers: new Headers(p243.headers),
                mode: 'cors',
                credentials: 'include',
                cache: 'default',
                redirect: p243.redirect,
                referrer: v211,
                body: p243.method !== 'GET' && p243.method !== 'HEAD' ? p245 : undefined
              })
            )
            return Promise.resolve(v213)
          })
        }
      }
      this.et = function (p246, p247, p248, f39 = true, p250 = false, p251 = false) {
        if (typeof p246 != 'object' && typeof p246 != 'function') {
          this.L('No object to replace method ' + p247)
        }
        var v214 = p246[p247]
        if (typeof v214 != 'function') {
          this.L('No method ' + p247 + ' defined in object ' + p246.constructor.name)
        }
        if (f39) {
          f39 = function () {
            if (p251) {
              return new v214(...arguments)
            } else {
              return v214.apply(this, arguments)
            }
          }
          if (p250) {
            f39 = f39.bind(p246)
          }
          p246[this.J(this.$, p247)] = f39
        }
        function f39() {
          return p248.call(
            this,
            (p252) => (p251 ? new v214(...p252) : v214.apply(this, p252)),
            p225.Array.from(arguments)
          )
        }
        if (p250) {
          f39 = f39.bind(p246)
        }
        p246[p247] = f39
        Object.defineProperty(p246, '__cpn', {
          value: this,
          writable: false,
          configurable: false,
          enumerable: false
        })
        return (p246.__cpn = this)
      }
      this.v = function (p253, p254, p255, p256, p257 = true, p258 = false) {
        if (p253 instanceof p225.Array) {
          var v215
          var v222 = p253
          p253 = new p225.Object()
          for (v215 of v222) {
            if (p254 in v215) {
              p253 = v215
              break
            }
          }
        }
        if (typeof p253 != 'object') {
          this.L('No object to replace property ' + p254)
        }
        if (!(p254 in p253)) {
          this.L('No property ' + p254 + ' defined in object ' + p253.constructor.name)
        }
        var v216
        var v217
        var v218
        var v219
        var v220
        var v221
        var v222 = p225.Object.getOwnPropertyDescriptor(p253, p254)
        if (!v222 || !v222.configurable) {
          this.L(
            'No configurable descriptor for object ' + p253.constructor.name + ', property ' + p254
          )
        }
        var vF2 = (p259, p260, p261) => {
          p259[p260] = p261
          if (this.V(p259)) {
            p259.setAttribute(p260, p261)
          }
          return this
        }
        v216 = v222
        v217 = this
        p225.Object.defineProperty(
          p253,
          p254,
          new p225.Object({
            set: function (p262) {
              vF2(this, v217.J(v217.A, p254), p262)
              p256.call(
                this,
                (p263) => {
                  if (v216.set) {
                    v216.set.call(this, p263)
                  }
                },
                p262,
                v217.P
              )
            },
            get: function () {
              return p255.call(this, () => v216.get.call(this), v217.P)
            },
            configurable: true,
            enumerable: true
          })
        )
        if (p257) {
          p225.Object.defineProperty(
            p253,
            this.J(this.$, p254),
            new p225.Object({
              set: function (p264) {
                if (v216.set) {
                  v216.set.call(this, p264)
                }
              },
              get: function () {
                return v216.get.call(this)
              },
              configurable: p258,
              enumerable: false
            })
          )
        }
        p254 = p254.toLowerCase()
        if (
          'Element' in p225 &&
          p253 instanceof p225.Element &&
          typeof p253.getAttribute == 'function'
        ) {
          v220 = p253.setAttribute
          v221 = this
          p253.setAttribute = function (p265, p266) {
            var v223 = p265.toLowerCase()
            if (v223 === p254) {
              vF2(this, v221.J(v221.A, p254), p266)
              p256.call(
                this,
                (p267) => {
                  v220.call(this, p254, p267)
                },
                p266,
                v221.T
              )
            } else {
              if (p257 && v223 === v221.$.toLowerCase() + p254) {
                p265 = p254
              }
              v220.call(this, p265, p266)
            }
          }
          v218 = p253.getAttribute
          v219 = this
          p253.getAttribute = function (p268) {
            var v224 = p268.toLowerCase()
            if (v224 === p254) {
              return p255.call(this, () => v218.call(this, p254), v219.T)
            } else {
              if (p257 && v224 === v219.$.toLowerCase() + p254) {
                p268 = p254
              }
              return v218.call(this, p268)
            }
          }
        }
        Object.defineProperty(p253, '__cpn', {
          value: this,
          writable: false,
          configurable: false,
          enumerable: false
        })
        return this
      }
      this.rt = function () {
        return Math.floor(Date.now() / 1000) + '.' + Math.floor(Math.random() * 10000000000)
      }
      this.nt = function (p269, p270) {
        var v225 = p225.Element.prototype
        return (
          v225.matches ||
          v225.matchesSelector ||
          v225.webkitMatchesSelector ||
          v225.mozMatchesSelector ||
          v225.msMatchesSelector ||
          v225.oMatchesSelector
        ).call(p269, p270)
      }
      this.st = function (p271) {
        return p225.encodeURIComponent(this.B64.encode(p271))
      }
      this.ht = function (p272) {
        return p225.decodeURIComponent(this.B64.decode(p272))
      }
      this.ot = function () {
        if (p225.document.title.length > 256) {
          return p225.document.title.substring(0, 256) + '...'
        } else {
          return p225.document.title
        }
      }
      this.at = function () {
        var v226 = p225.document.querySelector('meta[name="description"]')
        if (v226) {
          v226 = v226.getAttribute('content')
          if (v226) {
            if (v226.length > 256) {
              return v226.substring(0, 256) + '...'
            } else {
              return v226
            }
          }
        }
        return ''
      }
      this.ut = function (p273) {
        return p273.isTrusted
      }
      this.ct = function (p274) {
        return p274[Math.floor(Math.random() * p274.length)]
      }
      this.ft = function (p275 = null) {
        let v227
        if (p275) {
          ;(v227 = this.URI(p275)).origin(this.j)
          return v227.toString()
        } else if ((v227 = this.j + this.URI(p225.location.href).directory()).slice(-1) === '/') {
          return v227
        } else {
          return v227 + '/'
        }
      }
      return this
    }
  __Cpn.prototype.initScope =
    __Cpn.prototype.initScope ||
    function (p276, p277) {
      this.Scope = class {
        lt() {
          try {
            p277.et(
              p276,
              'fetch',
              function (p278, p279) {
                var v228 = p279[0]
                if (!(v228 instanceof Request)) {
                  v228 = new Request(v228)
                }
                return this.__cpn.K(v228).then(function (p280) {
                  var v229 = p279[1]
                  if (typeof v229 == 'object') {
                    v229.mode = p280.mode
                    v229.credentials = p280.credentials
                    v229.cache = p280.cache
                    v229.referrer = p280.referrer
                    delete v229.integrity
                    p279[1] = v229
                  }
                  p279[0] = p280
                  return p278(p279)
                })
              },
              true,
              true
            )
          } catch (_0x404455) {
            p277.m(_0x404455)
          }
          return this
        }
        j() {
          p276.origin = p277.u.origin
          return this
        }
        dt() {
          try {
            p277.v(
              p276.ServiceWorkerRegistration.prototype,
              'scope',
              function (p281) {
                p281 = this.__cpn.URI(p281())
                p281.origin(this.__cpn.u.origin)
                return p281.toString()
              },
              function () {}
            )
          } catch (_0x1c02b0) {
            p277.m(_0x1c02b0)
          }
          return this
        }
        vt() {
          if ('XMLHttpRequest' in p276) {
            try {
              p277.et(p276.XMLHttpRequest.prototype, 'open', function (p282, p283) {
                p283[1] = this.__cpn.Uri.create(p283[1]).tt()
                return p282(p283)
              })
            } catch (_0x2b1728) {
              p277.m(_0x2b1728)
            }
            try {
              p277.v(
                p276.XMLHttpRequest.prototype,
                'responseURL',
                function (p284) {
                  return this.__cpn.Uri.create(p284()).p()
                },
                function () {}
              )
            } catch (_0x3a4ffd) {
              p277.m(_0x3a4ffd)
            }
          }
          return this
        }
        yt(p285, p286, p287 = false, p288 = false) {
          p277.v(
            p285,
            p286,
            function (p289) {
              p289 = this.__cpn.Uri.create(p289())
              if (p288 && !p289.gt(true)) {
                return ''
              } else {
                return p289.p()
              }
            },
            p287
              ? function () {}
              : function (p290, p291) {
                  p290(this.__cpn.Uri.create(p291).tt())
                }
          )
          return this
        }
      }
      return this
    }
  __Cpn.prototype.initUri =
    __Cpn.prototype.initUri ||
    function (p292, p293) {
      this.Uri = class {
        static create(p294, p295 = false) {
          return new this(p294, p295)
        }
        constructor(p296, p297 = false) {
          this.uri = null
          if ((!p297 && p296 != null) || (p297 && p296)) {
            this.uri = p293.URI((p296 += ''))
          }
          this.url = p296
        }
        wt() {
          return (
            !!this.uri &&
            (!this.uri.protocol() ||
              this.uri.protocol() === 'http' ||
              this.uri.protocol() === 'https')
          )
        }
        bt() {
          return (
            !!this.uri &&
            !!this.url &&
            !p293.U.every((p298) => !this.url.match(new p292.RegExp(p298)))
          )
        }
        _t(p299 = false) {
          return this.uri.hasSearch(p293.k) && (!p299 || (this.it() !== '1' && p299))
        }
        gt(p300 = false) {
          return !this.wt() || this.bt() || this._t(p300)
        }
        $t() {
          return !!this.url && !!this.url.match(/^blob:/i)
        }
        it() {
          if (this.wt()) {
            return this.uri.query(true)[p293.k]
          } else {
            return null
          }
        }
        xt() {
          return p293.j + p293.S + '?r=' + p293.B64.encode(this.url) + '&' + p293.k + '=1'
        }
        tt(p301 = new p292.Object(), p302 = null) {
          if (this.gt()) {
            if (this._t()) {
              return this.uri.clone().absoluteTo(p292.location.href).toString()
            } else {
              return this.url
            }
          }
          try {
            if ((v233 = this.uri.clone()).origin() && p293.URI(v233.origin()).equals(p293.j)) {
              v233.origin('')
            }
            if (
              !(v233 = (p302 = p302 || p293.u.At()) ? v233.absoluteTo(p302) : v233).protocol() ||
              !v233.hostname()
            ) {
              p293.L('No origin for url ' + this.url + ', possible result is ' + v233)
            }
            var v230
            var v231 = btoa(v233.origin()).replace(/=+$/g, '')
            v233 = this.kt(v233.origin(p293.j), p293.k, v231)
            for (v230 in p301) {
              var v232 = p301[v230]
              var v233 = this.kt(v233, p293.B + ':' + v230, v232)
            }
            return v233.toString()
          } catch (_0x27a1c7) {
            p293.H(this.url + ': ' + _0x27a1c7.message + '; base url: ' + (p302 || '-'))
            return this.url
          }
        }
        p() {
          var v234 = this.it()
          if (!v234 || v234 === '1') {
            return this.url
          }
          try {
            var vAtob = atob(v234)
          } catch (_0x41f875) {
            p293.G(_0x41f875, 'Wrong CPO hash supplied, url: ' + this.url)
            return this.url
          }
          var v235
          var v236 = this.uri.clone().removeSearch(p293.k)
          for (v235 in v236.query(true)) {
            if (v235.match(new p292.RegExp('^' + p293.B + ':', 'i'))) {
              v236.removeSearch(v235)
            }
          }
          return v236.origin(vAtob).toString().replace(p293.F, 'location').trim()
        }
        Et() {
          var v237 = p293.URI(this.url)
          return this.kt(v237, p293.k, '1') + ''
        }
        kt(p303, p304, p305) {
          p304 = p292.encodeURIComponent(p304) + '=' + p292.encodeURIComponent(p305)
          p304 = (p303.search() ? '&' : '?') + p304
          return p303.search(p303.search() + p304)
        }
      }
      return this
    }
  __Cpn.prototype.initElement =
    __Cpn.prototype.initElement ||
    function (p306, p307) {
      this.Element = class C {
        static create(p308) {
          return new this(p308)
        }
        constructor(p309) {
          if (!p307.V(p309)) {
            throw new TypeError('Wrong argument passed. Should be instance of Element')
          }
          this.Ct = p309
          this.St = new p306.Object({
            a: () => {
              this.Bt('href')
            },
            area: () => {
              this.Bt('href')
            },
            form: () => {
              this.Bt('action')
            },
            video: () => {
              this.Bt('src', true)
            },
            audio: () => {
              this.Bt('src', true)
            },
            source: () => {
              this.Bt('src', true)
            },
            use: () => {
              this.Bt('href', true)
            },
            iframe: () => {
              var v238
              var v239
              var v240
              var v241
              var v244 = this.Pt('src')
              var v243 = p307.Uri.create(v244)
              var v244 = !!v244 && !!v243.wt() && !v243.gt()
              var v245 = this.Ct.hasAttribute('sandbox')
              if (
                (v244 || v245) &&
                ((v238 = this.Ct.parentNode),
                (v239 = document.createElement('framestub')),
                v238 && v238.replaceChild(v239, this.Ct),
                v244 && this.Bt('src', true),
                v245 && this.Ct.removeAttribute('sandbox'),
                v238)
              ) {
                v238.replaceChild(this.Ct, v239)
              }
              if (v243.$t()) {
                p307.H('TODO: blob iframe detected: ' + v243.toString())
              }
              var v246 = p307.X(this.Ct.outerHTML)
              var vF3 = () => {
                var v247
                if (!(p307._ in this.Ct.contentWindow)) {
                  ;(v247 = function () {}).prototype = p306.Object.getPrototypeOf(p307)
                  new v247().init(this.Ct.contentWindow, p307.I, p307.j, p306.location.href)
                  p307.W('frame ' + v246 + ' initialized')
                }
              }
              if (this.Ct.contentWindow) {
                vF3()
              } else {
                v240 = 0
                v241 = p306.setInterval(() => {
                  if (this.Ct.contentWindow) {
                    vF3()
                  }
                  if (v240 >= 200 || this.Ct.contentWindow) {
                    p307.W('interval for frame ' + v246 + ' cleared, counter ' + v240)
                    p306.clearInterval(v241)
                  } else {
                    v240++
                    p307.W('frame load interval ' + v246)
                  }
                }, 10)
              }
            },
            base: () => {
              var v248
              var v249
              if (!this.Tt(p307.R)) {
                if (
                  (v248 = p306.document.head) &&
                  (v249 = v248.querySelector('base[' + p307.R + ']'))
                ) {
                  v248.removeChild(v249)
                }
              }
              p307.Element.create(p306.document.documentElement).Rt()
            }
          })
          this.Ft = new p306.Object({
            a: () => {
              this.Ut('href')
            },
            area: () => {
              this.Ut('href')
            },
            form: () => {
              this.Ut('action')
            }
          })
        }
        Ot() {
          if ('tagName' in this.Ct && this.Ct.tagName) {
            return this.Ct.tagName.toLowerCase()
          } else {
            return null
          }
        }
        Tt(p310) {
          return this.Ct.hasAttribute(p310)
        }
        Dt(p311) {
          return this.Ct.getAttribute(p311)
        }
        It(p312, p313) {
          try {
            this.Ct[p312] = p313
          } catch (_0x3247c1) {
            p307.H(_0x3247c1.message)
          }
          this.Ct.setAttribute(p312, p313)
          return this
        }
        Pt(p314) {
          return this.Dt(p307.J(p307.$, p314))
        }
        jt(p315, p316) {
          return this.It(p307.J(p307.$, p315), p316)
        }
        Ht(p317) {
          return this.Dt(p307.J(p307.A, p317))
        }
        Mt(p318, p319) {
          return this.It(p307.J(p307.A, p318), p319)
        }
        Lt(p320) {
          return this.Tt(p307.J(p307.A, p320))
        }
        gt() {
          return !!this.Dt(p307._) || !!this.Ct[p307._]
        }
        bt() {
          if (!p307.O.every((p321) => !p307.nt(this.Ct, p321))) {
            return true
          }
          if (p307.V(this.Ct.parentElement)) {
            try {
              return C.create(this.Ct.parentElement).bt()
            } catch (_0x34772e) {}
          }
          return false
        }
        tt() {
          var v250
          if (!this.gt() && !this.bt()) {
            this.It(p307._, '1')
            v250 = this.Ot()
            if (this.St[v250]) {
              this.St[v250]()
            }
          }
          return this
        }
        Nt() {
          this.tt()
          if (this.Ct.children.length && !this.bt()) {
            for (var v251 of this.Ct.children) {
              if (p307.V(v251)) {
                try {
                  C.create(v251).Nt()
                } catch (_0x17f446) {}
              }
            }
          }
          return this
        }
        Wt() {
          var v252 = this.Ot()
          if (this.Ft[v252]) {
            this.Ft[v252]()
          }
          return this
        }
        Rt() {
          this.Wt()
          if (this.Ct.children.length) {
            for (var v253 of this.Ct.children) {
              if (p307.V(v253)) {
                try {
                  C.create(v253).Rt()
                } catch (_0xaf5cc7) {}
              }
            }
          }
          return this
        }
        Bt(p322, v255 = false) {
          var v254 = this.Pt(p322)
          var v255 = p307.Uri.create(v254, v255)
          if (!v255.gt()) {
            this.jt(p322, v255.tt())
            this.Mt(p322, v254)
          }
          return this
        }
        Ut(p324) {
          var v256
          if (this.Lt(p324)) {
            v256 = this.Ht(p324)
            this.jt(p324, p307.Uri.create(v256).tt())
          }
          return this
        }
      }
      return this
    }
  __Cpn.prototype.initCookie =
    __Cpn.prototype.initCookie ||
    function (p325, p326) {
      this.Cookie = class C2 {
        static create(p327) {
          return new this(p327)
        }
        constructor(p328) {
          this.cookieString = p328
        }
        zt() {
          var v257
          var v258
          var v259 = this.Gt(this.cookieString)
          if (
            v259 !== null &&
            !this.Zt(v259.name) &&
            ((v257 = 'domain' in v259 ? v259.domain.replace(/^\./, '') : p326.u.qt().hostname()),
            this.Vt(v257))
          ) {
            v258 = v259.name.replace(/^(__host)|(__secure)/i, '__$1')
            v259.name = v258 + '@' + v257
            v259.domain = p326.I
            v259.path = 'path' in v259 ? v259.path : '/'
            v259.secure = true
            return C2.Qt(v259)
          } else {
            return null
          }
        }
        Yt() {
          var v260 = this.Gt(this.cookieString)
          if (v260 !== null && this.Zt(v260.name)) {
            return C2.Qt(v260)
          } else {
            return null
          }
        }
        Xt() {
          var v261
          var v262 = new p325.Array()
          for (v261 of C2.Jt(this.cookieString, false)) {
            var v263
            var v266 = v261.name
            var v265 = v261.value
            var v266 = C2.Kt('@', v266)
            if (
              1 in v266 &&
              ((v263 = (v263 = v266[0]).replace(/^__(__host)|(__secure)/i, '$1')),
              (v266 = v266[1]),
              this.Vt(v266))
            ) {
              v262.push(v263 + '=' + v265)
            }
          }
          return v262.join('; ')
        }
        ti() {
          var v267
          var v268 = new p325.Array()
          for (v267 of C2.Jt(this.cookieString, false)) {
            var v269 = v267.name
            var v270 = v267.value
            if (this.Zt(v269)) {
              v268.push(v269 + '=' + v270)
            }
          }
          return v268.join('; ')
        }
        Zt(p329) {
          return !!p329.match(new p325.RegExp('^' + p326.C, 'i'))
        }
        Vt(p330) {
          return !!p326.u
            .qt()
            .hostname()
            .match(new p325.RegExp(this.ii(p330), 'i'))
        }
        ii(p331) {
          return p331.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&')
        }
        Gt(p332) {
          if (!p332) {
            return null
          }
          var v271 = new p325.Object()
          for (var v272 = p332.split(';'), v273 = 0; v273 < v272.length; v273++) {
            var v274 = C2.Kt('=', v272[v273])
            if (v273) {
              v271[v274[0].trim().toLowerCase()] = !(1 in v274) || v274[1]
            } else {
              if (!(1 in v274)) {
                return null
              }
              v271.name = v274[0].trim()
              v271.value = v274[1]
            }
          }
          return v271
        }
        static Jt(p333, p334 = true) {
          var v275 = new (p334 ? p325.Object : p325.Array)()
          for (var v276 = p333.split(';'), v277 = 0; v277 < v276.length; v277++) {
            var v278 = C2.Kt('=', v276[v277])
            if (1 in v278) {
              if (p334) {
                v275[v278[0].trim()] = v278[1]
              } else {
                v275.push({
                  name: v278[0].trim(),
                  value: v278[1]
                })
              }
            }
          }
          return v275
        }
        static ei(p335, p336, p337 = null) {
          p335 = this.Jt(p335)
          if (p336 in p335) {
            return p335[p336]
          } else {
            return p337
          }
        }
        static Qt(p338) {
          var v279
          var v280 = new p325.Array()
          if (!('name' in p338) || !p338.name) {
            return null
          }
          v280.push(p338.name + '=' + p338.value)
          delete p338.name
          delete p338.value
          for (v279 in p338) {
            var v281 = p338[v279]
            if (v281 === true) {
              v280.push(v279)
            } else if (v281 !== false) {
              v280.push(v279 + '=' + v281)
            }
          }
          return v280.join(';')
        }
        static Kt(p339, p340) {
          p339 = p340.indexOf(p339)
          if (p339 >= 0) {
            return new p325.Array(p340.slice(0, p339), p340.slice(p339 + 1))
          } else {
            return new p325.Array(p340)
          }
        }
      }
      return this
    }
  __Cpn.prototype.initLocation =
    __Cpn.prototype.initLocation ||
    function (p341, p342) {
      this.WorkerLocation = class {
        static create() {
          return new this()
        }
        get hash() {
          return p341.location.hash
        }
        get host() {
          return this.qt().host()
        }
        get hostname() {
          return this.qt().hostname()
        }
        get href() {
          return this.ri()
        }
        get pathname() {
          return p341.location.pathname
        }
        get port() {
          return this.qt().port()
        }
        get protocol() {
          return this.qt().protocol() + ':'
        }
        get search() {
          return this.qt().search()
        }
        get origin() {
          return this.qt().origin()
        }
        toString() {
          return this.ri()
        }
        ri(p343 = false) {
          var v282 = p342.Uri.create(p341.location.href)
          if (!p343 || v282.gt(true)) {
            return v282.p()
          } else {
            return p341.location.href
          }
        }
        qt(p344 = false) {
          return p342.URI(this.ri(p344))
        }
        At() {
          return this.qt(true)
        }
      }
      this.Location = class extends this.WorkerLocation {
        static create(p345, p346 = false) {
          return new this(p345, p346)
        }
        constructor(p347, p348 = false) {
          super()
          this.proxyUrl = p347
          this.passiveMode = p348
          p341.addEventListener(
            'hashchange',
            () => {
              this.ni()
            },
            true
          )
          p341.addEventListener(
            'popstate',
            () => {
              this.ni()
            },
            true
          )
        }
        get hash() {
          return super.hash
        }
        set hash(p349) {
          p341.location.hash = p349
        }
        get host() {
          return super.host
        }
        set host(p350) {
          this.assign(this.qt().host(p350))
        }
        get hostname() {
          return super.hostname
        }
        set hostname(p351) {
          this.assign(this.qt().hostname(p351))
        }
        get href() {
          return super.href
        }
        set href(p352) {
          this.assign(p352)
        }
        get pathname() {
          return super.pathname
        }
        set pathname(p353) {
          this.assign(this.qt().pathname(p353))
        }
        get port() {
          return super.port
        }
        set port(p354) {
          this.assign(this.qt().port(p354))
        }
        get protocol() {
          return super.protocol
        }
        set protocol(p355) {
          this.assign(this.qt().protocol(p355.replace(/:$/g, '')))
        }
        get search() {
          return super.search
        }
        set search(p356) {
          this.assign(this.qt().search(p356))
        }
        get username() {
          return this.qt().username()
        }
        set username(p357) {}
        get password() {
          return this.qt().password()
        }
        set password(p358) {}
        assign(p359) {
          p341.location.assign(this.passiveMode ? p359 + '' : p342.Uri.create(p359).tt())
        }
        reload(p360) {
          p341.location.reload(p360)
        }
        replace(p361) {
          p341.location.replace(this.passiveMode ? p361 + '' : p342.Uri.create(p361).tt())
        }
        ni() {
          var v283 = p341.document.querySelector('base[' + p342.R + ']')
          if (v283) {
            v283.setAttribute('href', this.ri())
          }
          this.si()
          return this
        }
        si() {}
        At() {
          var v284 = p341.document.querySelector('base')
          if (v284) {
            try {
              var v285 = p342.Element.create(v284).Pt('href')
            } catch (_0x4fd172) {}
            if (v285) {
              return p342.URI(v285).absoluteTo(this.qt())
            }
          }
          let v286 = this.ri()
          if (!p342.Uri.create(v286).wt() && this.proxyUrl) {
            v286 = p342.Uri.create(this.proxyUrl).p()
          }
          return p342.URI(v286)
        }
      }
      return this
    }
  __Cpn.prototype.initUi =
    __Cpn.prototype.initUi ||
    function (p362, p363) {
      this.Ui = class {
        constructor() {
          this.hi = 10000
          this.oi = 1000
          this.ai = 600000
          this.ui = 600000
          this.ci = 5
          this.fi = null
          this.li = null
          this.di = null
          this.pi = null
          this.vi = null
          this.yi = null
          this.mi = null
          this.gi = false
          this.wi = false
          this.bi = false
          this._i = false
          this.$i = null
          this.xi = false
          this.Ai = p363.adsJson ? JSON.parse(p363.adsJson) : null
        }
        ki() {
          if (p363.Z()) {
            this.Ei()
          } else if (/compl|inter|loaded/.test(document.readyState) && this.isUiInjectable()) {
            this.Ci()
          } else {
            p362.document.addEventListener(
              'DOMContentLoaded',
              () => {
                if (this.isUiInjectable()) {
                  this.Ci()
                }
              },
              true
            )
          }
          return this
        }
        isUiInjectable() {
          return p362.document.body !== null
        }
        Ci() {
          return this
        }
        Ei() {
          return this
        }
        Si(p364) {
          console.log('Ads: ' + p363.showAds)
          console.log('Ad codes: ' + !!p363.adsJson)
          console.log('Adblock: ' + p364)
          p362.document.body.insertAdjacentHTML('afterbegin', p363.modal)
          if (p363.header) {
            if (p363.header) {
              this.Bi(p364)
            }
            ;[...document.querySelectorAll('#__cpsHeader a')].forEach((p365) => {
              p365.addEventListener(
                'click',
                function (p366) {
                  if (this.target === '_blank') {
                    p362.open(this.href, '_blank').focus()
                  } else {
                    p362.location.href = this.href
                  }
                  p366.stopImmediatePropagation()
                  p366.preventDefault()
                },
                true
              )
            })
          }
          return this
        }
        Bi(p367) {
          p362.document.body.insertAdjacentHTML('afterbegin', p363.header)
          this.fi =
            p362.document.getElementById('__cpsHeader') ||
            p362.document.getElementById('__cpsExtensionHeader')
          this.li = p362.document.getElementById('__cpsHeaderBody')
          this.di = p362.document.getElementById('__cpsHeaderTab')
          this.pi = p362.document.getElementById('__cpsUrl')
          this.vi = p362.document.getElementById('__cpsSubmitButton')
          this.yi = p362.document.getElementById('__cpsPermalinkButton')
          this.mi = p362.document.getElementById('__cpsPermalinkContainer')
          if (p363.fixedHeader) {
            this.Pi()
          } else {
            this.Ti()
          }
          if (this.yi && this.mi) {
            this.yi.addEventListener(
              'click',
              (p368) => {
                function f40() {
                  p363.H('Unable to copy permalink')
                }
                this.mi.value = p363.M().toString()
                this.mi.select()
                try {
                  if (document.execCommand('copy')) {
                    p362.alert(
                      'The permalink was copied into your clipboard.\nTime to live for the permalink is 3 hours.'
                    )
                  } else {
                    f40()
                  }
                } catch (_0x40376a) {
                  f40()
                }
                p368.stopImmediatePropagation()
              },
              true
            )
          }
          if (this.vi && this.pi) {
            this.vi.addEventListener(
              'click',
              (p369) => {
                var v288 = this.pi.value
                var v288 = p363.Uri.create(v288)
                p363.u.href = v288.xt()
                p369.stopImmediatePropagation()
              },
              true
            )
            this.pi.addEventListener(
              'keyup',
              (p370) => {
                if (p370.keyCode === 13) {
                  this.vi.click()
                }
                p370.stopImmediatePropagation()
              },
              true
            )
          }
          return this
        }
        Ti() {
          this.Ri()
          this.Fi()
          p362.setTimeout(() => {
            if (!this.xi) {
              p363.W('Header close assigned by timeout')
              p362.document.addEventListener(
                'mousedown',
                (p371) => {
                  if (p363.ut(p371) && !this.fi.contains(p371.target)) {
                    this.Oi()
                  }
                },
                true
              )
              this.xi = true
            }
          }, this.oi)
          return this
        }
        Pi() {
          var v289
          var v290 = p362.document.querySelector('html')
          var v291 = p362.document.querySelector('body')
          var v292 = {
            'overscroll-behavior-y': 'auto',
            'padding-top': '0',
            'padding-left': '0',
            'padding-right': '0',
            'margin-top': '0',
            'margin-left': '0',
            'margin-right': '0',
            width: '100%',
            'min-width': '100%',
            top: '0',
            left: '0',
            position: 'absolute',
            'min-height': '100%'
          }
          for (v289 in v292) {
            v290.style.setProperty(v289, v292[v289], 'important')
            v291.style.setProperty(v289, v292[v289], 'important')
          }
          v290.style.setProperty('overflow', 'visible', 'important')
          v291.style.setProperty('overflow-y', 'auto', 'important')
          this.fi.style.setProperty('position', 'absolute', 'important')
          var v293 = 0
          var v294 = []
          var v295 = null
          var v296 = this.Di(this.fi)
          this.fi.style.setProperty('transform', 'translateY(-' + v296 + 'px)', 'important')
          p362.document.body.style.setProperty('top', v296 + 'px', 'important')
          var vF5 = () => {
            var v297
            var v298 = []
            for (v297 of document.getElementsByTagName('*')) {
              var v299 = v297.getAttribute('data---cpt')
              var v300 = p362.getComputedStyle(v297)
              if ((v300.position === 'fixed' && v299 !== '0') || v299 === '1') {
                if (!(v299 = v297.style.getPropertyValue('top')) || v299 === 'auto') {
                  v299 = v300.top
                  p362.document.body.style.setProperty('top', v296 + 1 + 'px', 'important')
                  if (v299 !== v300.top) {
                    v297.style.setProperty('top', this.Ii(v299) - v296 + 'px')
                  }
                  p362.document.body.style.setProperty('top', v296 + 'px', 'important')
                }
                v298.push(v297)
              }
            }
            v294 = v298
          }
          vF5()
          var vF6 = (p372 = false) => {
            var v301 = (v301 = v296 - this.ji()) > 0 ? v301 : 0
            if (p372 || v301 !== v295) {
              for (var v302 of v294) {
                if (v301 && p362.getComputedStyle(v302).position === 'fixed') {
                  v302.style.setProperty('transform', 'translateY(' + v301 + 'px)', 'important')
                  v302.style.setProperty('transition', 'none', 'important')
                  v302.style.setProperty('transform-origin', '0 0 0', 'important')
                  v302.setAttribute('data---cpt', '1')
                } else if (v302.getAttribute('data---cpt') === '1') {
                  v302.style.removeProperty('transform')
                  v302.style.removeProperty('transform-origin')
                  v302.removeAttribute('data---cpt')
                }
              }
              v295 = v301
            }
          }
          vF6()
          p362.addEventListener('scroll', () => {
            if (v293) {
              vF6()
              v293 = 0
            }
            v293++
          })
          p362.setInterval(() => {
            vF5()
            vF6(true)
          }, 1000)
          return this
        }
        Ri() {
          let vF7 = (p373) => {
            if (!this.fi.style.transition) {
              if (!this.ji()) {
                if (this.gi) {
                  this.Hi('show', p373)
                }
                this.wi = false
              } else {
                if (!this.gi) {
                  this.Hi('hide', p373)
                }
                this.bi = false
              }
              this.Mi(p373)
            }
          }
          vF7(false)
          p362.setTimeout(() => {
            p362.setInterval(() => {
              vF7(true)
            }, 500)
          }, 500)
          p362.addEventListener(
            'scroll',
            () => {
              vF7(true)
            },
            true
          )
          return this
        }
        Fi() {
          if (this.$i) {
            p362.clearTimeout(this.$i)
            this.$i = null
          }
          this.$i = p362.setTimeout(() => {
            this.Oi()
          }, this.hi)
          return this
        }
        Li() {
          if (!this._i) {
            this.fi.style.transition = null
            this.wi = true
            this.bi = false
            this.Hi('show')
          }
          return this
        }
        Oi() {
          if (!this._i) {
            this.fi.style.transition = null
            this.bi = true
            this.wi = false
            this.Hi('hide')
          }
          return this
        }
        ji() {
          return (
            p362.scrollY ||
            p362.pageYOffset ||
            p362.document.documentElement.scrollTop ||
            document.body.scrollTop
          )
        }
        Di(p374) {
          return this.Ii(p362.getComputedStyle(p374).height)
        }
        Ii(p375) {
          return Number(p375.replace(/px$/i, ''))
        }
        Ni(p376) {
          p376 = p376.replace(/^translateY\(([^)]+)\)$/i, '$1')
          return this.Ii(p376)
        }
        Wi(p377, p378, p379 = false) {
          if (this.Ni(p377.style.transform) !== p378) {
            if (p379) {
              p377.style.visibility = 'visible'
              p377.style.transition = '0.2s'
              p362.setTimeout(() => {
                p377.dispatchEvent(new p362.Event('transitionend'))
              }, 200)
            }
            p377.style.transform = 'translateY(' + p378 + 'px)'
          }
          return this
        }
        zi(p380, p381, p382, p383, p384, p385 = {}, p386 = () => {}) {
          var v303
          var v304 = new URLSearchParams()
          v304.append(p363.k, '1')
          v304.append('v', '2')
          v304.append('en', p382)
          v304.append('tid', p380)
          v304.append('cid', p381)
          v304.append('dh', p383)
          v304.append('ul', p362.navigator.language || p362.navigator.userLanguage)
          v304.append('dt', p362.document.title)
          v304.append('sr', p362.screen.width + 'x' + p362.screen.height)
          v304.append('dl', p384)
          v304.append('seg', '1')
          v304.append('ir', '0')
          v304.append('_ee', '1')
          v304.append('_p', Math.floor(Math.random() * 2147483648) + '')
          if (p362.document.referrer) {
            v304.append('dr', p362.document.referrer)
          }
          for (v303 in p385) {
            v304.append(v303, p385[v303])
          }
          ;('__cpOriginalFetch' in p362 ? p362.__cpOriginalFetch : p362.fetch)(
            'https://www.google-analytics.com/g/collect?' + v304,
            {
              method: 'POST'
            }
          )
            .then(function (p387) {
              if (p387.status >= 200 && p387.status < 300) {
                p363.W('GA hit: ' + p381 + ', ' + p384)
              } else {
                p363.H('GA request failed, status: ' + p387.status)
              }
              p386()
            })
            .catch(function (p388) {
              p363.H('GA request failed, message: ' + p388.message)
              p386()
            })
          return this
        }
        Gi(p389, p390) {
          p390()
          return this
        }
        Zi(p391, p392, p393, p394, p395 = false, p396 = null) {
          var v305
          if (p363.showAds && !p391 && this.Ai && !p392()) {
            p363.Popup.make(p362.location.href, {
              newTab: true,
              beforeOpen: (p397, p398) => {
                if (p395) {
                  this.qi('Launching CroxyProxy advertisement...')
                  p362.setTimeout(() => {
                    p398(v305(p397))
                  }, 1500)
                } else {
                  p398(v305(p397))
                }
              },
              afterOpen: () => {
                var v308 = p363.URI(p363.frontOrigin).domain(true)
                var v308 = v308 in this.Ai ? this.Ai[v308] : this.Ai.default
                var v308 = v308[p394() % v308.length]
                let v309 = p363.ct(v308)
                this.Gi(p363.URI(v309).hostname(), () => {
                  console.log('Navigated to ', v309)
                  p362.location.href = v309
                })
              },
              blur: !(v305 = (p399) => {
                p393()
                let v310
                p399 = p399.target || p399.srcElement
                v310 =
                  p399 instanceof HTMLAnchorElement && p399.hasAttribute('href')
                    ? p399.getAttribute('href')
                    : p362.location.href
                if (p396) {
                  return p396(v310)
                } else {
                  return v310
                }
              })
            })
          }
          return this
        }
        qi(p400, p401 = null, p402 = null, p403 = {}) {
          let v311 = document.getElementById('__cpsModal')
          let v312 = document.getElementById('__cpsModalContent')
          v312.innerHTML = p400
          if (!(v311.style.display = 'block') !== p403.closingOverlay) {
            v311.addEventListener(
              'click',
              (p404) => {
                if (!v312.contains(p404.target)) {
                  v311.style.display = 'none'
                  p404.stopImmediatePropagation()
                  p404.preventDefault()
                }
              },
              true
            )
          }
          p400 = document.getElementById('__cpsButtonOk')
          if (p400) {
            p400.addEventListener(
              'click',
              (p405) => {
                v311.style.display = 'none'
                if (p401) {
                  p401()
                }
                p405.stopImmediatePropagation()
                p405.preventDefault()
              },
              true
            )
          }
          p400 = document.getElementById('__cpsButtonCancel')
          if (p400) {
            p400.addEventListener(
              'click',
              (p406) => {
                v311.style.display = 'none'
                if (p402) {
                  p402()
                }
                p406.stopImmediatePropagation()
                p406.preventDefault()
              },
              true
            )
          }
          if (p403.wide) {
            v312.classList.add('__cpsModalContentWide')
          }
          return this
        }
        Hi(p407, p408 = 0) {}
        Mi(p409 = 0) {}
      }
      this.ProxyUi = class extends this.Ui {
        static create() {
          return new this()
        }
        constructor() {
          super()
          this.Vi = 9999
          this.Qi = p363.C + 'StatSampleNum'
          this.Yi = p363.C + 'HeaderTabClosed'
          this.Xi = null
        }
        Ci() {
          this.Ji((p410) => {
            super.Si(p410)
            var v313 = null
            p363.u.si = (p411 = true) => {
              var v314 = p363.u.ri()
              if (v313 !== v314) {
                v313 = v314
                if (this.pi) {
                  this.pi.value = p362.decodeURIComponent(v314)
                }
                this.Ki({
                  'ep.adblock_status': p410 ? 'enabled' : 'disabled',
                  'ep.navigation_type': p411 ? 'hash' : 'location',
                  'ep.server_hostname': p363.I
                })
              }
            }
            p363.u.si(false)
            var v315 = p363.C + 'PopShown'
            let v316 = p363.C + 'PopCount'
            this.Zi(
              p410,
              () => +p363.Cookie.ei(p362.document.__cpOriginalCookie, v315, 0),
              () => {
                var v317 = +p363.Cookie.ei(p362.document.__cpOriginalCookie, v316, 0)
                var v318 = new Date()
                v318.setTime(new Date().getTime() + this.ai + v317 * this.ui)
                p362.document.__cpOriginalCookie = p363.Cookie.Qt({
                  name: v315,
                  value: 1,
                  domain: p362.location.host,
                  expires: v318.toUTCString(),
                  path: '/',
                  secure: true,
                  samesite: 'none',
                  priority: 'high',
                  partitioned: true
                })
              },
              () => {
                var v319 = +p363.Cookie.ei(p362.document.__cpOriginalCookie, v316, 0)
                var v320 = v319 >= this.ci - 1 ? 0 : 1 + v319
                p362.document.__cpOriginalCookie = p363.Cookie.Qt({
                  name: v316,
                  value: v320,
                  domain: p362.location.host,
                  path: '/',
                  secure: true,
                  samesite: 'none',
                  priority: 'high',
                  partitioned: true
                })
                return v319
              },
              false,
              (p412) => p363.Uri.create(p412).tt()
            )
            this.te()
          })
          return this
        }
        Ei() {
          if (p363.frontOrigin) {
            let v321 = null
            let v322 = null
            let v323 = p363.URI(p363.frontOrigin)
            let v324 = v323.origin()
            p362.addEventListener(
              'message',
              (p413) => {
                if (p413.__cpOriginalOrigin === v324) {
                  switch (p413.__cpOriginalData.type) {
                    case 'proxyLocation':
                      if (v321 === null) {
                        this.Ji((p414) => {
                          v321 = p414
                          p363.u.si = (p415 = true) => {
                            var v325 = p363.u.ri()
                            if (v322 !== v325) {
                              v322 = v325
                              this.Ki({
                                'ep.adblock_status': v321 ? 'enabled' : 'disabled',
                                'ep.navigation_type': p415 ? 'hash' : 'location',
                                'ep.server_hostname': p363.I
                              })
                            }
                          }
                          p363.u.si(false)
                        })
                      }
                      p413.source.postMessage(
                        {
                          type: 'proxyLocation',
                          url: v322 || p363.u.ri()
                        },
                        v323.origin()
                      )
                      break
                    case 'proxyForward':
                      p362.history.forward()
                      break
                    case 'proxyBackward':
                      p362.history.back()
                  }
                  p413.stopImmediatePropagation()
                  p413.stopPropagation()
                }
              },
              true
            )
          }
          return this
        }
        te() {
          if (p363.isProxyHost) {
            let v326 = '__cpcTermsAccepted'
            if (!p363.Cookie.ei(p362.document.__cpOriginalCookie, v326)) {
              this.qi(
                '<iframe __cpp="1" class="__cpsInfoFrame" src="' +
                  p362.location.origin +
                  '/__cpp.php?page=terms&__cpo=1"></iframe><button __cpp="1" id="__cpsButtonOk" class="__cpsButton __cpsMainButton">I agree</button>',
                () => {
                  var v327 = new Date()
                  v327.setTime(new Date().getTime() + 2419200000)
                  p362.document.__cpOriginalCookie = p363.Cookie.Qt({
                    name: v326,
                    value: 1,
                    domain: p362.location.host,
                    expires: v327.toUTCString(),
                    path: '/',
                    secure: true,
                    samesite: 'none',
                    priority: 'high',
                    partitioned: true
                  })
                },
                null,
                {
                  wide: true,
                  closingOverlay: false
                }
              )
            }
          }
          return this
        }
        Bi(p416) {
          super.Bi(p416)
          this.fi.addEventListener(
            'transitionend',
            () => {
              this.fi.style.transition = null
              if (this._i || this.gi || this.bi) {
                this.li.style.visibility = 'hidden'
              }
            },
            true
          )
          if (+p363.Cookie.ei(p362.document.__cpOriginalCookie, this.Yi, 0)) {
            this.Hi('close', false)
          }
          if (this.di) {
            this.di.addEventListener(
              'click',
              (p417) => {
                if (this.gi && !this._i) {
                  this.Li()
                } else {
                  this.Hi(this._i ? 'open' : 'close')
                  p362.document.__cpOriginalCookie = p363.Cookie.Qt({
                    name: this.Yi,
                    value: +this._i,
                    domain: p362.location.host,
                    path: '/',
                    secure: true,
                    samesite: 'none',
                    priority: 'high',
                    partitioned: true
                  })
                }
                p417.stopImmediatePropagation()
              },
              true
            )
          }
          return this
        }
        Hi(p418, p419 = true) {
          if (p419 && this.fi.style.transition) {
            return this
          }
          switch (p418) {
            case 'open':
              if (!this._i) {
                return this
              }
              this.Fi()
              this._i = false
              this.gi = false
              this.wi = true
              this.bi = false
              this.li.style.visibility = 'visible'
              break
            case 'close':
              if (this._i) {
                return this
              }
              this._i = true
              this.gi = false
              this.wi = false
              this.bi = false
              if (!p419) {
                this.li.style.visibility = 'hidden'
              }
              break
            case 'show':
              if (this._i || !this.gi || this.bi) {
                return this
              }
              this.Fi()
              this.gi = false
              this.li.style.visibility = 'visible'
              break
            case 'hide':
              if (this._i || this.gi || this.wi) {
                return this
              }
              this.gi = true
              if (!p419) {
                this.li.style.visibility = 'hidden'
              }
          }
          return this.Mi(p419)
        }
        Mi(p420 = false) {
          var v328
          if (this._i) {
            v328 = -this.Di(this.li)
          } else {
            if (this.gi || this.bi) {
              v328 = -this.Di(this.li)
            }
            if (!this.gi || !!this.wi) {
              v328 = 0
            }
          }
          return this.Wi(this.fi, v328, p420)
        }
        Ki(p421 = {}) {
          var v329
          if (p363.analyticsUid && p363.analyticsTrackingId) {
            if (!(v329 = +p363.Cookie.ei(p362.document.__cpOriginalCookie, this.Qi, 0))) {
              this.zi(
                p363.analyticsTrackingId,
                p363.analyticsUid,
                'page_view',
                p363.u.hostname,
                p363.u.href,
                p421
              )
            }
            v329 = v329 >= this.Vi - 1 ? 0 : 1 + v329
            ;(p421 = new Date()).setTime(new Date().getTime() + 21600000)
            p362.document.__cpOriginalCookie = p363.Cookie.Qt({
              name: this.Qi,
              value: v329,
              domain: p362.location.host,
              path: '/',
              secure: true,
              samesite: 'none',
              expires: p421.toUTCString(),
              priority: 'high',
              partitioned: true
            })
          }
          return this
        }
        Gi(p422, p423) {
          if (p363.analyticsUid && p363.analyticsTrackingId) {
            return this.zi(
              'G-0GESC8GM3Q',
              p363.analyticsUid,
              'pop_impression_' + p422.replace('.', '_'),
              p363.u.hostname,
              p363.u.href,
              {
                'ep.direct_link_host': p422
              },
              p423
            )
          } else {
            p423()
            return this
          }
        }
        Ji(p424) {
          var v330
          if (this.Xi === false || this.Xi === true) {
            p424(this.Xi)
          } else {
            ;(v330 = new p363.FAB({
              checkOnLoad: false,
              resetOnEnd: true
            })).onNotDetected(() => {
              this.Xi = false
              p363.W('Adblock not detected')
              p424(false)
            })
            v330.onDetected(() => {
              this.Xi = true
              p363.W('Adblock detected')
              p424(true)
            })
            v330.check()
          }
          return this
        }
      }
      this.ExtensionUi = class extends this.Ui {
        static create() {
          return new this()
        }
        constructor() {
          super()
          this.ie = 'uniqId'
          this.ee = 'popShowTime'
          this.re = 'popCount'
          this.ne = 'headerClosed'
        }
        Ci() {
          if (p363.style) {
            p362.document.head.insertAdjacentHTML('afterbegin', p363.style)
          }
          super.Si(false)
          var v331 = p362.location.href
          p362.setInterval(() => {
            if (v331 !== p362.location.href) {
              v331 = p362.location.href
              this.se({
                'ep.navigation_type': 'hash'
              })
            }
          }, 200)
          this.se({
            'ep.navigation_type': 'location'
          })
          p362.chrome.storage.sync.get(this.ee, (p425) => {
            p362.chrome.storage.sync.get(this.re, (p426) => {
              this.Zi(
                false,
                () => {
                  if (this.ee in p425) {
                    var v332 = (v332 = p426[this.re]) ? +v332 : 0
                    var v333 = Date.now()
                    var v334 = p425[this.ee]
                    if (v333 < v334 && v334 <= v333 + this.ai + v332 * this.ui) {
                      return true
                    }
                  }
                  return false
                },
                () => {
                  var v335 = (v335 = p426[this.re]) ? +v335 : 0
                  var v336 = {}
                  v336[this.ee] = Date.now() + this.ai + v335 * this.ui
                  p362.chrome.storage.sync.set(v336)
                },
                () => {
                  var v337 = p426[this.re]
                  p426[this.re] = (v337 = v337 ? +v337 : 0) >= this.ci - 1 ? 0 : 1 + v337
                  p362.chrome.storage.sync.set(p426)
                  return v337
                },
                true
              )
            })
          })
          return this
        }
        Bi(p427) {
          p362.chrome.storage.sync.get(this.ne, (p428) => {
            super.Bi(p427)
            this.fi.addEventListener(
              'transitionend',
              () => {
                this.fi.style.transition = null
                if (this._i || this.gi || this.bi) {
                  this.li.style.visibility = 'hidden'
                }
              },
              true
            )
            if (p428[this.ne]) {
              this.Hi('close', false)
            }
            if (this.di) {
              this.di.addEventListener(
                'click',
                (p429) => {
                  var v338
                  if (this.gi && !this._i) {
                    this.Li()
                  } else {
                    this.Hi(this._i ? 'open' : 'close')
                    ;(v338 = new p362.Object())[this.ne] = this._i
                    p362.chrome.storage.sync.set(v338)
                  }
                  p429.stopImmediatePropagation()
                },
                true
              )
            }
          })
        }
        Hi(p430, p431 = true) {
          if (p431 && this.fi.style.transition) {
            return this
          }
          switch (p430) {
            case 'open':
              if (!this._i) {
                return this
              }
              this.Fi()
              this._i = false
              this.gi = false
              this.wi = true
              this.bi = false
              this.li.style.visibility = 'visible'
              break
            case 'close':
              if (this._i) {
                return this
              }
              this._i = true
              this.gi = true
              this.wi = false
              this.bi = false
              if (!p431) {
                this.li.style.visibility = 'hidden'
              }
              break
            case 'show':
              if (!this.gi || this._i || this.bi) {
                return this
              }
              this.Fi()
              this.gi = false
              this.li.style.visibility = 'visible'
              break
            case 'hide':
              if (this.gi || this._i || this.wi) {
                return this
              }
              this.gi = true
              if (!p431) {
                this.li.style.visibility = 'hidden'
              }
          }
          return this.Mi(p431)
        }
        Mi(p432 = false) {
          var v339
          if (this._i) {
            v339 = -this.Di(this.li)
          } else {
            if (this.gi || this.bi) {
              v339 = -this.Di(this.li)
            }
            if (!this.gi || !!this.wi) {
              v339 = 0
            }
          }
          return this.Wi(this.fi, v339, p432)
        }
        se(p433 = {}) {
          return this.he((p434) =>
            this.zi(
              'G-0WD9HNFY6Z',
              p434,
              'page_view',
              p362.location.hostname,
              p362.location.href,
              p433
            )
          )
        }
        Gi(p435, p436) {
          return this.he((p437) =>
            this.zi(
              'G-0WD9HNFY6Z',
              p437,
              'pop_impression_' + p435.replace('.', '_'),
              p362.location.hostname,
              p362.location.href,
              {
                'ep.direct_link_host': p435
              },
              p436
            )
          )
        }
        he(p438) {
          p362.chrome.storage.sync.get(this.ie, (p439) => {
            var v340 = ''
            if (this.ie in p439 && p439[this.ie]) {
              v340 = p439[this.ie]
            } else {
              p439 = {}
              v340 = p363.rt()
              p439[this.ie] = v340
              p362.chrome.storage.sync.set(p439)
            }
            return p438(v340)
          })
          return this
        }
      }
      return this
    }
  __Cpn.prototype.initAd =
    __Cpn.prototype.initAd ||
    function (p440, p441) {
      this.Ad = class {
        static create(p442 = [], p443 = []) {
          return new this(p442, p443)
        }
        static oe(p444, p445 = false, p446 = false) {
          return (
            '<iframe sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-scripts allow-same-origin" frameborder="0" scrolling="no" id="' +
            p444 +
            '" __cpp="1" src="' +
            ((p441.cdnOrigin || p441.frontOrigin) +
              '/zapper?__cpo=1&h=' +
              p441.st(p441.M().hostname()) +
              '&a=' +
              +p446 +
              '&m=' +
              +p445) +
            '"></iframe>'
          )
        }
        static ae(p447, p448) {
          var v342 = p440.document.getElementById(p447)
          if (!v342) {
            throw new Error('No ad code container #' + p447 + ' found')
          }
          var v342 = p440.document.createNodeIterator(v342, NodeFilter.SHOW_COMMENT).nextNode()
          if (v342) {
            v342 = v342.textContent
            v342 = new Blob(
              [
                '<!DOCTYPE html><html><head><style>html, body { width: 100%; height: 100%; padding: 0; margin: 0; } body { padding: 5px 0 0 5px; width: calc(100% - 5px); }</style></head><body>' +
                  v342 +
                  '</body></html>'
              ],
              {
                type: 'text/html'
              }
            )
            return (
              '<iframe scrolling="no" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-scripts allow-same-origin" id="' +
              p448 +
              '" frameborder="0" __cpp="1" src="' +
              URL.createObjectURL(v342) +
              '"></iframe>'
            )
          }
          throw new Error('No ad code comment in #' + p447)
        }
        constructor(p449 = [], p450 = []) {
          this.ue = 0
          this.ce = p440.Array()
          this.fe = p449
          this.le = p450
          this.de = 0
          this.pe = null
        }
        ve(p451, p452) {
          if (!p441.Z() && !!p441.showAds && !p452) {
            this.ye(p451)
            if (this.ue) {
              p440.setInterval(() => {
                this.me(p451)
              }, this.ue)
            }
          }
          return this
        }
        ye(p453) {
          var v343 = this.ge()
          if (this.pe !== v343) {
            this.pe = v343
            this.ce = this.we(v343 ? this.le : this.fe)
            this.me(p453)
          }
          return this
        }
        me(p454) {
          var v344
          if (this.ce.length && (p454 = p440.document.getElementById(p454))) {
            v344 = this.ce[this.de]
            this.de = this.ce[this.de + 1] ? this.de + 1 : 0
            p454.innerHTML = ''
            this.be(p454, 'beforeend', v344)
          }
          return this
        }
        be(p455, p456, p457) {
          var v345
          var v346 = p440.document.createElement('div')
          v346.innerHTML = p457
          for (v345 of v346.childNodes) {
            if (v345 instanceof p440.Element) {
              if (
                v345 instanceof p440.HTMLScriptElement ||
                v345 instanceof p440.HTMLIFrameElement
              ) {
                var v347 = p440.document.createElement(v345.tagName)
                for (var v348 = 0; v348 < v345.attributes.length; v348++) {
                  var v349 = v345.attributes[v348]
                  v347.setAttribute(v349.name, v349.value)
                }
                v347.appendChild(p440.document.createTextNode(v345.innerHTML))
                p455.insertAdjacentElement(p456, v347)
              } else {
                p455.insertAdjacentElement(p456, v345)
              }
            }
          }
          return this
        }
        we(p458) {
          for (var v350 = p458.length - 1; v350 > 0; v350--) {
            var v351 = p440.Math.floor(p440.Math.random() * (v350 + 1))
            var v352 = p458[v350]
            p458[v350] = p458[v351]
            p458[v351] = v352
          }
          return p458
        }
        _e() {
          if (p440.innerWidth > 0) {
            return p440.innerWidth
          } else {
            return p440.screen.width
          }
        }
        ge() {
          return this._e() < 750
        }
      }
      return this
    }
  __Cpn.prototype.initPopup =
    __Cpn.prototype.initPopup ||
    function (p459, p460) {
      this.Popup = function (p461, p462) {
        this.__construct(p461, p462)
      }
      var v353 = 0
      var v354 = false
      var v355 = p459.top !== p459.self ? top : p459.self
      var v356 = navigator.userAgent.toLowerCase()
      var v357 = /webkit/.test(v356)
      if (/mozilla/.test(v356)) {
        ;/(compatible|webkit)/.test(v356)
      }
      var v358 = /chrome/.test(v356)
      var v359 = /msie|trident\//.test(v356) && !/opera/.test(v356)
      var v360 = /firefox/.test(v356)
      if (/safari/.test(v356)) {
        ;/chrome/.test(v356)
      }
      ;/opera/.test(v356)
      var vParseInt = parseInt(
        v356.match(/(?:[^\s]+(?:ri|ox|me|ra)\/|trident\/.*?rv:)([\d]+)/i)[1],
        10
      )
      var v361 = {
        simulateClick: function (p463) {
          var v362 = p459.document.createElement('a')
          var v363 = p459.document.createEvent('MouseEvents')
          v362.href = p463 || 'data:text/html,<script>window.close();</script>;'
          p459.document.body.appendChild(v362)
          v363.initMouseEvent(
            'click',
            true,
            true,
            p459,
            0,
            0,
            0,
            0,
            0,
            true,
            false,
            false,
            true,
            0,
            null
          )
          v362.dispatchEvent(v363)
          v362.parentNode.removeChild(v362)
        },
        blur: function (p464) {
          try {
            p464.blur()
            p464.opener.window.focus()
            p459.focus()
            if (v360) {
              this.openCloseWindow(p464)
            } else if (v357) {
              if (!v358 || vParseInt < 41) {
                this.openCloseTab()
              }
            } else if (v359) {
              setTimeout(function () {
                p464.blur()
                p464.opener.window.focus()
                p459.focus()
              }, 1000)
            }
          } catch (_0x3302f2) {}
        },
        openCloseWindow: function (p465) {
          var v364 = p465.window.open('about:blank')
          v364.focus()
          v364.close()
          setTimeout(function () {
            try {
              ;(v364 = p465.window.open('about:blank')).focus()
              v364.close()
            } catch (_0x2f51f7) {}
          }, 1)
        },
        openCloseTab: function () {
          this.simulateClick()
        },
        detachEvent: function (p466, p467, p468) {
          if ((p468 = p468 || p459).removeEventListener) {
            return p468.removeEventListener(p466, p467)
          } else {
            return p468.detachEvent('on' + p466, p467)
          }
        },
        attachEvent: function (p469, p470, p471) {
          if ((p471 = p471 || p459).addEventListener) {
            return p471.addEventListener(p469, p470, true)
          } else {
            return p471.attachEvent('on' + p469, p470)
          }
        },
        mergeObject: function () {
          var v365
          var v366 = {}
          for (var v367 = 0; v367 < arguments.length; v367++) {
            for (v365 in arguments[v367]) {
              v366[v365] = arguments[v367][v365]
            }
          }
          return v366
        }
      }
      this.Popup.prototype = {
        defaultWindowOptions: {
          width: p459.screen.width,
          height: p459.screen.height,
          left: 0,
          top: 0,
          location: 1,
          toolbar: 1,
          status: 1,
          menubar: 1,
          scrollbars: 1,
          resizable: 1
        },
        defaultPopOptions: {
          cookieExpires: null,
          cookiePath: '/',
          newTab: true,
          blur: true,
          blurByAlert: false,
          chromeDelay: 500,
          beforeOpen: function (p472, p473) {
            p473()
          },
          afterOpen: function (p474) {}
        },
        __chromeNewWindowOptions: {
          scrollbars: 0
        },
        __construct: function (p475, p476) {
          this.url = p475
          this.name = '__cpcPopShown'
          this.executed = false
          this.setOptions(p476)
          this.register()
        },
        register: function () {
          function f41(p477) {
            if (p460.ut(p477) && vThis4.shouldExecute(p477)) {
              p477.preventDefault()
              p477.stopImmediatePropagation()
              v353 = new Date().getTime()
              vThis4.options.beforeOpen.call(undefined, p477, (p478) => {
                if (p478 instanceof String || typeof p478 == 'string') {
                  vThis4.url = p478
                }
                if (vThis4.options.newTab) {
                  if (v358 && vParseInt > 30 && vThis4.options.blur) {
                    p459.open('javascript:window.focus()', '_self', '')
                    v361.simulateClick(vThis4.url)
                    v368 = null
                  } else {
                    v368 = v355.window.open(vThis4.url, '_blank')
                    setTimeout(function () {
                      if (!v354 && vThis4.options.blurByAlert) {
                        v354 = true
                        alert()
                      }
                    }, 3)
                  }
                } else {
                  v368 = v355.window.open(vThis4.url, this.url, vThis4.getParams())
                }
                if (vThis4.options.blur) {
                  v361.blur(v368)
                }
                vThis4.options.afterOpen.call(undefined, p477)
                for (v369 of v370) {
                  v361.detachEvent(v371, f41, v369)
                }
              })
            }
          }
          var v368
          var v369
          var vThis4 = this
          var v370 = []
          var v371 = 'click'
          v361.attachEvent(v371, f41, p459)
          v370.push(p459)
          v361.attachEvent(v371, f41, p459.document)
          v370.push(p459.document)
        },
        shouldExecute: function (p479) {
          var v372
          var v373
          var v374
          return (
            (!v358 || !v353 || !(v353 + this.options.chromeDelay > new Date().getTime())) &&
            !((v372 = document.getElementById('__cpsModal')),
            (v373 = document.getElementById('__cpsHeader')),
            (v374 = document.getElementById('__cpsFooter')),
            p479.target &&
              p479.target instanceof Node &&
              ((v372 && v372.contains(p479.target)) ||
                (v373 && v373.contains(p479.target)) ||
                (v374 && v374.contains(p479.target))))
          )
        },
        setOptions: function (p480) {
          this.options = v361.mergeObject(
            this.defaultWindowOptions,
            this.defaultPopOptions,
            p480 || {}
          )
          if (!this.options.newTab && v358) {
            for (var v375 in this.__chromeNewWindowOptions) {
              this.options[v375] = this.__chromeNewWindowOptions[v375]
            }
          }
        },
        getParams: function () {
          var v376
          var v377 = ''
          for (v376 in this.options) {
            if (this.defaultWindowOptions[v376] !== undefined) {
              v377 += (v377 ? ',' : '') + v376 + '=' + this.options[v376]
            }
          }
          return v377
        }
      }
      this.Popup.make = function (p481, p482) {
        return new this(p481, p482)
      }
      return this
    }
  __Cpn.prototype.initWindow =
    __Cpn.prototype.initWindow ||
    function (p483, p484) {
      this.Window = class extends this.Scope {
        static create() {
          return new this()
        }
        h() {
          if (p483[p484._]) {
            p484.H('duplicated init')
          } else {
            p483[p484._] = '1'
            p484.PostedMessageOverride.create().h()
            this.Le()
              .Me()
              .He()
              .je()
              .Ie()
              .De()
              .Oe()
              .lt()
              .Ue()
              .Fe()
              .dt()
              .Re()
              .Te()
              .j()
              .Pe()
              .Be()
              .Se()
              .Ce()
              .Ee()
              .vt()
              .ke()
              .Ae()
              .tt()
              .xe()
              .$e()
            try {
              this.yt(p483.ServiceWorker.prototype, 'scriptURL', true)
            } catch (_0x396c14) {
              p484.m(_0x396c14)
            }
            try {
              this.yt(p483.HTMLMediaElement.prototype, 'currentSrc', true)
            } catch (_0x569c7f) {
              p484.m(_0x569c7f)
            }
            try {
              this.yt(
                new p483.Array(p483.Document.prototype, p483.HTMLDocument.prototype),
                'referrer',
                true,
                true
              )
            } catch (_0xcc4368) {
              p484.m(_0xcc4368)
            }
            this.We().Ne(p483.HTMLAnchorElement.prototype).Ne(p483.HTMLAreaElement.prototype)
            try {
              this.ze(p483.HTMLIFrameElement.prototype, 'src', false, true)
            } catch (_0x2cecc3) {
              p484.m(_0x2cecc3)
            }
            try {
              this.ze(p483.HTMLMediaElement.prototype, 'src', false, true)
            } catch (_0x4b078a) {
              p484.m(_0x4b078a)
            }
            try {
              this.ze(p483.HTMLSourceElement.prototype, 'src', false, true)
            } catch (_0xb43dcc) {
              p484.m(_0xb43dcc)
            }
            try {
              this.ze(p483.SVGUseElement.prototype, 'href', false, true)
            } catch (_0x4ccde9) {
              p484.m(_0x4ccde9)
            }
          }
          return this
        }
        Ae() {
          try {
            p484.et(
              p483,
              'WebSocket',
              function (p485, p486) {
                p486[0] =
                  'wss://' +
                  p483.location.host +
                  '/__cpw.php?u=' +
                  p484.B64.encode(p486[0]) +
                  '&o=' +
                  p484.B64.encode(p484.u.origin)
                return p485(p486)
              },
              true,
              false,
              true
            )
          } catch (_0x1aebc8) {
            p484.m(_0x1aebc8)
          }
          return this
        }
        xe() {
          p484.ProxyUi.create().ki()
          return this
        }
        tt() {
          try {
            p484.Element.create(p483.document.documentElement).Nt()
          } catch (_0x3bac9b) {
            p484.m(_0x3bac9b)
          }
          return this
        }
        Le() {
          if (p484.urlTimestamp && p484.sessionEndRedirectTtl && p484.sessionEndRedirectUrl) {
            var v378 = p484.sessionEndRedirectTtl
            let v379 = p484.urlTimestamp + v378
            p483.setInterval(() => {
              var v380
              var v381 = Math.floor(Date.now() / 1000)
              var v382 = v379 - v381
              if (
                v382 >= 0 &&
                ((v380 = p483.document.getElementById('__cpsTime')) &&
                  (v380.innerHTML =
                    ('0' + Math.floor(v382 / 60)).slice(-2) +
                    ':' +
                    ('0' + Math.floor(v382 % 60)).slice(-2)),
                v381 >= v379)
              ) {
                p483.location.href = p484.sessionEndRedirectUrl
              }
            }, 1000)
          }
          return this
        }
        Me() {
          for (var v383 of new p483.Array(p483.Window.prototype, p483.Document.prototype)) {
            p483.Object.defineProperty(
              v383,
              p484.F,
              new p483.Object({
                set: function (p487) {
                  p484.u.assign(p487)
                },
                get: function () {
                  return p484.u
                },
                configurable: false,
                enumerable: true
              })
            )
          }
          return this
        }
        He() {
          let vF8 = (p488) =>
            p483.navigator.serviceWorker.__cpOriginalGetRegistration
              ? p483.navigator.serviceWorker.__cpOriginalGetRegistration(p488)
              : p483.navigator.serviceWorker.getRegistration(p488)
          vF8(p484.j + '/')
            .then((p489) => {
              if (p489) {
                let v384 = false
                p483.setInterval(() => {
                  if (!v384) {
                    v384 = true
                    let v385 = p484.ft()
                    vF8(v385)
                      .then((p490) => {
                        var v386
                        if (p490) {
                          v384 = false
                        } else {
                          p484.W(
                            'sw unregister called, trying to re-install the default worker if needed for: ' +
                              v385
                          )
                          p490 = p484.serviceWorkerUrl
                          v386 = {
                            scope: v385
                          }
                          ;(p483.navigator.serviceWorker.__cpOriginalRegister
                            ? p483.navigator.serviceWorker.__cpOriginalRegister(p490, v386)
                            : p483.navigator.serviceWorker.register(p490, v386)
                          )
                            .then(() => {
                              p484.W(
                                'sw ' +
                                  p484.serviceWorkerUrl +
                                  ' successfully re-installed for: ' +
                                  v385
                              )
                              v384 = false
                            })
                            .catch((p491) => {
                              v384 = false
                              p484.H(p491)
                            })
                        }
                      })
                      .catch((p492) => {
                        v384 = false
                        p484.H(p492)
                      })
                  }
                }, 200)
              } else {
                p483.location.href = p484.M().toString()
              }
            })
            .catch((p493) => {
              p484.H(p493)
            })
          return this
        }
        je() {
          var v387
          if ((v387 = p483.MutationObserver)) {
            new v387((p494) => {
              for (var v388 of p494) {
                if (v388.type === 'childList' && v388.addedNodes.length) {
                  for (var v389 of v388.addedNodes) {
                    if (p484.V(v389)) {
                      try {
                        p484.Element.create(v389).Nt()
                      } catch (_0x26afec) {}
                    }
                  }
                }
              }
            }).observe(
              p483.document,
              new p483.Object({
                subtree: true,
                childList: true,
                attributes: false,
                characterData: false,
                attributeOldValue: false,
                characterDataOldValue: false
              })
            )
          }
          return this
        }
        Ie() {
          if (!p484.Z()) {
            p483.document.addEventListener(
              'keydown',
              function (p495) {
                if (
                  ((p495 = p495 || event).ctrlKey && p495.keyCode === 116) ||
                  (p495.shiftKey && p495.keyCode === 116) ||
                  (p495.ctrlKey && p495.shiftKey && p495.keyCode === 82) ||
                  (p495.metaKey && p495.shiftKey && p495.keyCode === 82)
                ) {
                  p483.location.reload(false)
                  p495.preventDefault()
                }
              },
              true
            )
          }
          return this
        }
        De() {
          try {
            p484.v(
              p483.HTMLBaseElement.prototype,
              'href',
              function (p496) {
                if (this.hasAttribute(this.__cpn.R)) {
                  return ''
                } else {
                  return p496()
                }
              },
              function (p497, p498) {
                p497(p498)
                try {
                  p484.Element.create(p483.document.documentElement).Rt()
                } catch (_0x249d62) {}
              }
            )
          } catch (_0x1360fa) {
            p484.m(_0x1360fa)
          }
          return this
        }
        Oe() {
          try {
            p484.et(
              p483,
              'open',
              function (p499, p500) {
                var v390 = p500[0]
                p500[0] = this.__cpn.Uri.create(v390).tt()
                return p499(p500)
              },
              true,
              true
            )
          } catch (_0x21c6bd) {
            p484.m(_0x21c6bd)
          }
          return this
        }
        Ue() {
          function f42(p501, p502) {
            var v391
            var v392 = p502.Uri.create(p501)
            return (p501 = v392.$t()
              ? ((v391 =
                  "importScripts('" +
                  p502.serviceWorkerUrl +
                  "'); try { importScripts.call(window, '" +
                  p501 +
                  "'); } catch (e) { if (e.name === 'NetworkError') {console.warn('CP Worker Error: ' + e.message + '. Trying the eval method...');fetch('" +
                  p501 +
                  "').then(function (response) { if (response.ok) { response.text().then((body) => { eval.call(window, body); }); }}).catch(function (e) {console.warn('CP Worker Error: ' + e.message + '. Failed to fetch blob script " +
                  p501 +
                  "');}); }}"),
                (v391 = new p502.D.Blob([v391], {
                  type: 'application/javascript'
                })),
                p502.D.URL.createObjectURL(v391))
              : v392.tt(
                  new p502.D.Object({
                    'parser:sw': 1
                  })
                ))
          }
          try {
            p484.et(p483.URL, 'revokeObjectURL', function (p503, p504) {
              p484.H('Blob object url is not revoked')
            })
          } catch (_0xd25490) {
            p484.m(_0xd25490)
          }
          try {
            p484.et(
              p483,
              'Worker',
              function (p505, p506) {
                p506[0] = f42.call(this, p506[0], p484)
                return p505(p506)
              },
              true,
              false,
              true
            )
          } catch (_0x385080) {
            p484.m(_0x385080)
          }
          try {
            p484.et(
              p483,
              'SharedWorker',
              function (p507, p508) {
                p508[0] = f42.call(this, p508[0], p484)
                return p507(p508)
              },
              true,
              false,
              true
            )
          } catch (_0x1a8fb0) {
            p484.m(_0x1a8fb0)
          }
          try {
            p484.et(p483.ServiceWorkerContainer.prototype, 'register', function (p509, p510) {
              p484.W('sw register called')
              return new this.__cpn.D.Promise((p511) => {
                this.__cpn.D.setTimeout(() => {
                  p510[0] = f42.call(this, p510[0], this.__cpn)
                  p510[1] = p510[1] || {}
                  p510[1].scope = this.__cpn.ft(p510[1].scope)
                  p484.W('base sw register called')
                  p511(p509(p510))
                }, 5000)
              })
            })
          } catch (_0x2930be) {
            p484.m(_0x2930be)
          }
          return this
        }
        Fe() {
          try {
            p484.et(
              p483.ServiceWorkerContainer.prototype,
              'getRegistration',
              function (p512, p513) {
                p513[0] = this.__cpn.ft(p513[0])
                return p512(p513)
              }
            )
          } catch (_0x5a685b) {
            p484.m(_0x5a685b)
          }
          return this
        }
        Re() {
          try {
            p484.et(p483.History.prototype, 'replaceState', function (p514, p515) {
              if (2 in p515) {
                p515[2] = this.__cpn.Uri.create(p515[2]).tt()
              }
              p514(p515)
              this.__cpn.u.ni()
            })
          } catch (_0x351381) {
            p484.m(_0x351381)
          }
          try {
            p484.et(p483.History.prototype, 'pushState', function (p516, p517) {
              if (2 in p517) {
                p517[2] = this.__cpn.Uri.create(p517[2]).tt()
              }
              p516(p517)
              this.__cpn.u.ni()
            })
          } catch (_0x4d7a56) {
            p484.m(_0x4d7a56)
          }
          return this
        }
        Te() {
          try {
            p484.et(p483.Navigator.prototype, 'registerProtocolHandler', function () {
              p484.H('No protocol handlers can be registered')
            })
          } catch (_0x2f0614) {
            p484.m(_0x2f0614)
          }
          return this
        }
        Pe() {
          try {
            p484.v(
              new p483.Array(p483.Document.prototype, p483.HTMLDocument.prototype),
              'cookie',
              function (p518) {
                return this.__cpn.Cookie.create(p518()).Xt()
              },
              function (p519, p520) {
                p520 = this.__cpn.Cookie.create(p520).zt()
                if (p520 !== null) {
                  p519(p520)
                }
              },
              true,
              true
            )
            p484.v(
              new p483.Array(p483.Document.prototype, p483.HTMLDocument.prototype),
              p484.J(p484.$, 'cookie'),
              function (p521) {
                return this.__cpn.Cookie.create(p521()).ti()
              },
              function (p522, p523) {
                p523 = this.__cpn.Cookie.create(p523).Yt()
                if (p523 !== null) {
                  p522(p523)
                }
              },
              false
            )
          } catch (_0x1145cb) {
            p484.m(_0x1145cb)
          }
          return this
        }
        Be() {
          try {
            p484.v(
              new p483.Array(p483.Document.prototype, p483.HTMLDocument.prototype),
              'domain',
              function () {
                if ('__cpDomain' in this) {
                  return this.__cpDomain
                } else {
                  return this.__cpn.u.qt().host()
                }
              },
              function (p524, p525) {
                this.__cpDomain = p525
              }
            )
          } catch (_0x23dce9) {
            p484.m(_0x23dce9)
          }
          return this
        }
        Se() {
          try {
            p484.v(
              new p483.Array(p483.HTMLScriptElement.prototype, p483.HTMLLinkElement.prototype),
              'integrity',
              function () {
                return null
              },
              function () {}
            )
          } catch (_0x583cfb) {
            p484.m(_0x583cfb)
          }
          return this
        }
        Ce() {
          try {
            p484.v(
              new p483.Array(p483.Document.prototype, p483.HTMLDocument.prototype),
              'URL',
              function () {
                return this.__cpn.u.href
              },
              function () {}
            )
          } catch (_0xcb0c0f) {
            p484.m(_0xcb0c0f)
          }
          return this
        }
        Ee() {
          try {
            p484.v(
              new p483.Array(p483.Document.prototype, p483.HTMLDocument.prototype),
              'documentURI',
              function () {
                return this.__cpn.u.href
              },
              function () {}
            )
          } catch (_0x38ed73) {
            p484.m(_0x38ed73)
          }
          return this
        }
        We() {
          var vF9 = (p526) => {
            try {
              var v393
              var v394
              var v395
              var v396 = p526.__cpn.Element.create(p526).tt()
              if (
                p526.method.toLowerCase() === 'get' &&
                (typeof (v393 = v396.Tt('action')
                  ? v396.Pt('action')
                  : p526.__cpn.D.location.href) != 'string' &&
                  p526.__cpn.L('Form action is incorrect'),
                (v394 = p526.__cpn.URI(v393).query(true)),
                p526.__cpn.k in v394) &&
                !p526.querySelector('input[name="' + p526.__cpn.k + '"]')
              ) {
                ;(v395 = p526.__cpn.D.document.createElement('input')).setAttribute(
                  'type',
                  'hidden'
                )
                v395.setAttribute('name', p526.__cpn.k)
                v395.setAttribute('value', v394[p526.__cpn.k])
                p526.appendChild(v395)
              }
            } catch (_0x74c7ce) {}
          }
          try {
            this.ze(p483.HTMLFormElement.prototype, 'action')
          } catch (_0x2b6f31) {
            p484.m(_0x2b6f31)
          }
          try {
            p484.et(p483.HTMLFormElement.prototype, 'submit', function (p527, p528) {
              vF9(this)
              return p527(p528)
            })
          } catch (_0x288af4) {
            p484.m(_0x288af4)
          }
          try {
            p484.et(p483.HTMLInputElement.prototype, 'click', function (p529, p530) {
              if (this.type === 'submit' && this.form) {
                vF9(this.form)
              }
              return p529(p530)
            })
          } catch (_0x43b8d7) {
            p484.m(_0x43b8d7)
          }
          p483.addEventListener(
            'submit',
            function (p531) {
              if (p531.target) {
                vF9(p531.target)
              }
            },
            true
          )
          return this
        }
        Ne(p532) {
          try {
            p484.et(p532, 'click', function (p533, p534) {
              try {
                this.__cpn.Element.create(this).tt()
              } catch (_0x160e13) {}
              return p533(p534)
            })
          } catch (_0x358764) {
            p484.m(_0x358764)
          }
          try {
            p484.et(p532, 'toString', function () {
              return this.href
            })
          } catch (_0x178937) {
            p484.m(_0x178937)
          }
          try {
            this.ze(p532, 'href')
          } catch (_0x2bdc61) {
            p484.m(_0x2bdc61)
          }
          try {
            p484.v(
              p532,
              'protocol',
              function () {
                var v397 = this.__cpn.URI(this.href).protocol()
                return v397 && v397 + ':'
              },
              function (p535, p536) {
                this.href = this.__cpn.URI(this.href).protocol(p536.replace(/:$/g, '')).toString()
              }
            )
          } catch (_0x505aac) {
            p484.m(_0x505aac)
          }
          try {
            p484.v(
              p532,
              'host',
              function () {
                return this.__cpn.URI(this.href).host()
              },
              function (p537, p538) {
                this.href = this.__cpn.URI(this.href).host(p538).toString()
              }
            )
          } catch (_0x144142) {
            p484.m(_0x144142)
          }
          try {
            p484.v(
              p532,
              'hostname',
              function () {
                return this.__cpn.URI(this.href).hostname()
              },
              function (p539, p540) {
                this.href = this.__cpn.URI(this.href).hostname(p540).toString()
              }
            )
          } catch (_0x4c29f3) {
            p484.m(_0x4c29f3)
          }
          try {
            p484.v(
              p532,
              'port',
              function () {
                return this.__cpn.URI(this.href).port()
              },
              function (p541, p542) {
                this.href = this.__cpn.URI(this.href).port(p542).toString()
              }
            )
          } catch (_0x4d90c5) {
            p484.m(_0x4d90c5)
          }
          try {
            p484.v(
              p532,
              'search',
              function () {
                return this.__cpn.URI(this.href).search()
              },
              function (p543, p544) {
                this.href = this.__cpn.URI(this.href).search(p544).toString()
              }
            )
          } catch (_0x3a8cc8) {
            p484.m(_0x3a8cc8)
          }
          try {
            p484.v(
              p532,
              'username',
              function () {
                return this.__cpn.URI(this.href).username()
              },
              function (p545, p546) {
                this.href = this.__cpn.URI(this.href).username(p546).toString()
              }
            )
          } catch (_0x287ee2) {
            p484.m(_0x287ee2)
          }
          try {
            p484.v(
              p532,
              'password',
              function () {
                return this.__cpn.URI(this.href).password()
              },
              function (p547, p548) {
                this.href = this.__cpn.URI(this.href).password(p548).toString()
              }
            )
          } catch (_0x75afae) {
            p484.m(_0x75afae)
          }
          try {
            p484.v(
              p532,
              'origin',
              function () {
                return this.__cpn.URI(this.href).origin()
              },
              function () {}
            )
          } catch (_0x4a5cf5) {
            p484.m(_0x4a5cf5)
          }
          return this
        }
        ke() {
          try {
            p484.et(
              p483.Node.prototype,
              'appendChild',
              function (p549, p550) {
                p549 = p549(p550)
                if (p484.V(p550[0]) && p484.Y(this)) {
                  try {
                    p484.Element.create(p550[0]).Nt()
                  } catch (_0x255a88) {}
                }
                return p549
              },
              true,
              false
            )
          } catch (_0x4fb82f) {
            p484.m(_0x4fb82f)
          }
          try {
            p484.et(
              p483.Node.prototype,
              'replaceChild',
              function (p551, p552) {
                p551 = p551(p552)
                if (p484.V(p552[0]) && p484.Y(this)) {
                  try {
                    p484.Element.create(p552[0]).Nt()
                  } catch (_0x2d5f93) {}
                }
                return p551
              },
              true,
              false
            )
          } catch (_0x2f4705) {
            p484.m(_0x2f4705)
          }
          try {
            p484.et(
              p483.Node.prototype,
              'insertBefore',
              function (p553, p554) {
                p553 = p553(p554)
                if (p484.V(p554[0]) && p484.Y(this)) {
                  try {
                    p484.Element.create(p554[0]).Nt()
                  } catch (_0x26c4e6) {}
                }
                return p553
              },
              true,
              false
            )
          } catch (_0x9cde49) {
            p484.m(_0x9cde49)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'after',
              function (vP555, p556) {
                var v398
                var vP555 = vP555(p556)
                for (v398 of p556) {
                  if (p484.V(v398) && p484.Y(this)) {
                    try {
                      p484.Element.create(v398).Nt()
                    } catch (_0x27cf65) {}
                  }
                }
                return vP555
              },
              true,
              false
            )
          } catch (_0x568f92) {
            p484.m(_0x568f92)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'before',
              function (vP557, p558) {
                var v399
                var vP557 = vP557(p558)
                for (v399 of p558) {
                  if (p484.V(v399) && p484.Y(this)) {
                    try {
                      p484.Element.create(v399).Nt()
                    } catch (_0x2071a6) {}
                  }
                }
                return vP557
              },
              true,
              false
            )
          } catch (_0x24ce90) {
            p484.m(_0x24ce90)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'replaceWith',
              function (vP559, p560) {
                var v400
                var v401 = p484.Y(this)
                var vP559 = vP559(p560)
                for (v400 of p560) {
                  if (p484.V(v400) && v401) {
                    try {
                      p484.Element.create(v400).Nt()
                    } catch (_0x31f7b0) {}
                  }
                }
                return vP559
              },
              true,
              false
            )
          } catch (_0x3aed2c) {
            p484.m(_0x3aed2c)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'insertAdjacentElement',
              function (p561, p562) {
                p561 = p561(p562)
                if (p484.V(p562[1]) && p484.Y(this)) {
                  try {
                    p484.Element.create(p562[1]).Nt()
                  } catch (_0x5d75e8) {}
                }
                return p561
              },
              true,
              false
            )
          } catch (_0x549efb) {
            p484.m(_0x549efb)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'append',
              function (vP563, p564) {
                var v402
                var vP563 = vP563(p564)
                for (v402 of p564) {
                  if (p484.V(v402) && p484.Y(this)) {
                    try {
                      p484.Element.create(v402).Nt()
                    } catch (_0x55281d) {}
                  }
                }
                return vP563
              },
              true,
              false
            )
          } catch (_0x131760) {
            p484.m(_0x131760)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'prepend',
              function (vP565, p566) {
                var v403
                var vP565 = vP565(p566)
                for (v403 of p566) {
                  if (p484.V(v403) && p484.Y(this)) {
                    try {
                      p484.Element.create(v403).Nt()
                    } catch (_0x232710) {}
                  }
                }
                return vP565
              },
              true,
              false
            )
          } catch (_0x3c65c3) {
            p484.m(_0x3c65c3)
          }
          try {
            p484.et(
              p483.Element.prototype,
              'insertAdjacentHTML',
              function (p567, p568) {
                p567 = p567(p568)
                if (p568[1] && p484.Y(this)) {
                  try {
                    p484.Element.create(p483.document.documentElement).Nt()
                  } catch (_0x36fe9f) {}
                }
                return p567
              },
              true,
              false
            )
          } catch (_0x176f2d) {
            p484.m(_0x176f2d)
          }
          try {
            p484.v(
              p483.Element.prototype,
              'innerHTML',
              function (p569) {
                return p569()
              },
              function (p570, p571) {
                p570 = p570(p571)
                if (p571 && p484.Y(this)) {
                  try {
                    p484.Element.create(this).Nt()
                  } catch (_0x500e6a) {}
                }
                return p570
              }
            )
          } catch (_0x4175ce) {
            p484.m(_0x4175ce)
          }
          try {
            p484.v(
              p483.Element.prototype,
              'outerHTML',
              function (p572) {
                return p572()
              },
              function (vP573, p574) {
                var v404 = p484.Y(this)
                var vP573 = vP573(p574)
                if (p574 && v404) {
                  try {
                    p484.Element.create(p483.document.documentElement).Nt()
                  } catch (_0x317ecc) {}
                }
                return vP573
              }
            )
          } catch (_0x502333) {
            p484.m(_0x502333)
          }
          return this
        }
        ze(p575, p576, p577 = false, p578 = false) {
          p484.v(
            p575,
            p576,
            function (p579, p580) {
              if (p580 === this.__cpn.T) {
                try {
                  var v405 = this.__cpn.Element.create(this)
                  if (v405.Lt(p576)) {
                    return v405.Ht(p576)
                  }
                } catch (_0x38e2a7) {}
              }
              return this.__cpn.Uri.create(p579(), p578).p()
            },
            p577
              ? function () {}
              : function (p581, p582) {
                  p581(this.__cpn.Uri.create(p582, p578).tt())
                }
          )
          return this
        }
        $e() {
          setTimeout(function () {}, 2000)
          return this
        }
      }
      return this
    }
  __Cpn.prototype.URI = __Cpn.prototype.URI || window.URI.noConflict()
  __Cpn.prototype.B64 = __Cpn.prototype.B64 || window.Base64.noConflict()
  __Cpn.prototype.FAB = __Cpn.prototype.FAB || window.FuckAdBlock
  delete window.FuckAdBlock
  delete window.fuckAdBlock
  if (!__Cpn.prototype.init) {
    __Cpn.prototype.init = function (p583, p584, p585, p586) {
      this.initScope(p583, this)
        .initPostedMessageOverride(p583, this)
        .initUri(p583, this)
        .initElement(p583, this)
        .initCookie(p583, this)
        .initLocation(p583, this)
        .initUi(p583, this)
        .initAd(p583, this)
        .initPopup(p583, this)
        .initWindow(p583, this)
        .initCpn(p583, p584, p585, this.Location.create(p586, false))
        .Window.create()
        .h()
    }
    new __Cpn().init(window, window.location.hostname, window.location.origin, window.location.href)
  }
})
