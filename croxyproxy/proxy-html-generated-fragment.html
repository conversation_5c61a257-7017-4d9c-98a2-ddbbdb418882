<!-- s: no | l: yes -->
<base __cpGenerated="1" href="https://cheatsheets.zip/" />
<!-- CP_UI_TAG -->
<script __cpp="1">
  window.__Cpn = window.__Cpn
    ? window.__Cpn
    : function () {
        this.permalink = this.URI(
          'https://**************/__cpi.php?s=UkQ2YXlSaWJuc3ZoeGR2dG04WW9LckptZ2c0bUVmZ3lqaFpSeXgzN2hEVW1KS2pvUHk1clA3S1kvdkR4S1F6MmJYSVBXQkU1K0tacEtPdnU0d2JVNmpiMitwSGwvSTdtUGRFd3ZVZW1TRGM9&r=aHR0cHM6Ly8xOTkuNzEuMjE0LjEyMS8%2FX19jcG89YUhSMGNITTZMeTlqYUdWaGRITm9aV1YwY3k1NmFYQQ%3D%3D&__cpo=1'
        )
        this.modal =
          '<style __cpp="1"> #__cpsModal #__cpsModalContent { background: url("data:image/png;base64,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") repeat !important; } #__cpsModal { display: none; position: fixed !important; z-index: 2147483647 !important; left: 0 !important; top: 0 !important; width: 100% !important; height: 100% !important; overflow: auto !important; background-color: rgb(0,0,0) !important; background-color: rgba(0,0,0,0.2) !important; box-sizing: border-box !important; clear: both !important; } #__cpsModal #__cpsModalContent { background-color: #fefefe !important; margin: 10% auto auto auto !important; padding: 15px !important; border: 1px solid #888 !important; width: 260px !important; text-align: center !important; color: #555555 !important; font: normal 15px Arial, Sans-Serif !important; box-shadow: 0 0 5px 0 #999999 !important; } #__cpsModal #__cpsModalContent.__cpsModalContentWide { width: 50% !important; } #__cpsModal a, #__cpsModal a:hover { color: #9E814D !important; cursor: pointer !important; text-decoration: underline !important; } #__cpsModal #__cpsModalRemoveAds { font-weight: bold !important; padding-top: 10px !important; } #__cpsModal .__cpsButton { width: auto !important; min-width: 70px !important; height: 26px !important; padding: 4px 8px !important; border-radius: 4px !important; border: 1px solid #B2B2B2 !important; font-size: 1em !important; background-color: #BABABA !important; color: white !important; text-decoration: none !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; justify-content: center !important; } #__cpsModal .__cpsButton:hover { background-color: #AEADAD !important; color: white !important; text-decoration: none !important; } #__cpsModal .__cpsButton:focus { outline: 0 !important; background-color: #AEADAD !important; color: white !important; text-decoration: none !important; } #__cpsModal .__cpsMainButton { background-color: #719D57 !important; } #__cpsModal .__cpsMainButton:hover { background-color: #84AD6B !important; } #__cpsModal .__cpsMainButton:focus { background-color: #84AD6B !important; } #__cpsModal .__cpsInfoFrame { width: 100% !important; height: 380px !important; margin: 0 0 10px 0 !important; padding: 0 !important; border: 1px solid #c6c6c6 !important; } #__cpsModal .h1 { font-weight: bold !important; padding-bottom: 4px !important; font-size: 1.15em !important; width: 100% !important; border-bottom: 1px solid #CFCECE !important; color: #626262 !important; } #__cpsModal .par { margin: 15px 0 0 0 !important; } /** media queries **/ @media (max-width: 728px) { #__cpsModal #__cpsModalContent.__cpsModalContentWide { width: 80% !important; } }</style><div data---cpt="0" id="__cpsModal" __cpp="1"><div id="__cpsModalContent" __cpp="1"></div></div>'
        this.header =
          '<div class="__cps" id="__cpsHeader" __cpp="1"> <table __cpp="1" cellspacing="0" cellpadding="0" id="__cpsHeaderBody"> <tr __cpp="1"> <td colspan="4" id="__cpsHeaderZapperColumn" __cpp="1"> <div __cpp="1" id="__cpsZapperContainer"> <div id="__cpsHeaderZapper" __cpp="1"></div> </div> </td> </tr> <tr __cpp="1" id="__cpsHeaderRow"> <td id="__cpsHomeLinkColumn" __cpp="1"> <a id="__cpsHomeLink" href="https://www.croxyproxy.com/?__cpo=1&__cpd=" class="__cpsButton" __cpp="1"> <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAQCAYAAAAWGF8bAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAFnSURBVHjalNI/LENRFMfxW0UwsZVJgkhQBhJ/EpOYCJPYDQaJyZ/RYGAhsdglJmEQswgWEhKjRJhKNf4M/kYV1/c2v0bVa1+d5NOe++65p++dV2OtNT46sC+dulaBXZxhDzWper9mg7i1P3GHAZThQdfcd7NfwwAmkNChaUwpj2NZzV3coClXw1IsqfgFw+hDP4bwZH9HzoYhbKnwAt0Y0V3FlXdpdqmIod6roZvDsYp20II5+zfmEca21s/ozWzoHimiglU0Yt1mjw00YEXrqF5gsuE4XrUxizYcWf9wNa2Y0foNk67hmt7mmAZ/afOPKz3dqGa86f4elcaYakRxiJD5X8TQjipECvm4ljCKPQ4k8KHc1Rdl7Lsz5boZU5C2wUTNp0fDRfTIgse+O/Nl0n7RL05xoLzWr7ggj4YlWXLfhsEsMwxmydNnGPR65HucoA7vCKjwMa3G5RHNzarZuc4m41uAAQBcTpcTzLSUFgAAAABJRU5ErkJggg==" alt=""> Home </a> </td> <td id="__cpsUrlColumn" __cpp="1"> <input disabled="disabled" id="__cpsUrl" type="text" name="url" __cpp="1"> </td> <td id="__cpsPremiumColumn" __cpp="1"> <a id="__cpsPremiumButton" target="_blank" href="https://reflect4.me/register?utm_source=proxy_header&__cpo=1" class="__cpsButton" __cpp="1"> &#9733; Reflect4 </a> </td> <td id="__cpsPermalinkColumn" __cpp="1"> <input id="__cpsPermalinkButton" class="__cpsButton" type="button" value="Permalink" __cpp="1"> <textarea id="__cpsPermalinkContainer" __cpp="1"></textarea> </td> </tr> </table> <div id="__cpsHeaderTab" title="Show/hide proxy navigation bar"> <div id="__cpsHeaderTabPatch"></div> </div></div>'
        __Cpn.prototype.debugMode = 0
        __Cpn.prototype.cdnOrigin = 'https://cdn.croxyproxy.com'
        __Cpn.prototype.frontOrigin = 'https://www.croxyproxy.com'
        __Cpn.prototype.serviceWorkerUrl =
          'https://**************/__cpa.sw.js?__cpo=1&dummy=3fc865bb71f724078fdfd8b6bd71f9bc'
        __Cpn.prototype.showAds = true
        __Cpn.prototype.isProxyHost = false
        this.analyticsUid = '1737362584.1686030427'
        this.analyticsTrackingId = 'G-RWN945234K'
        this.urlTimestamp = 1739496190
        this.sessionEndRedirectUrl = ''
        this.sessionEndRedirectTtl = 0
        this.fixedHeader = false
        this.adsJson =
          '{ "default": [ [ "https://p23hxejm1.com//CPR/CPR.php?c=1843194", "https://p23hxejm1.com//CPR/CPR.php?c=1843194", "https://p23hxejm1.com//CPR/CPR.php?c=1843194", "https://p23hxejm1.com//CPR/CPR.php?c=1843194", "https://p23hxejm1.com//CPR/CPR.php?c=1843194", "https://p23hxejm1.com//CPR/CPR.php?c=1843194", "https://cockpitprivilegedsag.com/api/users?token=L3Bhc2gxZHZmP2tleT02Zjc3N2MxYWI1YTJlMThiZDU0NDg2Y2NkMTY3ZjYzMw==", "https://cockpitprivilegedsag.com/api/users?token=L3Bhc2gxZHZmP2tleT02Zjc3N2MxYWI1YTJlMThiZDU0NDg2Y2NkMTY3ZjYzMw==" ], [ "https://cockpitprivilegedsag.com/api/users?token=L3Bhc2gxZHZmP2tleT02Zjc3N2MxYWI1YTJlMThiZDU0NDg2Y2NkMTY3ZjYzMw==" ] ]}'
      }
</script>
<style __cpp="1">
  /* common */
  html {
    overflow-y: visible !important;
    overscroll-behavior-y: auto !important;
  }

  body {
    overflow-y: auto !important;
    overscroll-behavior-y: auto !important;
  }

  table {
    color: inherit !important;
  }

  html:fullscreen .__cps {
    display: none !important;
  }

  html:-moz-full-screen .__cps {
    display: none !important;
  }

  html:-webkit-full-screen .__cps {
    display: none !important;
  }

  .__cps {
    box-sizing: border-box !important;
    clear: both !important;
    z-index: 2147483647 !important;
    color: #555555 !important;
    display: block !important;
  }

  .__cps,
  .__cps input,
  .__cps td,
  .__cps a {
    font:
      normal 14px Arial,
      Sans-Serif !important;
  }

  .__cps *,
  .__cps *:hover,
  .__cps *:focus,
  .__cps *:active {
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    box-sizing: inherit !important;
    background: transparent none !important;
    box-shadow: none !important;
    transform: none !important;
    vertical-align: top !important;
    text-transform: none !important;
    display: revert !important;
  }

  .__cps *:before,
  .__cps *:after {
    box-sizing: inherit !important;
  }

  .__cps *::selection {
    background: #a2c18f !important;
    color: white !important;
  }

  .__cps a,
  .__cps a:hover {
    color: #9e814d !important;
    cursor: pointer !important;
    text-decoration: none !important;
  }

  #__cpsHeader {
    width: 540px !important;
    top: 0 !important;
    position: fixed !important;
    margin-left: auto !important;
    margin-right: auto !important;
    left: 0 !important;
    right: 0 !important;
    display: block !important;
  }

  .__cps #__cpsHeaderTabPatch,
  .__cps #__cpsHeaderBody {
    background: url('data:image/png;base64,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')
      repeat !important;
  }

  /** header **/
  .__cps #__cpsHeaderBody {
    width: 100%;
    height: 100%;
    font: inherit !important;
    border-collapse: collapse !important;
    border-radius: 2px !important;
    box-shadow: 0 0 5px 0 #999999 !important;
    display: table !important;
  }

  .__cps #__cpsHeaderBody td {
    padding: 0;
    margin: 0 !important;
    vertical-align: top;
  }

  .__cps #__cpsHeaderBody td:last-child {
    padding-right: 4px !important;
  }

  .__cps #__cpsHeaderTab {
    float: right !important;
    background:
      url('data:image/png;base64,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')
        no-repeat 5px 2px,
      url('data:image/png;base64,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')
        repeat !important;
    background-size:
      auto 80%,
      cover !important;
    width: 35px !important;
    height: 16px !important;
    cursor: pointer !important;
    box-shadow: 0 0 5px 0 #999999 !important;
    display: block !important;
  }

  .__cps #__cpsZapperContainer {
    width: 100% !important;
    text-align: center !important;
    z-index: 2147483647 !important;
    position: relative !important;
    background: linear-gradient(rgba(0, 0, 0, 0.09), rgba(0, 0, 0, 0)) !important;
  }

  .__cps #__cpsHeaderZapper {
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .__cps #__cpsHeaderRow {
    height: 100% !important;
  }

  .__cps #__cpsHeaderTab {
    border-radius: 0 0 2px 2px !important;
  }

  .__cps #__cpsHeaderTabPatch {
    height: 4px !important;
    width: 100% !important;
    position: relative !important;
    top: -4px !important;
  }

  .__cps #__cpsHeaderZapper {
    width: 100% !important;
  }

  .__cps #__cpsHeaderZapperFrame {
    border: 0 !important;
    overflow: hidden !important;
    width: 100% !important;
    height: 80px !important;
    vertical-align: bottom !important;
  }

  .__cps #__cpsMessageColumn {
    height: 20px !important;
    padding: 4px 4px 0 4px !important;
    text-align: center !important;
  }

  .__cps #__cpsMessage {
    height: 20px !important;
    line-height: 16px !important;
    background-color: #f2e9d9 !important;
    border-radius: 4px !important;
    padding: 0 6px !important;
    overflow: hidden !important;
    border: 1px solid #d7c39a !important;
  }

  .__cps #__cpsMessage a {
    font-weight: bold !important;
  }

  .__cps #__cpsHeaderRow {
    height: 34px !important;
  }

  .__cps #__cpsUrlColumn {
    padding: 4px 0 0 4px !important;
  }

  .__cps #__cpsSubmitColumn,
  .__cps #__cpsHomeLinkColumn {
    padding: 4px 0 0 4px !important;
  }

  .__cps #__cpsSubmitColumn {
    width: 70px !important;
  }

  .__cps #__cpsHomeLinkColumn {
    width: 71px !important;
  }

  .__cps #__cpsPremiumColumn {
    width: 88px !important;
    padding: 4px 0 0 4px !important;
  }

  .__cps #__cpsPremiumButton {
    color: #fcf2b4 !important;
  }

  .__cps #__cpsPermalinkColumn {
    width: 85px !important;
    padding: 4px 0 0 4px !important;
  }

  .__cps #__cpsUrl,
  .__cps .__cpsButton {
    width: 100% !important;
    height: 26px !important;
    line-height: 16px !important;
    padding: 4px !important;
    border-radius: 4px !important;
    border: 1px solid #b2b2b2 !important;
    font-size: 1em !important;
    display: inline-block !important;
  }

  .__cps #__cpsUrl:focus {
    outline: 2px auto #a2c18f !important;
  }

  .__cps #__cpsUrl {
    background-color: white !important;
    color: #555555 !important;
  }

  .__cps #__cpsUrl:disabled {
    background: transparent !important;
  }

  .__cps .__cpsButton {
    background-color: #969595 !important;
    color: white !important;
    text-decoration: none !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    text-align: center !important;
  }

  .__cps .__cpsButton:hover {
    background-color: #868585 !important;
    color: white !important;
    text-decoration: none !important;
  }

  .__cps .__cpsButton:focus {
    outline: 0 !important;
    background-color: #7a7979 !important;
    color: white !important;
    text-decoration: none !important;
  }

  .__cps #__cpsSubmitButton,
  .__cps #__cpsHomeLink,
  .__cps #__cpsPremiumButton {
    background-color: #94b87f !important;
  }

  .__cps #__cpsSubmitButton:hover,
  .__cps #__cpsHomeLink:hover,
  .__cps #__cpsPremiumButton:hover {
    background-color: #a2c18f !important;
  }

  .__cps #__cpsSubmitButton:focus,
  .__cps #__cpsHomeLink:focus,
  .__cps #__cpsPremiumButton:focus {
    background-color: #86af6e !important;
  }

  .__cps #__cpsHomeLink {
    white-space: nowrap !important;
  }

  .__cps #__cpsHomeLink img {
    width: 15px !important;
  }

  .__cps #__cpsPermalinkContainer {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 2px !important;
    height: 2px !important;
    padding: 0 !important;
    border: 0 !important;
    outline: 0 !important;
    background: transparent !important;
    z-index: -1 !important;
  }

  /** extension styles **/
  .__cps #__cpsExtensionProxyColumn {
    width: 34px !important;
    padding: 2px !important;
  }

  .__cps #__cpsExtensionProxyColumn img {
    width: 100% !important;
    height: auto !important;
  }

  .__cps #__cpsExtensionPermalinkColumn {
    height: 34px !important;
    padding: 4px !important;
  }

  .__cps #__cpsExtensionPermalinkColumn #__cpsPermalinkButton {
    height: 28px !important;
    padding: 1px !important;
  }

  /** media queries **/
  @media (max-width: 728px) {
    .__cps,
    #__cpsHeader,
    .__cps #__cpsHeaderZapperColumn,
    .__cps #__cpsHeaderZapper,
    .__cps #__cpsUrlColumn {
      width: 100% !important;
    }

    .__cps #__cpsSubmitButton {
      width: 70px !important;
    }

    .__cps #__cpsHomeLink {
      width: 67px !important;
    }

    .__cps #__cpsPermalinkButton {
      width: 75px !important;
    }

    .__cps #__cpsHeaderZapperFrame {
      width: 100% !important;
      height: 70px !important;
    }
  }

  @media (max-width: 550px) {
    .__cps #__cpsHeaderZapperFrame {
      height: 50px !important;
    }
  }
</style>
<script
  __cpp="1"
  type="text/javascript"
  src="https://**************/__cpa.cp.js?__cpo=1&dummy=e8bd78d2e15bb7696a5abe317d345d71"
></script>
