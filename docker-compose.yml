x-logging: &default-logging
  logging:
    driver: 'json-file'
    options:
      max-size: '100m' # 每个日志文件最大尺寸
      max-file: '3' # 保留的日志文件数量

services:
  redis-proxy:
    <<: *default-logging
    build:
      context: ./apps/redis-proxy
      dockerfile: ./Dockerfile.dev
    ports:
      - '6380:6380'
    volumes:
      - ./apps/redis-proxy/logs:/usr/local/openresty/nginx/logs

  nginx-lua-dev:
    <<: *default-logging
    build:
      context: ./apps/nginx-lua
      dockerfile: ./Dockerfile.dev
    labels:
      dev.orbstack.domains: 'dp.local'
      dev.orbstack.http-port: 80
      dev.orbstack.https-port: 443
    volumes:
      - ./apps/nginx-lua/conf/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
      - ./apps/nginx-lua/conf/conf.d:/usr/local/openresty/nginx/conf/conf.d
      - ./apps/nginx-lua/conf/stream.conf:/usr/local/openresty/nginx/conf/stream.conf
      - ./apps/nginx-lua/lua:/usr/local/openresty/nginx/lua
      - ./apps/nginx-lua/ssl/https:/usr/local/openresty/nginx/ssl/https
      - ./apps/nginx-lua/ssl/redis:/usr/local/openresty/nginx/ssl/redis
      - ./apps/service-worker/dist:/usr/local/openresty/nginx/html
      - ./apps/nginx-lua/logs:/usr/local/openresty/nginx/logs
    env_file:
      - ./apps/nginx-lua/.env.local
