---
description:
globs:
alwaysApply: false
---
# ProxyOrb 代理服务开发规则

## OpenResty + Lua 代理服务

### 项目结构规范
```
apps/nginx-lua/
├── conf/                   # nginx 配置文件
│   ├── nginx.conf         # 主配置文件
│   ├── conf.d/           # 模块化配置
│   └── stream.conf       # Stream 模块配置
├── lua/                   # Lua 源代码
│   ├── core/             # 核心模块
│   ├── handlers/         # 请求处理器
│   ├── middleware/       # 中间件
│   └── utils/            # 工具模块
├── html/                 # 静态文件
└── ssl/                  # SSL 证书
```

### Lua 模块化开发
```lua
-- 模块声明标准格式
local _M = {
    _VERSION = '0.1.0'
}

-- 私有函数使用 local
local function private_function()
    -- 私有实现
end

-- 公共函数
function _M.public_function(param1, param2)
    -- 参数验证
    if not param1 or type(param1) ~= "string" then
        return nil, "Invalid parameter"
    end

    -- 实现逻辑
    return result, nil
end

return _M
```

### 错误处理和日志
```lua
local log = require "utils.logger"

-- 统一错误处理
local function handle_error(err, context)
    log.error("Error in %s: %s", context, err)
    ngx.status = 500
    ngx.say("Internal Server Error")
    ngx.exit(500)
end

-- 安全的函数调用
local ok, result = pcall(risky_function, param)
if not ok then
    handle_error(result, "risky_function")
    return
end
```

### Redis 连接管理
```lua
local redis_util = require "utils.redis"

-- 使用连接池
local function get_cached_data(key)
    return redis_util.exec(function(redis)
        local result, err = redis:get(key)
        if not result then
            return nil, err
        end
        return result
    end)
end

-- 带过期时间的缓存
local function set_cache(key, value, ttl)
    return redis_util.set(key, value, ttl or 3600)
end
```

### 中间件模式
```lua
-- middleware/auth.lua
local _M = {}

function _M.check()
    local session = ngx.var.session
    if not session or session == "-" then
        ngx.status = 401
        ngx.say("Unauthorized")
        ngx.exit(401)
    end
end

function _M.handle_auth_api()
    -- 处理认证 API 逻辑
end

return _M
```

### URL 处理和验证
```lua
local url_util = require "utils.url"
local config = require "core.config"

local function parse_proxy_url(uri)
    local args = ngx.req.get_uri_args()
    local pot = args[config.base.pot_key]

    if not pot then
        return nil, "Missing proxy target"
    end

    -- Base64 解码和 URL 验证
    local target_url = url_util.decode_pot(pot)
    local parsed = url_util.parse_url(target_url)

    if not parsed or not parsed.host then
        return nil, "Invalid target URL"
    end

    return parsed, nil
end
```

### 性能优化准则
- 使用 `ngx.shared.DICT` 缓存频繁访问的数据
- 避免在 `header_filter` 和 `body_filter` 阶段进行重计算
- 使用连接池管理外部连接 (Redis, HTTP)
- 合理设置 `lua_code_cache` (生产环境开启)
- 使用 `ngx.timer` 处理异步任务

### WebSocket 处理
```lua
-- handlers/websocket.lua
local function handle_websocket()
    -- 检查 Upgrade 头
    local upgrade = ngx.var.http_upgrade
    if not upgrade or upgrade:lower() ~= "websocket" then
        return false, "Not a WebSocket request"
    end

    -- 处理 WebSocket 特定逻辑
    ngx.var.original_url = build_websocket_url(target_url)
    ngx.ctx.is_websocket = true

    return true
end
```

## Service Worker 拦截器开发

### 拦截器基类
```typescript
export abstract class AbstractInterceptor implements BaseInterceptor {
  protected config: InterceptorConfig

  constructor(config: InterceptorConfig) {
    this.config = config
  }

  protected processUrl(url: string | null | undefined): string {
    return toProxyURL(url || '', window.location.href)
  }

  abstract init(): void
}
```

### URL 重写核心逻辑
```typescript
import { toProxyURL } from '@/utils/url'

export function toProxyURL(url: string, baseUrl: string): string {
  try {
    const target = new URL(url, baseUrl)
    const proxy = new URL(window.location.href)

    // 编码目标 URL
    const encodedTarget = encodePot(target.toString())

    // 构建代理 URL
    proxy.searchParams.set('__pot', encodedTarget)
    proxy.pathname = target.pathname

    return proxy.toString()
  } catch (error) {
    console.warn('Failed to process URL:', error)
    return url
  }
}
```

### DOM 拦截器
```typescript
export class DOMDynamicInterceptor extends AbstractInterceptor {
  init(): void {
    this.interceptElementCreation()
    this.interceptAttributeChanges()
  }

  private interceptElementCreation(): void {
    const originalCreateElement = document.createElement
    const self = this

    document.createElement = function <K extends keyof HTMLElementTagNameMap>(
      tagName: K
    ): HTMLElementTagNameMap[K] {
      const element = originalCreateElement.call(this, tagName)

      // 拦截特定元素的属性设置
      if (tagName === 'a' || tagName === 'img' || tagName === 'script') {
        self.interceptElement(element)
      }

      return element
    }
  }
}
```

### 网络请求拦截
```typescript
export class RequestInterceptor extends AbstractInterceptor {
  init(): void {
    this.interceptFetch()
    this.interceptXHR()
    this.interceptWebSocket()
  }

  private interceptFetch(): void {
    const originalFetch = window.fetch
    const self = this

    window.fetch = function (input: RequestInfo | URL, init?: RequestInit) {
      const url = typeof input === 'string' ? input : input.url
      const processedUrl = self.processUrl(url)

      return originalFetch(processedUrl, init)
    }
  }
}
```

### 存储拦截器
```typescript
export class StorageInterceptor extends AbstractInterceptor {
  init(): void {
    this.interceptStorage(localStorage)
    this.interceptStorage(sessionStorage)
  }

  private interceptStorage(storage: Storage): void {
    const originalGetItem = storage.getItem.bind(storage)
    const originalSetItem = storage.setItem.bind(storage)
    const self = this

    storage.getItem = function (key: string): string | null {
      // 使用域名隔离存储
      const prefixedKey = `${self.config.target.hostname}:${key}`
      return originalGetItem(prefixedKey)
    }

    storage.setItem = function (key: string, value: string): void {
      const prefixedKey = `${self.config.target.hostname}:${key}`
      return originalSetItem(prefixedKey, value)
    }
  }
}
```

## 响应内容处理

### HTML 处理器
```typescript
export class ProxyHTMLProcessor implements BaseProcessor {
  canProcess(contentType: string): boolean {
    return contentType.includes('text/html')
  }

  async process(content: string, requestUrl: string): Promise<string> {
    let processedContent = content

    // 注入拦截器脚本
    processedContent = this.injectInterceptor(processedContent, requestUrl)

    // 重写 meta refresh
    processedContent = this.processMetaRefresh(processedContent, requestUrl)

    // 处理 base 标签
    processedContent = this.processBaseTags(processedContent, requestUrl)

    return processedContent
  }
}
```

### JavaScript 处理器
```typescript
export class ProxyJSProcessor implements BaseProcessor {
  canProcess(contentType: string): boolean {
    return contentType.includes('javascript') || contentType.includes('json')
  }

  async process(content: string, requestUrl: string): Promise<string> {
    // 简单的字符串替换，处理常见的 URL 模式
    return content
      .replace(/https?:\/\/[^"'\s]+/g, (match) =>
        this.processUrl(match, requestUrl)
      )
  }
}
```

## 安全和性能考虑

### 安全准则
- 严格验证输入参数，防止 URL 注入
- 使用白名单过滤危险的目标域名
- 防止代理循环 (检测自身域名)
- 限制代理深度和请求频率
- 过滤敏感响应头

### 性能优化
```lua
-- 使用本地缓存减少重复计算
local url_cache = ngx.shared.proxy_cache

local function get_cached_url(key)
    local cached = url_cache:get(key)
    if cached then
        return cached
    end

    local result = expensive_url_processing(key)
    url_cache:set(key, result, 300) -- 缓存5分钟
    return result
end
```

### 错误恢复
```typescript
// Service Worker 中的错误恢复
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error)
  // 不阻止默认处理，确保页面功能正常
})

// 拦截器中的安全回退
try {
  this.interceptAPI()
} catch (error) {
  console.warn('Failed to intercept API, falling back to default:', error)
  // 不影响页面正常功能
}
```

始终确保代理服务的稳定性和可靠性，错误处理不应影响用户的正常浏览体验。
