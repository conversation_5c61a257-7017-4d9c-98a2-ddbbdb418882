---
description:
globs:
alwaysApply: false
---
# ProxyOrb 前端开发规则

## Next.js 15 + React 19 最佳实践

### App Router 结构
- 使用 App Router (`app/` 目录) 而非 Pages Router
- 路由文件命名：`page.tsx`, `layout.tsx`, `loading.tsx`, `error.tsx`
- 国际化路由结构：`app/[locale]/(main)/page.tsx`
- 路由组使用括号分组：`(main)`, `(auth)`

### 服务端组件优先
```tsx
// 默认使用服务端组件
export default async function Page() {
  const data = await fetchData()
  return <div>{data}</div>
}

// 仅在需要交互时使用客户端组件
'use client'
export function InteractiveComponent() {
  const [state, setState] = useState()
  return <button onClick={() => setState(prev => !prev)}>Toggle</button>
}
```

### TypeScript 接口定义
```tsx
// Props 接口必须导出，便于复用
export interface PageProps {
  params: Promise<{ locale: string }>
  searchParams: Promise<Record<string, string | string[] | undefined>>
}

// 组件 props 严格类型化
interface ComponentProps {
  title: string
  children: React.ReactNode
  className?: string
}
```

### 数据获取模式
- 服务端组件使用 async/await 直接获取数据
- 客户端使用 tRPC + React Query 进行状态管理
- 使用 Suspense 处理加载状态
- 使用 Error Boundary 处理错误状态

### 国际化 (i18n)
```tsx
import { getTranslations, setRequestLocale } from '@momo/i18n/server'

export default async function Page({ params }: PageProps) {
  const { locale } = await params
  setRequestLocale(locale)
  const t = await getTranslations('metadata')

  return <h1>{t('site-title')}</h1>
}
```

### 样式和 UI 组件
- 使用 Tailwind CSS 进行样式设计
- 优先使用 `@momo/ui` 包中的组件
- 响应式设计优先：`sm:`, `md:`, `lg:`, `xl:`
- 深色模式支持：`dark:` 前缀
- 使用 `cn()` 函数合并类名

### 性能优化
```tsx
import dynamic from 'next/dynamic'
import { memo } from 'react'

// 懒加载非关键组件
const LazyComponent = dynamic(() => import('./heavy-component'), {
  loading: () => <div>Loading...</div>
})

// 性能敏感组件使用 memo
const OptimizedComponent = memo(function Component({ data }: Props) {
  return <div>{data}</div>
})
```

### SEO 和元数据
```tsx
import type { Metadata } from 'next'

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations('metadata')

  return {
    title: t('page-title'),
    description: t('page-description'),
    openGraph: {
      title: t('page-title'),
      description: t('page-description'),
      url: `${SITE_URL}${getLocalizedPath({ slug: '', locale })}`
    }
  }
}
```

### 表单处理
```tsx
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const schema = z.object({
  url: z.string().url('请输入有效的 URL')
})

export function ProxyForm() {
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(schema)
  })

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register('url')} />
      {errors.url && <span>{errors.url.message}</span>}
    </form>
  )
}
```

### 状态管理
- 简单状态使用 React hooks (`useState`, `useReducer`)
- 全局状态使用 Zustand 或 nanostores
- 服务端状态使用 tRPC + React Query
- 表单状态使用 react-hook-form

### 错误处理
```tsx
// error.tsx
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px]">
      <h2>出现错误！</h2>
      <button onClick={reset}>重试</button>
    </div>
  )
}
```

### 代码分割和懒加载
- 使用 `dynamic()` 懒加载重型组件
- 路由级别自动代码分割
- 使用 `Suspense` 包装异步组件
- 避免在初始包中包含第三方库

### 测试策略
- 组件测试使用 React Testing Library
- E2E 测试使用 Playwright
- 类型检查作为测试的一部分
- 关键用户流程必须有测试覆盖

### 环境变量
```tsx
import { env } from '@momo/env'

// 客户端环境变量必须以 NEXT_PUBLIC_ 开头
const apiUrl = env.NEXT_PUBLIC_API_URL

// 服务端环境变量直接使用
const secretKey = env.JWT_SECRET
```

## 代理相关前端功能

### URL 输入组件
```tsx
interface URLInputProps {
  onSubmit: (url: string) => void
  loading?: boolean
}

export function URLInput({ onSubmit, loading }: URLInputProps) {
  // URL 验证和提交逻辑
  // 支持多种 URL 格式
  // 提供使用提示和示例
}
```

### 代理状态显示
- 显示当前代理目标
- 连接状态指示器
- 错误信息展示
- 网络质量监控

### 响应式设计
- 移动端友好的 URL 输入
- 平板和桌面端的多列布局
- 触摸友好的交互元素
- 适配不同屏幕尺寸

始终关注用户体验、性能和可访问性，确保代理服务的前端界面简洁易用。
