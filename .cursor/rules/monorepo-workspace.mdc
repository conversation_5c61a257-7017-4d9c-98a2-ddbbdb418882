---
description:
globs:
alwaysApply: false
---
# ProxyOrb Monorepo 工作空间管理

## 工作空间结构
```
proxyOrb/
├── apps/                   # 应用程序
│   ├── web/               # Next.js 前端应用
│   ├── nginx-lua/         # OpenResty 代理服务
│   ├── service-worker/    # Service Worker 拦截器
│   └── redis-proxy/       # Redis 代理服务
├── packages/              # 共享包
│   ├── ui/               # UI 组件库
│   ├── utils/            # 工具函数
│   ├── db/               # 数据库层
│   ├── env/              # 环境变量管理
│   ├── i18n/             # 国际化
│   └── shared/           # 共享类型和常量
├── turbo.json            # Turborepo 配置
├── package.json          # 根目录 package.json
└── pnpm-workspace.yaml   # pnpm 工作空间配置
```

## pnpm 工作空间管理

### 包依赖声明
```json
// apps/web/package.json
{
  "dependencies": {
    "@momo/ui": "workspace:*",
    "@momo/utils": "workspace:*",
    "@momo/env": "workspace:*"
  }
}
```

### 安装和管理依赖
```bash
# 为特定应用安装依赖
pnpm add react-hook-form --filter @momo/web

# 为所有包安装依赖
pnpm install

# 为特定包安装开发依赖
pnpm add -D @types/node --filter @momo/ui

# 在根目录安装全局工具
pnpm add -D turbo -w
```

### 包版本管理
- 内部包使用 `workspace:*` 引用最新版本
- 外部依赖在根目录统一管理版本
- 使用 `pnpm check:npm` 检查依赖更新

## Turborepo 构建系统

### 任务配置 (turbo.json)
```json
{
  "globalDependencies": [".env.local"],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"],
      "env": ["NODE_ENV"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "dependsOn": ["^build"],
      "outputs": []
    },
    "type-check": {
      "dependsOn": ["^build"],
      "outputs": []
    }
  }
}
```

### 常用构建命令
```bash
# 构建所有包和应用
pnpm build

# 只构建包 (不包括应用)
pnpm build:packages

# 开发模式启动所有服务
pnpm dev

# 启动特定服务
pnpm dev:web
pnpm dev:server

# 构建特定应用
turbo build --filter=@momo/web
```

### 缓存策略
- 开发环境禁用缓存以确保实时更新
- 生产构建启用缓存提高构建速度
- 输出目录配置确保正确的缓存失效
- 环境变量变化自动失效缓存

## 共享包开发

### 包结构标准
```
packages/ui/
├── src/
│   ├── components/       # React 组件
│   ├── hooks/           # 自定义 hooks
│   ├── utils/           # 工具函数
│   └── index.ts         # 导出入口
├── package.json
├── tsconfig.json
└── tailwind.config.ts
```

### 导出规范
```typescript
// packages/ui/src/index.ts
export * from './components/button'
export * from './components/input'
export * from './hooks/use-theme'
export { default as Button } from './components/button'

// 类型导出
export type { ButtonProps } from './components/button'
```

### TypeScript 配置继承
```json
// packages/ui/tsconfig.json
{
  "extends": "@momo/tsconfig/base.json",
  "compilerOptions": {
    "declaration": true,
    "declarationMap": true,
    "outDir": "dist"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

## 环境变量管理

### 环境配置层次
```
.env.local          # 本地开发 (不提交)
.env.development    # 开发环境
.env.production     # 生产环境
.env                # 默认配置
```

### 环境变量验证
```typescript
// packages/env/src/index.ts
import { z } from 'zod'

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']),
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  JWT_SECRET: z.string().min(32)
})

export const env = envSchema.parse(process.env)
```

## 代码质量控制

### ESLint 配置共享
```javascript
// packages/eslint-config/index.js
module.exports = {
  extends: [
    'next/core-web-vitals',
    '@typescript-eslint/recommended',
    'prettier'
  ],
  rules: {
    '@typescript-eslint/no-explicit-any': 'warn',
    'simple-import-sort/imports': 'error'
  }
}
```

### 统一代码格式化
```json
// prettier.config.js (根目录)
module.exports = {
  semi: false,
  singleQuote: true,
  trailingComma: 'none',
  tabWidth: 2,
  printWidth: 100
}
```

### 提交钩子
```json
// package.json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "**/*": "prettier --write --ignore-unknown"
  }
}
```

## 测试策略

### 单元测试
```bash
# 运行所有测试
pnpm test:unit

# 测试特定包
pnpm test:unit --filter @momo/utils

# 监视模式
pnpm test:unit:watch
```

### E2E 测试
```bash
# 安装 Playwright
pnpm test:e2e:install

# 运行 E2E 测试
pnpm test:e2e

# UI 模式运行测试
pnpm test:e2e:ui
```

## 发布和版本管理

### Changesets 工作流
```bash
# 创建变更集
npx changeset

# 版本更新
npx changeset version

# 发布包
npx changeset publish
```

### 发布前检查
```bash
# 完整检查流程
pnpm check

# 包括以下步骤：
# - 类型检查
# - 代码检查
# - 格式检查
# - 拼写检查
# - 依赖检查
```

## 开发工作流

### 启动开发环境
```bash
# 完整开发环境 (前端 + 代理服务)
pnpm dev

# 仅前端开发
pnpm dev:web

# 仅代理服务开发
pnpm dev:server
```

### 构建和部署
```bash
# 生产构建
pnpm build

# 构建 Web 应用
pnpm build:web

# 构建代理服务
pnpm build:server
```

### 清理和重置
```bash
# 清理所有构建输出
pnpm clean

# 清理并重新安装依赖
rm -rf node_modules && pnpm install
```

## 性能优化

### 构建性能
- 使用 Turborepo 的增量构建
- 启用远程缓存 (可选)
- 并行执行独立任务
- 合理配置依赖关系图

### 开发体验
- 热重载配置优化
- TypeScript 项目引用
- 快速的类型检查
- 实时错误提示

## 故障排除

### 常见问题
1. **依赖解析错误**: 检查 workspace 配置和包引用
2. **构建缓存问题**: 清理 `.turbo` 缓存目录
3. **类型错误**: 确保包之间的类型导出正确
4. **热重载失效**: 检查文件监听配置

### 调试命令
```bash
# 查看依赖树
pnpm list --depth=2

# 检查 workspace 配置
pnpm m ls

# 详细构建日志
turbo build --verbose

# 清理所有缓存
pnpm clean && rm -rf .turbo
```

始终保持 monorepo 的整洁和高效，确保团队协作的顺畅。
