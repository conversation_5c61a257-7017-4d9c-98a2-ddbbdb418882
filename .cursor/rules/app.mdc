---
description:
globs:
alwaysApply: false
---
# ProxyOrb 项目核心规则

## 项目概述
ProxyOrb 是一个免费的在线代理服务网站，提供简单、安全、高效的网页代理功能，方便用户访问被限制的网站。项目采用现代化的微服务架构，包含前端展示、代理服务、Service Worker 拦截等多个核心组件。

## 技术架构
### 核心应用模块
- **apps/web**: Next.js 15 前端应用，使用 React 19、TypeScript、Tailwind CSS
- **apps/nginx-lua**: OpenResty + Lua 代理服务器，负责实际的 HTTP/HTTPS/WebSocket 代理
- **apps/service-worker**: Service Worker 拦截器，处理客户端请求重写和响应处理
- **apps/redis-proxy**: Redis 代理服务，用于缓存和状态管理

### 共享包架构
- **packages/ui**: 基于 Tailwind CSS 的 UI 组件库
- **packages/i18n**: 国际化支持 (next-intl)
- **packages/db**: 数据库层 (Drizzle ORM + PostgreSQL)
- **packages/env**: 环境变量管理 (Zod 验证)
- **packages/utils**: 通用工具函数
- **packages/shared**: 共享类型定义和常量

## 代理工作原理
### 请求流程
1. 用户访问 ProxyOrb 网站，输入目标 URL
2. 前端将目标 URL 进行 Base64 编码作为 `__pot` 参数
3. nginx-lua 服务器解析 `__pot` 参数，转发请求到目标服务器
4. Service Worker 拦截页面内的所有子请求，重写 URL 以保持代理状态
5. 响应经过处理后返回给客户端

### 关键技术实现
- **URL 重写**: 使用 `__pot` 参数传递目标 URL，Base64 编码确保兼容性
- **WebSocket 支持**: 通过 `__pows` 前缀处理 WebSocket 连接
- **Service Worker 拦截**: 拦截 fetch、XMLHttpRequest、WebSocket 等网络请求
- **DOM 动态处理**: 拦截 location、history、storage 等 Web API
- **响应内容处理**: 重写 HTML、CSS、JavaScript 中的链接和资源引用

## 开发规范

### 代码风格
- 统一使用 TypeScript，严格类型检查
- 遵循 ESLint + Prettier 配置
- 使用 conventional commits 规范
- 组件采用函数式编程风格，优先使用 hooks

### 命名规范
- 文件名使用 kebab-case: `user-profile.tsx`
- 组件名使用 PascalCase: `UserProfile`
- 函数和变量使用 camelCase: `getUserProfile`
- 常量使用 SCREAMING_SNAKE_CASE: `API_BASE_URL`
- 类型定义使用 PascalCase: `UserProfile`

### 目录结构
```
apps/web/src/
├── app/                 # Next.js App Router
├── components/          # React 组件
├── lib/                # 业务逻辑和工具
├── hooks/              # 自定义 React hooks
├── styles/             # 全局样式
└── types/              # 类型定义
```

### 组件开发
- 优先使用服务端组件，必要时使用 'use client'
- 组件 props 必须定义 TypeScript 接口
- 使用 React.memo 优化性能关键组件
- 异步组件使用 Suspense 边界
- 错误处理使用 Error Boundary

### 代理服务开发 (nginx-lua)
- Lua 代码严格遵循模块化结构
- 中间件按职责分离：认证、指标、响应处理等
- 错误处理必须记录详细日志
- 性能关键路径避免阻塞操作
- Redis 连接使用连接池管理

### Service Worker 开发
- 拦截器按功能模块化：location、storage、request 等
- URL 处理统一使用 `toProxyURL` 函数
- 避免拦截代理自身的请求
- 错误处理不能影响页面正常功能
- 保持与目标网站 API 兼容性

## 性能优化原则
- 前端资源启用压缩和缓存
- 代理服务使用 nginx 缓冲优化
- 图片使用 Next.js Image 组件优化

## SEO 优化
- 多语言支持 (i18n)
- 语义化 HTML 结构
- Meta 标签和 JSON-LD 结构化数据
- 页面性能优化 (Core Web Vitals)
- 内容策略：技术博客、使用指南

## 监控和日志
- 结构化日志输出
- 请求指标收集
- 错误追踪和报警
- 性能监控
- 用户行为分析

## 部署配置
- Docker 容器化部署
- nginx 反向代理 + SSL 终端
- Redis 集群用于会话管理
- 水平扩展支持
- 健康检查和故障恢复

## 关键环境变量
```
NODE_ENV                    # 运行环境
REDIS_HOST/PORT            # Redis 配置
JWT_SECRET                 # JWT 密钥
NGINX_ENV                  # nginx 环境配置
LOG_LEVEL                  # 日志级别
```

开发时始终考虑用户体验、安全性和性能，确保代理服务的稳定性和可用性。
