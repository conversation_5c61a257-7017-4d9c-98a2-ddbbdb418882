{
  {{#unless shouldPublish}}
  "private": true,
  {{/unless}}
  "name": "@momo/{{ name }}",
  "version": "{{ version }}",
  "description": "{{{ description }}}",
  "license": "MIT",
  {{#if shouldPublish}}
  "author": "momo <<EMAIL>> (https://github.com/momo/)",
  "homepage": "https://github.com/proxyorb/proxyorb.com#readme",
  "repository": {
    "type": "git",
    "url": "git+https://github.com/proxyorb/proxyorb.com.git"
  },
  "bugs": {
    "url": "https://github.com/proxyorb/proxyorb.com/issues"
  },
  "files": [
    "dist"
  ],
  "publishConfig": {
    "access": "public"
  },
  {{/if}}
  "type": "module",
  {{#if shouldCompile}}
  "main": "./dist/index.js",
  "module": "./dist/index.js",
  "types": "./dist/index.d.ts",
  "sideEffects": false,
  {{else}}
  "main": "./src/index.ts",
  {{/if}}
  "scripts": {
    "lint": "eslint . --max-warnings 0",
    "lint:fix": "eslint --fix .",
    "type-check": "tsc --noEmit"
    {{#if shouldCompile}}
    ,"build": "tsup",
    "dev": "tsup --watch",
    "clean": "rm -rf .turbo dist"
    {{else}}
    ,"clean": "rm -rf .turbo"
    {{/if}}
  },
  "devDependencies": {
    "@momo/eslint-config": "workspace:*",
    "@momo/tsconfig": "workspace:*"
  },
  "lint-staged": {
    "*.{cjs,mjs,js,jsx,cts,mts,ts,tsx,json}": "eslint --fix",
    "**/*": "prettier --write --ignore-unknown"
  }
}
