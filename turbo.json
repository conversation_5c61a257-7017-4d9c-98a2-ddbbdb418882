{"$schema": "https://turbo.build/schema.json", "globalDependencies": [".env.*local"], "globalEnv": ["NODE_ENV", "CI", "ANALYZE", "PORT", "LOG_LEVEL", "JWT_SECRET", "FALLBACK_SERVERS", "REDIS_HOST", "REDIS_PORT", "REDIS_DB", "REDIS_USERNAME", "REDIS_PASSWORD", "REDIS_KEY_PREFIX", "HEARTBEAT_WORKER_KEY", "HEARTBEAT_ACTIVE_WORKERS_KEY", "GOOGLE_API_KEY", "GITHUB_TOKEN", "WAKATIME_API_KEY", "AUTH_SECRET", "AUTH_TRUST_HOST", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "GITHUB_CLIENT_ID", "GITHUB_CLIENT_SECRET", "DATABASE_URL", "UPSTASH_REDIS_REST_URL", "UPSTASH_REDIS_REST_TOKEN", "IP_ADDRESS_SALT", "DISCORD_WEBHOOK_URL", "NEXT_PUBLIC_UMAMI_URL", "NEXT_PUBLIC_UMAMI_WEBSITE_ID", "NEXT_PUBLIC_FLAG_COMMENT", "NEXT_PUBLIC_FLAG_AUTH", "NEXT_PUBLIC_FLAG_STATS", "NEXT_PUBLIC_FLAG_ANALYTICS", "NEXT_PUBLIC_FLAG_LIKE_BUTTON", "NEXT_RUNTIME", "NEXT_PUBLIC_PROXY_SERVER_URL"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", ".mdx/**"]}, "build:mdx": {"dependsOn": ["@momo/mdx#build"], "outputs": [".mdx/**"]}, "build:packages": {"dependsOn": ["^build:packages"], "outputs": ["dist/**"]}, "clean": {"cache": false}, "db:check": {}, "db:generate": {}, "db:migrate": {"cache": false}, "db:push": {"cache": false, "interactive": true}, "db:seed": {}, "db:studio": {"cache": false, "interactive": true, "persistent": true}, "dev": {"cache": false, "persistent": true}, "dev:packages": {"cache": false, "persistent": true}, "dev:web": {"cache": false, "persistent": true}, "lint": {}, "lint:fix": {}, "test:e2e": {"outputs": ["playwright-report/**", "test-results/**"]}, "test:e2e:inspector": {"outputs": ["playwright-report/**", "test-results/**"]}, "test:e2e:ui": {"outputs": ["playwright-report/**", "test-results/**"]}, "type-check": {}}}