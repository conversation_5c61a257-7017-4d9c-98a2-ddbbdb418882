name: Release

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Create Release Pull Request or Publish to NPM
        id: changesets
        uses: changesets/action@v1
        with:
          commit: 'chore(release): version packages'
          title: 'chore(release): version packages'
          version: pnpm run version
          publish: pnpm run release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          UPDATE_REPO_SSH_KEY: ${{ secrets.UPDATE_REPO_SSH_KEY }}

      - name: Check if eslint config was released
        run: echo "PACKAGE_RELEASED=$(echo ${{ steps.changesets.outputs.publishedPackages }} | jq -r 'map(select(.name == "@momo/eslint-config")) | length > 0')" >> $GITHUB_ENV

      - name: Sync repo if needed
        if: env.PACKAGE_RELEASED == 'true'
        run: pnpm -r run postpublish
