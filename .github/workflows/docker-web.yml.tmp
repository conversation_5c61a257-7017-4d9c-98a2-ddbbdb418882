# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

name: Build & Deploy Web

on:
  push:
    branches: ['main']
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - '.github/workflows/docker-web.yml'
      - '.env.production'
  pull_request:
    branches: ['main']
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - '.github/workflows/docker-web.yml'
      - '.env.production'

env:
  TZ: Asia/Shanghai
  docker-registry: ghcr.io
  docker-username: ${{ github.actor }}
  docker-password: ${{ secrets.PACKAGE_TOKEN }}
  docker-namespace: fechin
  docker-image: proxyorb
  docker-version: latest
  cloudflare-zone-id: 9b2533c4230d69c1e969dd92d870c436

permissions:
  contents: read
  packages: write

jobs:
  build:
    name: Build Web Image
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker BuildX
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.docker-registry }}
          username: ${{ env.docker-username }}
          password: ${{ env.docker-password }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.docker-registry }}/${{ env.docker-namespace }}/${{ env.docker-image }}
          tags: |
            type=raw,value=latest
            type=sha,format=short

      - name: Build && Push Images
        uses: docker/build-push-action@v6
        with:
          context: ./
          push: true
          platforms: linux/amd64,linux/arm64
          file: ./apps/web/Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # 添加缓存配置
          cache-from: type=gha,scope=${{ env.docker-image }}-build-cache
          cache-to: type=gha,mode=max,scope=${{ env.docker-image }}-build-cache

  purge-cache:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Purge Cache in Cloudflare
        run: |
          curl -X POST "https://api.cloudflare.com/client/v4/zones/${{ env.cloudflare-zone-id }}/purge_cache" \
              -H "X-Auth-Email: <EMAIL>" \
              -H "X-Auth-Key: 4d54dc0c5e85c379d4930209f112fb8678246" \
              -H "Content-Type: application/json" \
              -d '{"purge_everything":true}'
