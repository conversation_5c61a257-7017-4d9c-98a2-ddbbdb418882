# -This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

name: Build & Deploy Nginx Lua

on:
  push:
    branches: ['main']
    paths:
      - 'apps/nginx-lua/**'
      - 'apps/service-worker/**'
      - 'docker-compose.yml'
      - '.github/workflows/docker-nginx-lua.yml'
  pull_request:
    branches: ['main']
    paths:
      - 'apps/nginx-lua/**'
      - 'apps/service-worker/**'
      - 'docker-compose.yml'
      - '.github/workflows/docker-nginx-lua.yml'

env:
  TZ: Asia/Shanghai
  docker-registry: ghcr.io
  docker-username: ${{ github.actor }}
  docker-password: ${{ secrets.PACKAGE_TOKEN }}
  docker-namespace: fechin
  docker-image: proxyorb-nginx
  docker-version: latest

permissions:
  contents: read
  packages: write

jobs:
  build:
    name: Build Nginx Lua Image
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup
        uses: ./.github/actions/setup

      - name: Build Service Worker
        shell: bash
        run: |
          mkdir -p apps/nginx-lua/html
          pnpm build:sw
          tree apps/nginx-lua

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker BuildX
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.docker-registry }}
          username: ${{ env.docker-username }}
          password: ${{ env.docker-password }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.docker-registry }}/${{ env.docker-namespace }}/${{ env.docker-image }}
          tags: |
            type=raw,value=latest
            type=sha,format=short,prefix=

      - name: Build && Push Images
        uses: docker/build-push-action@v6
        with:
          context: ./apps/nginx-lua
          file: ./apps/nginx-lua/Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # 添加缓存配置
          cache-from: type=gha,scope=${{ env.docker-image }}-build-cache
          cache-to: type=gha,mode=max,scope=${{ env.docker-image }}-build-cache

    #   - name: Cleanup old versions
    #     uses: dataaxiom/ghcr-cleanup-action@v1
    #     with:
    #       package: ${{ env.docker-image }}
    #       owner: ${{ env.docker-namespace }}
    #       token: ${{ secrets.GITHUB_TOKEN }}
    #       exclude-tags: latest
    #       keep-n-tagged: 6
