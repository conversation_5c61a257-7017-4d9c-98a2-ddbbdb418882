name: Check setup
description: Setup Node.js, pnpm, and install dependencies

runs:
  using: composite
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20

    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        run_install: false

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - name: Setup pnpm cache
      uses: actions/cache@v4
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Cache for Turbo
      uses: rharkor/caching-for-turbo@v1.5

    - name: Install dependencies
      shell: bash
      run: pnpm install --no-frozen-lockfile
