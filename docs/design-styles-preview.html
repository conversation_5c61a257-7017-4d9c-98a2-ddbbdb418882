<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OnlineVPN Design Styles Preview</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        background: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .style-section {
        margin-bottom: 60px;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .style-header {
        padding: 20px;
        background: #333;
        color: white;
        text-align: center;
      }

      .style-preview {
        height: 600px;
        overflow-y: auto;
      }

      /* Style 1: Cyberpunk/Gaming Dark Theme */
      .style-1 {
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        color: #00ff88;
        font-family: 'Courier New', monospace;
      }

      .style-1 .hero {
        text-align: center;
        padding: 60px 20px;
        position: relative;
      }

      .style-1 .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%2300ff88" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.1;
      }

      .style-1 h1 {
        font-size: 3rem;
        text-shadow: 0 0 20px #00ff88;
        margin-bottom: 20px;
        position: relative;
      }

      .style-1 .cta-button {
        background: linear-gradient(45deg, #ff0080, #00ff88);
        border: none;
        padding: 15px 40px;
        color: black;
        font-weight: bold;
        border-radius: 0;
        text-transform: uppercase;
        letter-spacing: 2px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .style-1 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        padding: 40px 20px;
      }

      .style-1 .feature-card {
        background: rgba(0, 255, 136, 0.1);
        border: 1px solid #00ff88;
        padding: 30px;
        text-align: center;
        position: relative;
      }

      .style-1 .feature-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #ff0080, #00ff88, #0080ff);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s;
      }

      .style-1 .feature-card:hover::before {
        opacity: 1;
      }

      /* Style 2: Minimalist Brutalist */
      .style-2 {
        background: #f8f8f8;
        color: #000;
        font-family: 'Arial Black', Arial, sans-serif;
      }

      .style-2 .hero {
        background: #000;
        color: #fff;
        padding: 80px 20px;
        text-align: left;
      }

      .style-2 h1 {
        font-size: 4rem;
        font-weight: 900;
        line-height: 0.9;
        margin-bottom: 30px;
        text-transform: uppercase;
      }

      .style-2 .cta-button {
        background: #ff3333;
        color: white;
        border: none;
        padding: 20px 60px;
        font-size: 1.2rem;
        font-weight: 900;
        text-transform: uppercase;
        cursor: pointer;
        transform: skew(-5deg);
      }

      .style-2 .features {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0;
      }

      .style-2 .feature-card {
        padding: 60px 40px;
        border: 4px solid #000;
        background: white;
        text-align: left;
      }

      .style-2 .feature-card:nth-child(odd) {
        background: #000;
        color: #fff;
      }

      .style-2 .feature-card h3 {
        font-size: 2rem;
        margin-bottom: 20px;
        text-transform: uppercase;
      }

      /* Style 3: Glassmorphism/Modern */
      .style-3 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-family:
          'SF Pro Display',
          -apple-system,
          sans-serif;
      }

      .style-3 .hero {
        padding: 80px 20px;
        text-align: center;
        backdrop-filter: blur(10px);
      }

      .style-3 h1 {
        font-size: 3.5rem;
        font-weight: 300;
        margin-bottom: 30px;
        background: linear-gradient(45deg, #fff, #a8edea);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .style-3 .cta-button {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 15px 40px;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .style-3 .cta-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .style-3 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        padding: 60px 20px;
      }

      .style-3 .feature-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        transition: transform 0.3s ease;
      }

      .style-3 .feature-card:hover {
        transform: translateY(-10px);
        background: rgba(255, 255, 255, 0.15);
      }

      /* Style 4: Retro/Vintage 80s */
      .style-4 {
        background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff);
        color: white;
        font-family:
          'Impact',
          Arial Black,
          sans-serif;
      }

      .style-4 .hero {
        padding: 80px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .style-4 .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
          90deg,
          transparent,
          transparent 2px,
          rgba(255, 255, 255, 0.1) 2px,
          rgba(255, 255, 255, 0.1) 4px
        );
      }

      .style-4 h1 {
        font-size: 4rem;
        text-transform: uppercase;
        letter-spacing: 5px;
        margin-bottom: 20px;
        text-shadow:
          4px 4px 0px #000,
          8px 8px 0px rgba(0, 0, 0, 0.3);
        position: relative;
      }

      .style-4 .cta-button {
        background: linear-gradient(45deg, #ffbe0b, #fb5607);
        border: 4px solid #000;
        color: #000;
        padding: 20px 50px;
        font-size: 1.3rem;
        font-weight: 900;
        text-transform: uppercase;
        cursor: pointer;
        transform: perspective(500px) rotateX(15deg);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
      }

      .style-4 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        padding: 60px 20px;
      }

      .style-4 .feature-card {
        background: linear-gradient(135deg, #ff006e, #8338ec);
        border: 3px solid #ffbe0b;
        padding: 30px;
        text-align: center;
        transform: skew(-5deg);
        transition: transform 0.3s ease;
      }

      .style-4 .feature-card:hover {
        transform: skew(-5deg) scale(1.05);
      }

      .style-4 .feature-card h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        text-shadow: 2px 2px 0px #000;
      }

      /* Style 5: Corporate/Professional */
      .style-5 {
        background: #f8fafc;
        color: #1e293b;
        font-family:
          'Inter',
          -apple-system,
          sans-serif;
      }

      .style-5 .hero {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 100px 20px;
        text-align: center;
      }

      .style-5 h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 25px;
        line-height: 1.2;
      }

      .style-5 .hero p {
        font-size: 1.25rem;
        margin-bottom: 40px;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .style-5 .cta-button {
        background: #ffffff;
        color: #1e40af;
        border: none;
        padding: 16px 32px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .style-5 .cta-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
      }

      .style-5 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        padding: 80px 20px;
        max-width: 1200px;
        margin: 0 auto;
      }

      .style-5 .feature-card {
        background: white;
        border-radius: 12px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
      }

      .style-5 .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
      }

      .style-5 .feature-card h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 16px;
        color: #1e40af;
      }

      .style-5 .feature-card p {
        color: #64748b;
        line-height: 1.6;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 style="text-align: center; margin-bottom: 40px; color: #333">
        OnlineVPN Design Styles Preview
      </h1>

      <!-- Style 1: Cyberpunk/Gaming Dark Theme -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 1: Cyberpunk/Gaming Dark Theme</h2>
          <p>Target: Gaming community, tech enthusiasts, privacy-focused users</p>
        </div>
        <div class="style-preview style-1">
          <div class="hero">
            <h1>ONLINE VPN</h1>
            <p style="font-size: 1.2rem; margin-bottom: 30px; color: #00ff88">
              SECURE • ANONYMOUS • UNLIMITED
            </p>
            <button class="cta-button">CONNECT NOW</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <h3>MILITARY ENCRYPTION</h3>
              <p>AES-256 bit encryption protects your data from cyber threats</p>
            </div>
            <div class="feature-card">
              <h3>ZERO LOGS</h3>
              <p>We don't track, store, or share your browsing activity</p>
            </div>
            <div class="feature-card">
              <h3>GLOBAL SERVERS</h3>
              <p>100+ servers worldwide for optimal speed and access</p>
            </div>
            <div class="feature-card">
              <h3>24/7 SUPPORT</h3>
              <p>Expert technical support available around the clock</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 2: Minimalist Brutalist -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 2: Minimalist Brutalist</h2>
          <p>Target: Design-conscious users, professionals, creative industry</p>
        </div>
        <div class="style-preview style-2">
          <div class="hero">
            <h1>ONLINE<br />VPN</h1>
            <p style="font-size: 1.5rem; margin-bottom: 40px; font-weight: 300">
              Privacy without compromise.<br />Speed without limits.
            </p>
            <button class="cta-button">GET STARTED</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <h3>FAST</h3>
              <p>
                Lightning-fast connections with unlimited bandwidth. No throttling, no restrictions.
              </p>
            </div>
            <div class="feature-card">
              <h3>SECURE</h3>
              <p>
                Bank-level encryption keeps your data safe from prying eyes and malicious actors.
              </p>
            </div>
            <div class="feature-card">
              <h3>PRIVATE</h3>
              <p>
                Strict no-logs policy ensures your online activity remains completely anonymous.
              </p>
            </div>
            <div class="feature-card">
              <h3>GLOBAL</h3>
              <p>Access content from anywhere with our worldwide network of premium servers.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 3: Glassmorphism/Modern -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 3: Glassmorphism/Modern</h2>
          <p>Target: Premium users, Apple ecosystem users, modern professionals</p>
        </div>
        <div class="style-preview style-3">
          <div class="hero">
            <h1>OnlineVPN</h1>
            <p style="font-size: 1.3rem; margin-bottom: 40px; opacity: 0.9">
              Experience the internet without boundaries
            </p>
            <button class="cta-button">Start Free Trial</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div style="font-size: 2rem; margin-bottom: 20px">🔒</div>
              <h3>Advanced Security</h3>
              <p>
                Military-grade encryption protects your data and ensures complete privacy online.
              </p>
            </div>
            <div class="feature-card">
              <div style="font-size: 2rem; margin-bottom: 20px">⚡</div>
              <h3>Lightning Speed</h3>
              <p>
                Optimized servers deliver blazing-fast speeds for streaming, gaming, and browsing.
              </p>
            </div>
            <div class="feature-card">
              <div style="font-size: 2rem; margin-bottom: 20px">🌍</div>
              <h3>Global Access</h3>
              <p>
                Connect to servers in 50+ countries and access content from anywhere in the world.
              </p>
            </div>
            <div class="feature-card">
              <div style="font-size: 2rem; margin-bottom: 20px">📱</div>
              <h3>All Devices</h3>
              <p>
                Protect all your devices with our apps for Windows, Mac, iOS, Android, and more.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 4: Retro/Vintage 80s -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 4: Retro/Vintage 80s</h2>
          <p>Target: Nostalgic users, creative professionals, entertainment industry</p>
        </div>
        <div class="style-preview style-4">
          <div class="hero">
            <h1>ONLINE VPN</h1>
            <p
              style="
                font-size: 1.4rem;
                margin-bottom: 40px;
                text-transform: uppercase;
                letter-spacing: 3px;
              "
            >
              RADICAL PRIVACY • TOTALLY SECURE
            </p>
            <button class="cta-button">GET CONNECTED</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <h3>MEGA FAST</h3>
              <p>Blazing speeds that'll blow your mind. Stream, game, and surf without limits.</p>
            </div>
            <div class="feature-card">
              <h3>ULTRA SECURE</h3>
              <p>Military-grade encryption keeps the bad guys out of your business.</p>
            </div>
            <div class="feature-card">
              <h3>TOTALLY PRIVATE</h3>
              <p>Zero logs, zero tracking. Your secrets stay secret, guaranteed.</p>
            </div>
            <div class="feature-card">
              <h3>WORLDWIDE</h3>
              <p>Servers across the globe. Access anything, anywhere, anytime.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 5: Corporate/Professional -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 5: Corporate/Professional</h2>
          <p>Target: Business users, enterprises, professional services</p>
        </div>
        <div class="style-preview style-5">
          <div class="hero">
            <h1>OnlineVPN</h1>
            <p>
              Enterprise-grade security and privacy solutions for modern businesses and
              professionals.
            </p>
            <button class="cta-button">Start Free Trial</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <h3>Enterprise Security</h3>
              <p>
                Bank-level encryption and advanced security protocols protect your sensitive
                business data from cyber threats.
              </p>
            </div>
            <div class="feature-card">
              <h3>Global Infrastructure</h3>
              <p>
                Premium servers in 50+ countries ensure reliable, high-speed connections for your
                international operations.
              </p>
            </div>
            <div class="feature-card">
              <h3>24/7 Expert Support</h3>
              <p>
                Dedicated technical support team available around the clock to assist with any
                connectivity issues.
              </p>
            </div>
            <div class="feature-card">
              <h3>Compliance Ready</h3>
              <p>
                GDPR, HIPAA, and SOC 2 compliant infrastructure meets the highest industry standards
                for data protection.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
