<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Free Online VPN & Web Proxy Browser - OnlineVPN</title>
    <meta
      name="description"
      content="Free online VPN and web proxy browser by OnlineVPN. Access blocked websites securely with our advanced proxy VPN technology. Military-grade encryption, instant access, no download required."
    />
    <meta
      name="keywords"
      content="online vpn, free online vpn, online vpn browser, free proxy vpn, web proxy browser, proxy vpn service, secure vpn proxy"
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://onlinevpn.app/" />
    <meta property="og:title" content="Free Online VPN & Web Proxy Browser - OnlineVPN" />
    <meta
      property="og:description"
      content="Free online VPN and web proxy browser. Access blocked websites securely with military-grade encryption. No download required."
    />
    <meta property="og:image" content="https://onlinevpn.app/images/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://onlinevpn.app/" />
    <meta property="twitter:title" content="Free Online VPN & Web Proxy Browser - OnlineVPN" />
    <meta
      property="twitter:description"
      content="Free online VPN and web proxy browser. Access blocked websites securely with military-grade encryption."
    />
    <meta property="twitter:image" content="https://onlinevpn.app/images/og-image.png" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://onlinevpn.app/" />

    <!-- Schema.org structured data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.8",
          "reviewCount": "2847"
        },
        "applicationCategory": "VPN Service",
        "description": "Free online VPN and web proxy browser with advanced proxy VPN technology for secure internet access",
        "features": [
          "Free Online VPN",
          "Web Proxy Browser",
          "Proxy VPN Service",
          "Military-Grade Encryption",
          "No Download Required"
        ],
        "name": "OnlineVPN - Free Online VPN & Web Proxy Browser",
        "offers": {
          "@type": "Offer",
          "availability": "https://schema.org/InStock",
          "price": "0",
          "priceCurrency": "USD"
        },
        "operatingSystem": "Web Browser"
      }
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'geo-primary': '#667eea',
              'geo-secondary': '#764ba2',
              'geo-accent': '#f093fb',
              'geo-dark': '#1a1a2e',
              'geo-light': '#16213e',
              'geo-text': '#ffffff',
              'geo-text-muted': 'rgba(255, 255, 255, 0.7)'
            },
            animation: {
              shimmer: 'shimmer 2s ease-in-out infinite',
              float: 'float 6s ease-in-out infinite',
              'rotate-slow': 'rotate 20s linear infinite',
              'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate'
            },
            keyframes: {
              shimmer: {
                '0%': { transform: 'translateX(-100%) translateY(-100%) rotate(45deg)' },
                '100%': { transform: 'translateX(100%) translateY(100%) rotate(45deg)' }
              },
              float: {
                '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                '50%': { transform: 'translateY(-20px) rotate(180deg)' }
              },
              rotate: {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              },
              'pulse-glow': {
                '0%': { boxShadow: '0 0 20px rgba(102, 126, 234, 0.5)' },
                '100%': {
                  boxShadow: '0 0 40px rgba(102, 126, 234, 0.8), 0 0 60px rgba(102, 126, 234, 0.6)'
                }
              }
            }
          }
        }
      }
    </script>
    <style>
      .geometric-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
      }
      .geometric-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="20,10 40,30 20,50 0,30" fill="%23ffffff" opacity="0.05"/><polygon points="80,20 100,40 80,60 60,40" fill="%23ffffff" opacity="0.05"/><polygon points="50,60 70,80 50,100 30,80" fill="%23ffffff" opacity="0.05"/></svg>');
      }
      .clip-polygon {
        clip-path: polygon(0% 0%, 90% 0%, 100% 20%, 100% 100%, 10% 100%, 0% 80%);
      }
      .clip-diamond {
        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
      }
      .clip-hexagon {
        clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
      }
      .clip-arrow {
        clip-path: polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%);
      }
      .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
    </style>
  </head>
  <body class="bg-geo-dark text-geo-text overflow-x-hidden">
    <!-- Navigation -->
    <nav class="geometric-bg relative z-50">
      <div class="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <div class="bg-geo-text clip-diamond animate-rotate-slow mr-4 h-10 w-10"></div>
            <h1 class="text-geo-text text-xl font-bold">ONLINE VPN</h1>
          </div>
          <div class="hidden items-center space-x-8 md:flex">
            <a
              href="#features"
              class="text-geo-text-muted hover:text-geo-text font-medium transition-colors"
              >FEATURES</a
            >
            <a
              href="#how-it-works"
              class="text-geo-text-muted hover:text-geo-text font-medium transition-colors"
              >HOW IT WORKS</a
            >
            <a
              href="#use-cases"
              class="text-geo-text-muted hover:text-geo-text font-medium transition-colors"
              >USE CASES</a
            >
          </div>
          <div class="flex items-center space-x-4">
            <button
              id="nav-start-vpn"
              class="glass-effect text-geo-text clip-arrow px-6 py-2 font-medium transition-all hover:bg-white/20"
            >
              START FREE VPN
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="geometric-bg relative py-20">
      <!-- Floating Geometric Elements -->
      <div class="pointer-events-none absolute inset-0 overflow-hidden">
        <div
          class="bg-geo-accent/20 clip-hexagon animate-float absolute left-10 top-20 h-20 w-20"
        ></div>
        <div
          class="bg-geo-text/10 clip-diamond animate-float absolute right-20 top-40 h-16 w-16"
          style="animation-delay: 2s"
        ></div>
        <div
          class="bg-geo-primary/30 clip-polygon animate-float absolute bottom-40 left-1/4 h-12 w-12"
          style="animation-delay: 4s"
        ></div>
        <div
          class="bg-geo-secondary/20 clip-hexagon animate-float absolute bottom-20 right-1/3 h-24 w-24"
          style="animation-delay: 1s"
        ></div>
      </div>

      <div class="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <!-- Hero Geometric Shape -->
          <div class="relative mx-auto mb-12 h-40 w-40">
            <div
              class="glass-effect clip-hexagon animate-pulse-glow flex h-full w-full items-center justify-center"
            >
              <div
                class="from-geo-primary to-geo-accent clip-diamond animate-rotate-slow h-20 w-20 bg-gradient-to-br"
              ></div>
            </div>
          </div>

          <h1
            class="text-geo-text mb-8 text-4xl font-light leading-tight tracking-wider md:text-6xl"
          >
            FREE ONLINE VPN &<br />
            <span class="text-geo-accent">WEB PROXY BROWSER</span>
          </h1>

          <p
            class="text-geo-text-muted mx-auto mb-12 max-w-3xl text-xl leading-relaxed tracking-wide"
          >
            GEOMETRIC SECURITY • MATHEMATICAL PRIVACY<br />
            Access any website securely with our advanced online VPN browser technology
          </p>

          <!-- Geometric Input Section -->
          <div class="mx-auto mb-12 max-w-2xl">
            <div class="glass-effect clip-polygon relative overflow-hidden p-8">
              <!-- Shimmer Effect -->
              <div
                class="absolute inset-0 opacity-0 transition-opacity duration-500 hover:opacity-100"
              >
                <div
                  class="animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                ></div>
              </div>

              <div class="relative z-10 flex flex-col gap-4 sm:flex-row">
                <input
                  id="url-input"
                  type="url"
                  placeholder="ENTER WEBSITE URL TO ACCESS WITH FREE VPN..."
                  class="border-geo-text/30 text-geo-text placeholder-geo-text-muted focus:border-geo-accent focus:ring-geo-accent clip-arrow flex-1 border bg-transparent px-6 py-4 transition-colors focus:border-2 focus:outline-none focus:ring-2 focus:ring-opacity-30"
                />
                <button
                  id="connect-vpn-btn"
                  class="from-geo-primary to-geo-accent text-geo-text clip-arrow hover:from-geo-accent hover:to-geo-primary whitespace-nowrap bg-gradient-to-r px-8 py-4 font-medium transition-all"
                >
                  CONNECT FREE VPN
                </button>
              </div>
              <div
                class="text-geo-text-muted mt-6 flex flex-wrap justify-center gap-6 text-sm tracking-wider"
              >
                <span>◆ FREE ONLINE VPN</span>
                <span>◆ NO DOWNLOAD REQUIRED</span>
                <span>◆ MILITARY-GRADE ENCRYPTION</span>
                <span>◆ UNLIMITED BANDWIDTH</span>
              </div>
            </div>
          </div>

          <!-- Secondary CTA -->
          <div class="flex flex-col justify-center gap-6 sm:flex-row">
            <button
              class="glass-effect text-geo-text clip-polygon px-10 py-4 font-medium tracking-wider transition-all hover:bg-white/20"
            >
              EXPLORE MATRIX
            </button>
            <button
              class="from-geo-secondary to-geo-primary text-geo-text clip-arrow hover:from-geo-primary hover:to-geo-secondary bg-gradient-to-r px-10 py-4 font-medium tracking-wider transition-all"
            >
              ANALYZE SYSTEM
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="bg-geo-dark relative py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-geo-text mb-4 text-3xl font-light tracking-wider">
            WHY CHOOSE OUR FREE ONLINE VPN
          </h2>
          <p class="text-geo-text-muted mx-auto max-w-2xl text-lg tracking-wide">
            Advanced geometric security algorithms protecting your online privacy with mathematical
            precision
          </p>
        </div>

        <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          <!-- Feature Card 1 -->
          <div
            class="glass-effect clip-polygon group relative overflow-hidden p-8 text-center transition-all duration-300 hover:bg-white/15"
          >
            <div
              class="absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
            >
              <div
                class="via-geo-accent/20 animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent to-transparent"
              ></div>
            </div>

            <div
              class="from-geo-primary to-geo-accent clip-hexagon relative z-10 mx-auto mb-6 h-16 w-16 bg-gradient-to-br"
            >
              <div class="flex h-full w-full items-center justify-center">
                <svg
                  class="text-geo-text h-8 w-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <polygon points="12,2 22,8.5 22,15.5 12,22 2,15.5 2,8.5 12,2" />
                  <polygon points="12,8 18,12 12,16 6,12 12,8" />
                </svg>
              </div>
            </div>
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">
              MILITARY-GRADE ENCRYPTION
            </h3>
            <p class="text-geo-text-muted text-sm leading-relaxed">
              Our free online VPN uses advanced encryption algorithms to protect your data with
              geometric precision and mathematical security.
            </p>
          </div>

          <!-- Feature Card 2 -->
          <div
            class="glass-effect clip-polygon group relative overflow-hidden p-8 text-center transition-all duration-300 hover:bg-white/15"
          >
            <div
              class="absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
            >
              <div
                class="via-geo-accent/20 animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent to-transparent"
              ></div>
            </div>

            <div
              class="from-geo-secondary to-geo-primary clip-hexagon relative z-10 mx-auto mb-6 h-16 w-16 bg-gradient-to-br"
            >
              <div class="flex h-full w-full items-center justify-center">
                <svg
                  class="text-geo-text h-8 w-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <polygon points="1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2 1,6" />
                  <line x1="8" y1="2" x2="8" y2="18" />
                  <line x1="16" y1="6" x2="16" y2="22" />
                </svg>
              </div>
            </div>
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">VECTOR SPEED</h3>
            <p class="text-geo-text-muted text-sm leading-relaxed">
              Optimized pathways deliver data at light speed through our abstract network topology.
            </p>
          </div>

          <!-- Feature Card 3 -->
          <div
            class="glass-effect clip-polygon group relative overflow-hidden p-8 text-center transition-all duration-300 hover:bg-white/15"
          >
            <div
              class="absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
            >
              <div
                class="via-geo-accent/20 animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent to-transparent"
              ></div>
            </div>

            <div
              class="from-geo-accent to-geo-secondary clip-hexagon relative z-10 mx-auto mb-6 h-16 w-16 bg-gradient-to-br"
            >
              <div class="flex h-full w-full items-center justify-center">
                <svg
                  class="text-geo-text h-8 w-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <circle cx="12" cy="12" r="3" />
                  <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
                </svg>
              </div>
            </div>
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">INFINITE NETWORK</h3>
            <p class="text-geo-text-muted text-sm leading-relaxed">
              Boundless connectivity through our mathematically perfect server constellation.
            </p>
          </div>

          <!-- Feature Card 4 -->
          <div
            class="glass-effect clip-polygon group relative overflow-hidden p-8 text-center transition-all duration-300 hover:bg-white/15"
          >
            <div
              class="absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
            >
              <div
                class="via-geo-accent/20 animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent to-transparent"
              ></div>
            </div>

            <div
              class="from-geo-primary to-geo-accent clip-hexagon relative z-10 mx-auto mb-6 h-16 w-16 bg-gradient-to-br"
            >
              <div class="flex h-full w-full items-center justify-center">
                <svg
                  class="text-geo-text h-8 w-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"
                  />
                </svg>
              </div>
            </div>
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">PRECISION SUPPORT</h3>
            <p class="text-geo-text-muted text-sm leading-relaxed">
              Expertly calibrated assistance with surgical precision for optimal performance.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Network Section -->
    <section id="network" class="geometric-bg relative py-20">
      <div class="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-geo-text mb-4 text-3xl font-light tracking-wider">
            NETWORK ARCHITECTURE
          </h2>
          <p class="text-geo-text-muted mx-auto max-w-3xl text-lg tracking-wide">
            Three-dimensional security protocols engineered for maximum efficiency
          </p>
        </div>

        <div class="grid grid-cols-1 gap-12 md:grid-cols-3">
          <!-- Step 1 -->
          <div class="text-center">
            <div class="glass-effect clip-hexagon relative mx-auto mb-8 h-24 w-24">
              <div class="flex h-full w-full items-center justify-center">
                <div
                  class="from-geo-primary to-geo-accent clip-diamond animate-pulse-glow h-8 w-8 bg-gradient-to-br"
                ></div>
              </div>
              <div
                class="bg-geo-accent clip-diamond animate-float absolute -right-2 -top-2 h-6 w-6"
              ></div>
            </div>
            <h3 class="text-geo-text mb-4 text-xl font-medium tracking-wider">INPUT COORDINATES</h3>
            <p class="text-geo-text-muted leading-relaxed">
              Initialize your destination parameters into our quantum-encrypted input matrix for
              processing.
            </p>
          </div>

          <!-- Step 2 -->
          <div class="text-center">
            <div class="glass-effect clip-hexagon relative mx-auto mb-8 h-24 w-24">
              <div class="flex h-full w-full items-center justify-center">
                <div
                  class="from-geo-secondary to-geo-primary clip-diamond animate-pulse-glow h-8 w-8 bg-gradient-to-br"
                  style="animation-delay: 1s"
                ></div>
              </div>
              <div
                class="bg-geo-primary clip-diamond animate-float absolute -right-2 -top-2 h-6 w-6"
                style="animation-delay: 2s"
              ></div>
            </div>
            <h3 class="text-geo-text mb-4 text-xl font-medium tracking-wider">ROUTE CALCULATION</h3>
            <p class="text-geo-text-muted leading-relaxed">
              Advanced algorithms compute optimal pathways through our geometric server
              constellation.
            </p>
          </div>

          <!-- Step 3 -->
          <div class="text-center">
            <div class="glass-effect clip-hexagon relative mx-auto mb-8 h-24 w-24">
              <div class="flex h-full w-full items-center justify-center">
                <div
                  class="from-geo-accent to-geo-secondary clip-diamond animate-pulse-glow h-8 w-8 bg-gradient-to-br"
                  style="animation-delay: 2s"
                ></div>
              </div>
              <div
                class="bg-geo-secondary clip-diamond animate-float absolute -right-2 -top-2 h-6 w-6"
                style="animation-delay: 4s"
              ></div>
            </div>
            <h3 class="text-geo-text mb-4 text-xl font-medium tracking-wider">
              SECURE TRANSMISSION
            </h3>
            <p class="text-geo-text-muted leading-relaxed">
              Data flows through crystalline security layers with mathematical precision and
              absolute privacy.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="bg-geo-light relative py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-geo-text mb-4 text-2xl font-light tracking-wider">SYSTEM METRICS</h2>
        </div>
        <div class="grid grid-cols-2 gap-8 md:grid-cols-4">
          <div class="glass-effect clip-polygon p-8 text-center">
            <div class="text-geo-accent mb-2 text-3xl font-light tracking-wider">10M+</div>
            <div class="text-geo-text-muted text-sm tracking-wider">ACTIVE NODES</div>
          </div>
          <div class="glass-effect clip-polygon p-8 text-center">
            <div class="text-geo-accent mb-2 text-3xl font-light tracking-wider">50+</div>
            <div class="text-geo-text-muted text-sm tracking-wider">DIMENSIONS</div>
          </div>
          <div class="glass-effect clip-polygon p-8 text-center">
            <div class="text-geo-accent mb-2 text-3xl font-light tracking-wider">99.9%</div>
            <div class="text-geo-text-muted text-sm tracking-wider">UPTIME</div>
          </div>
          <div class="glass-effect clip-polygon p-8 text-center">
            <div class="text-geo-accent mb-2 text-3xl font-light tracking-wider">∞</div>
            <div class="text-geo-text-muted text-sm tracking-wider">BANDWIDTH</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Security Section -->
    <section id="security" class="bg-geo-dark relative py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 items-center gap-16 lg:grid-cols-2">
          <div>
            <h2 class="text-geo-text mb-8 text-3xl font-light tracking-wider">
              QUANTUM SECURITY MATRIX
            </h2>
            <div class="space-y-6">
              <div class="glass-effect clip-arrow p-6">
                <div class="flex items-start gap-4">
                  <div class="bg-geo-accent clip-diamond mt-1 h-8 w-8 flex-shrink-0"></div>
                  <div>
                    <h3 class="text-geo-text mb-2 text-lg font-medium tracking-wide">
                      ZERO-KNOWLEDGE ARCHITECTURE
                    </h3>
                    <p class="text-geo-text-muted leading-relaxed">
                      Advanced cryptographic protocols ensure complete data anonymity through
                      mathematical abstraction.
                    </p>
                  </div>
                </div>
              </div>

              <div class="glass-effect clip-arrow p-6">
                <div class="flex items-start gap-4">
                  <div class="bg-geo-primary clip-diamond mt-1 h-8 w-8 flex-shrink-0"></div>
                  <div>
                    <h3 class="text-geo-text mb-2 text-lg font-medium tracking-wide">
                      GEOMETRIC ENCRYPTION
                    </h3>
                    <p class="text-geo-text-muted leading-relaxed">
                      Multi-dimensional security layers protect your digital footprint with
                      algorithmic precision.
                    </p>
                  </div>
                </div>
              </div>

              <div class="glass-effect clip-arrow p-6">
                <div class="flex items-start gap-4">
                  <div class="bg-geo-secondary clip-diamond mt-1 h-8 w-8 flex-shrink-0"></div>
                  <div>
                    <h3 class="text-geo-text mb-2 text-lg font-medium tracking-wide">
                      INFINITE SCALABILITY
                    </h3>
                    <p class="text-geo-text-muted leading-relaxed">
                      Fractal network architecture adapts dynamically to provide unlimited secure
                      connections.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="glass-effect clip-polygon relative overflow-hidden p-12 text-center">
            <div class="absolute inset-0 opacity-30">
              <div
                class="via-geo-accent/20 animate-shimmer absolute inset-0 bg-gradient-to-r from-transparent to-transparent"
              ></div>
            </div>

            <div
              class="from-geo-primary via-geo-accent to-geo-secondary clip-hexagon animate-rotate-slow relative z-10 mx-auto mb-8 h-32 w-32 bg-gradient-to-br"
            ></div>
            <h3 class="text-geo-text mb-6 text-2xl font-light tracking-wider">
              INITIALIZE CONNECTION
            </h3>
            <p class="text-geo-text-muted mb-8 text-lg leading-relaxed">
              Enter the geometric dimension of absolute privacy and unlimited access.
            </p>
            <button
              class="from-geo-primary to-geo-accent text-geo-text clip-arrow hover:from-geo-accent hover:to-geo-primary bg-gradient-to-r px-10 py-4 font-medium tracking-wider transition-all"
            >
              ACTIVATE MATRIX
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="bg-geo-light relative py-20">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-geo-text mb-4 text-3xl font-light tracking-wider">
            FREQUENTLY ASKED QUESTIONS
          </h2>
          <p class="text-geo-text-muted mx-auto max-w-2xl text-lg tracking-wide">
            Everything you need to know about our free online VPN and web proxy browser
          </p>
        </div>

        <div class="space-y-6">
          <!-- FAQ Item 1 -->
          <div class="glass-effect clip-polygon p-6">
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">
              WHAT IS A FREE ONLINE VPN?
            </h3>
            <p class="text-geo-text-muted leading-relaxed">
              A free online VPN is a web-based virtual private network service that encrypts your
              internet connection and hides your IP address without requiring any software
              downloads. Our online VPN browser provides instant secure access to blocked websites
              with military-grade encryption.
            </p>
          </div>

          <!-- FAQ Item 2 -->
          <div class="glass-effect clip-polygon p-6">
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">
              HOW DOES THE ONLINE VPN BROWSER WORK?
            </h3>
            <p class="text-geo-text-muted leading-relaxed">
              Our online VPN browser works by routing your internet traffic through secure proxy
              servers using advanced geometric algorithms. Simply enter a website URL, click
              "Connect Free VPN", and start browsing with complete privacy protection.
            </p>
          </div>

          <!-- FAQ Item 3 -->
          <div class="glass-effect clip-polygon p-6">
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">
              IS THE FREE PROXY VPN SERVICE REALLY FREE?
            </h3>
            <p class="text-geo-text-muted leading-relaxed">
              Yes, our proxy VPN service is completely free with no hidden fees, registration
              requirements, or time limits. You get unlimited bandwidth and access to our global
              network of VPN servers at no cost.
            </p>
          </div>

          <!-- FAQ Item 4 -->
          <div class="glass-effect clip-polygon p-6">
            <h3 class="text-geo-text mb-3 text-lg font-medium tracking-wider">
              CAN I USE THE ONLINE VPN ON MOBILE DEVICES?
            </h3>
            <p class="text-geo-text-muted leading-relaxed">
              Absolutely! Our online VPN browser works on all devices including smartphones,
              tablets, laptops, and desktop computers. The web proxy browser is fully responsive and
              optimized for mobile use.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-geo-light relative py-16">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="glass-effect clip-polygon p-12">
          <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
            <div>
              <div class="mb-6 flex items-center">
                <div class="bg-geo-accent clip-diamond animate-rotate-slow mr-3 h-8 w-8"></div>
                <h3 class="text-geo-text text-lg font-medium tracking-wider">ONLINE VPN</h3>
              </div>
              <p class="text-geo-text-muted mb-6 leading-relaxed">
                Geometric security protocols for the digital dimension.
              </p>
            </div>

            <div>
              <h4 class="text-geo-text mb-6 text-sm font-medium tracking-wider">SYSTEM</h4>
              <ul class="text-geo-text-muted space-y-3 text-sm">
                <li><a href="#" class="hover:text-geo-accent transition-colors">MODULES</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">NETWORK</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">SECURITY</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">MATRIX</a></li>
              </ul>
            </div>

            <div>
              <h4 class="text-geo-text mb-6 text-sm font-medium tracking-wider">SUPPORT</h4>
              <ul class="text-geo-text-muted space-y-3 text-sm">
                <li>
                  <a href="#" class="hover:text-geo-accent transition-colors">DOCUMENTATION</a>
                </li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">CONTACT</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">STATUS</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">DIAGNOSTICS</a></li>
              </ul>
            </div>

            <div>
              <h4 class="text-geo-text mb-6 text-sm font-medium tracking-wider">PROTOCOL</h4>
              <ul class="text-geo-text-muted space-y-3 text-sm">
                <li><a href="#" class="hover:text-geo-accent transition-colors">PRIVACY</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">TERMS</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">SECURITY</a></li>
                <li><a href="#" class="hover:text-geo-accent transition-colors">COMPLIANCE</a></li>
              </ul>
            </div>
          </div>

          <div
            class="border-geo-text/20 text-geo-text-muted mt-12 border-t pt-8 text-center text-sm tracking-wider"
          >
            <p>&copy; 2024 ONLINE VPN. ALL DIMENSIONS RESERVED.</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- FAQ Schema.org structured data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "A free online VPN is a web-based virtual private network service that encrypts your internet connection and hides your IP address without requiring any software downloads. Our online VPN browser provides instant secure access to blocked websites with military-grade encryption."
            },
            "name": "What is a free online VPN?"
          },
          {
            "@type": "Question",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Our online VPN browser works by routing your internet traffic through secure proxy servers using advanced geometric algorithms. Simply enter a website URL, click 'Connect Free VPN', and start browsing with complete privacy protection."
            },
            "name": "How does the online VPN browser work?"
          },
          {
            "@type": "Question",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes, our proxy VPN service is completely free with no hidden fees, registration requirements, or time limits. You get unlimited bandwidth and access to our global network of VPN servers at no cost."
            },
            "name": "Is the free proxy VPN service really free?"
          },
          {
            "@type": "Question",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Absolutely! Our online VPN browser works on all devices including smartphones, tablets, laptops, and desktop computers. The web proxy browser is fully responsive and optimized for mobile use."
            },
            "name": "Can I use the online VPN on mobile devices?"
          }
        ]
      }
    </script>

    <!-- Smooth scroll and focus functionality -->
    <script>
      // Smooth scroll to URL input and focus
      function scrollToUrlInput() {
        const urlInput = document.getElementById('url-input')
        if (urlInput) {
          urlInput.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })

          setTimeout(() => {
            urlInput.focus()
          }, 800)
        }
      }

      // Add event listeners when DOM is loaded
      document.addEventListener('DOMContentLoaded', function () {
        // Navigation "Start Free VPN" button
        const navStartBtn = document.getElementById('nav-start-vpn')
        if (navStartBtn) {
          navStartBtn.addEventListener('click', scrollToUrlInput)
        }

        // Connect VPN button functionality
        const connectBtn = document.getElementById('connect-vpn-btn')
        if (connectBtn) {
          connectBtn.addEventListener('click', function () {
            const urlInput = document.getElementById('url-input')
            const url = urlInput.value.trim()

            if (!url) {
              alert('Please enter a website URL first!')
              urlInput.focus()
              return
            }

            if (!url.startsWith('http://') && !url.startsWith('https://')) {
              urlInput.value = 'https://' + url
            }

            alert(
              'Connecting to: ' +
                urlInput.value +
                '\n\nThis is a demo. In production, this would connect through the VPN proxy.'
            )
          })
        }
      })
    </script>
  </body>
</html>
