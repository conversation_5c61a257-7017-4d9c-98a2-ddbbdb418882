<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OnlineVPN - Productivity Style Design</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'notion-text': '#37352f',
              'notion-text-light': '#787774',
              'notion-border': '#e9e9e7',
              'notion-bg': '#fafafa',
              'notion-hover': '#f1f1ef',
              'notion-blue': '#2383e2',
              'notion-blue-light': '#e7f3ff'
            },
            fontFamily: {
              notion: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
            }
          }
        }
      }
    </script>
  </head>
  <body class="font-notion bg-white">
    <!-- Navigation -->
    <nav class="border-notion-border sticky top-0 z-50 border-b bg-white">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-notion-text text-xl font-bold">OnlineVPN</h1>
            </div>
            <div class="ml-10 hidden md:block">
              <div class="flex items-baseline space-x-8">
                <a
                  href="#features"
                  class="text-notion-text-light hover:text-notion-text px-3 py-2 text-sm font-medium transition-colors"
                  >Features</a
                >
                <a
                  href="#workflow"
                  class="text-notion-text-light hover:text-notion-text px-3 py-2 text-sm font-medium transition-colors"
                  >Workflow</a
                >
                <a
                  href="#templates"
                  class="text-notion-text-light hover:text-notion-text px-3 py-2 text-sm font-medium transition-colors"
                  >Templates</a
                >
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <button
              class="text-notion-text-light hover:text-notion-text px-3 py-2 text-sm font-medium transition-colors"
            >
              Log in
            </button>
            <button
              class="bg-notion-text hover:bg-notion-text/90 rounded-md px-4 py-2 text-sm font-medium text-white transition-colors"
            >
              Get started
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-white py-20">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-notion-text mb-6 text-4xl font-bold leading-tight md:text-5xl">
            OnlineVPN
          </h1>

          <p class="text-notion-text-light mx-auto mb-12 max-w-3xl text-xl leading-relaxed">
            A connected workspace where privacy and productivity meet. Secure your workflow, protect
            your ideas, and work without boundaries.
          </p>

          <!-- Simple CTA -->
          <div class="mb-16">
            <button
              class="bg-notion-text hover:bg-notion-text/90 rounded-md px-6 py-3 text-base font-medium text-white transition-colors"
            >
              Get started
            </button>
          </div>

          <!-- URL Input Section -->
          <div class="mx-auto max-w-2xl">
            <div class="border-notion-border rounded-lg border bg-white p-6 shadow-sm">
              <div class="flex flex-col gap-3 sm:flex-row">
                <input
                  type="url"
                  placeholder="Enter website URL..."
                  class="border-notion-border text-notion-text placeholder-notion-text-light focus:border-notion-blue flex-1 rounded-md border px-4 py-3 transition-colors focus:outline-none"
                />
                <button
                  class="bg-notion-text hover:bg-notion-text/90 whitespace-nowrap rounded-md px-6 py-3 font-medium text-white transition-colors"
                >
                  Connect
                </button>
              </div>
              <div class="text-notion-text-light mt-4 flex flex-wrap justify-center gap-4 text-xs">
                <span>✓ No registration</span>
                <span>✓ Free forever</span>
                <span>✓ Secure by design</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="bg-notion-bg py-20">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-notion-text mb-4 text-3xl font-bold">Work anywhere</h2>
          <p class="text-notion-text-light mx-auto max-w-2xl text-lg">
            Access your tools and content securely from any location worldwide
          </p>
        </div>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <!-- Feature Card 1 -->
          <div
            class="border-notion-border rounded-lg border bg-white p-6 transition-all duration-200 hover:shadow-sm"
          >
            <div
              class="bg-notion-blue-light mb-4 flex h-12 w-12 items-center justify-center rounded-lg"
            >
              <svg
                class="text-notion-blue h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                ></path>
              </svg>
            </div>
            <h3 class="text-notion-text mb-2 text-lg font-semibold">Secure by design</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              Built with privacy-first principles to protect your digital workspace.
            </p>
          </div>

          <!-- Feature Card 2 -->
          <div
            class="border-notion-border rounded-lg border bg-white p-6 transition-all duration-200 hover:shadow-sm"
          >
            <div
              class="bg-notion-blue-light mb-4 flex h-12 w-12 items-center justify-center rounded-lg"
            >
              <svg
                class="text-notion-blue h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                ></path>
              </svg>
            </div>
            <h3 class="text-notion-text mb-2 text-lg font-semibold">Fast connections</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              Optimized for productivity with minimal latency and maximum reliability.
            </p>
          </div>

          <!-- Feature Card 3 -->
          <div
            class="border-notion-border rounded-lg border bg-white p-6 transition-all duration-200 hover:shadow-sm"
          >
            <div
              class="bg-notion-blue-light mb-4 flex h-12 w-12 items-center justify-center rounded-lg"
            >
              <svg
                class="text-notion-blue h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                ></path>
              </svg>
            </div>
            <h3 class="text-notion-text mb-2 text-lg font-semibold">Team ready</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              Collaborate securely with team members across different locations.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Workflow Section -->
    <section id="workflow" class="bg-white py-20">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-notion-text mb-4 text-3xl font-bold">Simple workflow</h2>
          <p class="text-notion-text-light mx-auto max-w-3xl text-lg">
            Get started in three simple steps. No downloads, no registration, just pure simplicity.
          </p>
        </div>

        <div class="grid grid-cols-1 gap-12 md:grid-cols-3">
          <!-- Step 1 -->
          <div class="text-center">
            <div
              class="bg-notion-blue-light mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-lg"
            >
              <span class="text-notion-blue text-xl font-bold">1</span>
            </div>
            <h3 class="text-notion-text mb-3 text-lg font-semibold">Enter URL</h3>
            <p class="text-notion-text-light leading-relaxed">
              Simply paste the website URL you want to visit into our clean, distraction-free
              interface.
            </p>
          </div>

          <!-- Step 2 -->
          <div class="text-center">
            <div
              class="bg-notion-blue-light mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-lg"
            >
              <span class="text-notion-blue text-xl font-bold">2</span>
            </div>
            <h3 class="text-notion-text mb-3 text-lg font-semibold">Choose location</h3>
            <p class="text-notion-text-light leading-relaxed">
              Select your preferred server location from our organized list of global options.
            </p>
          </div>

          <!-- Step 3 -->
          <div class="text-center">
            <div
              class="bg-notion-blue-light mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-lg"
            >
              <span class="text-notion-blue text-xl font-bold">3</span>
            </div>
            <h3 class="text-notion-text mb-3 text-lg font-semibold">Start working</h3>
            <p class="text-notion-text-light leading-relaxed">
              Begin your secure browsing session with complete privacy and peace of mind.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Templates Section -->
    <section id="templates" class="bg-notion-bg py-20">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-notion-text mb-4 text-3xl font-bold">Popular templates</h2>
          <p class="text-notion-text-light mx-auto max-w-2xl text-lg">
            Quick access to commonly used websites and services
          </p>
        </div>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <!-- Template 1 -->
          <div
            class="border-notion-border hover:bg-notion-hover cursor-pointer rounded-lg border bg-white p-4 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="flex h-8 w-8 items-center justify-center rounded bg-blue-100">
                <span class="text-sm text-blue-600">📧</span>
              </div>
              <div>
                <h4 class="text-notion-text text-sm font-medium">Email & Communication</h4>
                <p class="text-notion-text-light text-xs">Gmail, Outlook, Slack</p>
              </div>
            </div>
          </div>

          <!-- Template 2 -->
          <div
            class="border-notion-border hover:bg-notion-hover cursor-pointer rounded-lg border bg-white p-4 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="flex h-8 w-8 items-center justify-center rounded bg-green-100">
                <span class="text-sm text-green-600">📊</span>
              </div>
              <div>
                <h4 class="text-notion-text text-sm font-medium">Productivity Tools</h4>
                <p class="text-notion-text-light text-xs">Notion, Trello, Asana</p>
              </div>
            </div>
          </div>

          <!-- Template 3 -->
          <div
            class="border-notion-border hover:bg-notion-hover cursor-pointer rounded-lg border bg-white p-4 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="flex h-8 w-8 items-center justify-center rounded bg-purple-100">
                <span class="text-sm text-purple-600">🎨</span>
              </div>
              <div>
                <h4 class="text-notion-text text-sm font-medium">Design & Creative</h4>
                <p class="text-notion-text-light text-xs">Figma, Adobe, Canva</p>
              </div>
            </div>
          </div>

          <!-- Template 4 -->
          <div
            class="border-notion-border hover:bg-notion-hover cursor-pointer rounded-lg border bg-white p-4 transition-colors"
          >
            <div class="flex items-center gap-3">
              <div class="flex h-8 w-8 items-center justify-center rounded bg-orange-100">
                <span class="text-sm text-orange-600">💼</span>
              </div>
              <div>
                <h4 class="text-notion-text text-sm font-medium">Business & Finance</h4>
                <p class="text-notion-text-light text-xs">LinkedIn, Salesforce</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="bg-white py-20">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 items-center gap-16 lg:grid-cols-2">
          <div>
            <h2 class="text-notion-text mb-6 text-3xl font-bold">Why choose OnlineVPN?</h2>
            <div class="space-y-6">
              <div class="flex items-start gap-4">
                <div
                  class="bg-notion-blue-light mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded"
                >
                  <svg class="text-notion-blue h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-notion-text mb-1 font-semibold">No software installation</h3>
                  <p class="text-notion-text-light text-sm leading-relaxed">
                    Use our web-based proxy directly in your browser. Clean, simple, and efficient.
                  </p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div
                  class="bg-notion-blue-light mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded"
                >
                  <svg class="text-notion-blue h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-notion-text mb-1 font-semibold">Privacy focused</h3>
                  <p class="text-notion-text-light text-sm leading-relaxed">
                    Your browsing activity remains private. We don't track, store, or share your
                    data.
                  </p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div
                  class="bg-notion-blue-light mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded"
                >
                  <svg class="text-notion-blue h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-notion-text mb-1 font-semibold">Global access</h3>
                  <p class="text-notion-text-light text-sm leading-relaxed">
                    Connect to servers worldwide and access content as if you're browsing locally.
                  </p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div
                  class="bg-notion-blue-light mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded"
                >
                  <svg class="text-notion-blue h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-notion-text mb-1 font-semibold">Optimized performance</h3>
                  <p class="text-notion-text-light text-sm leading-relaxed">
                    Fast, reliable connections that don't slow down your workflow or productivity.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-notion-bg rounded-lg p-8">
            <h3 class="text-notion-text mb-6 text-xl font-semibold">Ready to get started?</h3>
            <p class="text-notion-text-light mb-8 leading-relaxed">
              Join thousands of professionals who use OnlineVPN to work securely from anywhere.
            </p>
            <div class="mb-8 space-y-4">
              <div class="text-notion-text-light flex items-center gap-3 text-sm">
                <span class="h-2 w-2 rounded-full bg-green-500"></span>
                <span>Free to use</span>
              </div>
              <div class="text-notion-text-light flex items-center gap-3 text-sm">
                <span class="h-2 w-2 rounded-full bg-green-500"></span>
                <span>No registration required</span>
              </div>
              <div class="text-notion-text-light flex items-center gap-3 text-sm">
                <span class="h-2 w-2 rounded-full bg-green-500"></span>
                <span>Works on all devices</span>
              </div>
            </div>
            <button
              class="bg-notion-text hover:bg-notion-text/90 rounded-md px-6 py-3 font-medium text-white transition-colors"
            >
              Start now
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="bg-notion-bg py-16">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 gap-8 text-center md:grid-cols-4">
          <div>
            <div class="text-notion-text mb-1 text-2xl font-bold">10M+</div>
            <div class="text-notion-text-light text-sm">Active users</div>
          </div>
          <div>
            <div class="text-notion-text mb-1 text-2xl font-bold">50+</div>
            <div class="text-notion-text-light text-sm">Countries</div>
          </div>
          <div>
            <div class="text-notion-text mb-1 text-2xl font-bold">99.9%</div>
            <div class="text-notion-text-light text-sm">Uptime</div>
          </div>
          <div>
            <div class="text-notion-text mb-1 text-2xl font-bold">24/7</div>
            <div class="text-notion-text-light text-sm">Support</div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="bg-white py-20">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-notion-text mb-4 text-3xl font-bold">Frequently asked questions</h2>
          <p class="text-notion-text-light text-lg">Everything you need to know about OnlineVPN</p>
        </div>

        <div class="space-y-4">
          <div class="border-notion-border rounded-lg border p-6">
            <h3 class="text-notion-text mb-2 font-semibold">What is a web proxy?</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              A web proxy acts as an intermediary between your device and the internet, allowing you
              to browse websites anonymously and securely.
            </p>
          </div>

          <div class="border-notion-border rounded-lg border p-6">
            <h3 class="text-notion-text mb-2 font-semibold">Is OnlineVPN really free?</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              Yes, OnlineVPN is completely free to use with no hidden fees, registration
              requirements, or time limits.
            </p>
          </div>

          <div class="border-notion-border rounded-lg border p-6">
            <h3 class="text-notion-text mb-2 font-semibold">How secure is my data?</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              We use enterprise-grade encryption and don't log your browsing activity. Your privacy
              and security are our top priorities.
            </p>
          </div>

          <div class="border-notion-border rounded-lg border p-6">
            <h3 class="text-notion-text mb-2 font-semibold">Can I use it on mobile devices?</h3>
            <p class="text-notion-text-light text-sm leading-relaxed">
              Yes, OnlineVPN works on all devices with a web browser, including smartphones,
              tablets, and computers.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-notion-bg py-16">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
          <div>
            <h3 class="text-notion-text mb-4 font-semibold">OnlineVPN</h3>
            <p class="text-notion-text-light mb-4 text-sm leading-relaxed">
              A connected workspace where privacy and productivity meet.
            </p>
          </div>

          <div>
            <h4 class="text-notion-text mb-4 text-sm font-medium">Product</h4>
            <ul class="text-notion-text-light space-y-2 text-sm">
              <li><a href="#" class="hover:text-notion-text transition-colors">Features</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Templates</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Workflow</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Security</a></li>
            </ul>
          </div>

          <div>
            <h4 class="text-notion-text mb-4 text-sm font-medium">Support</h4>
            <ul class="text-notion-text-light space-y-2 text-sm">
              <li><a href="#" class="hover:text-notion-text transition-colors">Help Center</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Contact</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Status</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Community</a></li>
            </ul>
          </div>

          <div>
            <h4 class="text-notion-text mb-4 text-sm font-medium">Legal</h4>
            <ul class="text-notion-text-light space-y-2 text-sm">
              <li><a href="#" class="hover:text-notion-text transition-colors">Privacy</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Terms</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Cookies</a></li>
              <li><a href="#" class="hover:text-notion-text transition-colors">Licenses</a></li>
            </ul>
          </div>
        </div>

        <div
          class="border-notion-border text-notion-text-light mt-12 border-t pt-8 text-center text-sm"
        >
          <p>&copy; 2024 OnlineVPN. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </body>
</html>
