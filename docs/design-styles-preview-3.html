<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OnlineVPN Design Styles Preview - Part 3</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        background: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .style-section {
        margin-bottom: 60px;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .style-header {
        padding: 20px;
        background: #333;
        color: white;
        text-align: center;
      }

      .style-preview {
        height: 600px;
        overflow-y: auto;
      }

      /* Style 11: Sci-Fi/Space Theme */
      .style-11 {
        background: radial-gradient(ellipse at center, #0f0f23 0%, #000000 100%);
        color: #00d4ff;
        font-family: 'Orbitron', 'Courier New', monospace;
        position: relative;
        overflow: hidden;
      }

      .style-11::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="0.5" fill="%2300d4ff" opacity="0.8"><animate attributeName="opacity" values="0.8;0.2;0.8" dur="3s" repeatCount="indefinite"/></circle><circle cx="90" cy="20" r="0.3" fill="%2300d4ff" opacity="0.6"><animate attributeName="opacity" values="0.6;0.1;0.6" dur="4s" repeatCount="indefinite"/></circle><circle cx="30" cy="80" r="0.4" fill="%2300d4ff" opacity="0.7"><animate attributeName="opacity" values="0.7;0.2;0.7" dur="2s" repeatCount="indefinite"/></circle><circle cx="70" cy="60" r="0.2" fill="%2300d4ff" opacity="0.5"><animate attributeName="opacity" values="0.5;0.1;0.5" dur="5s" repeatCount="indefinite"/></circle></svg>');
      }

      .style-11 .hero {
        padding: 100px 20px;
        text-align: center;
        position: relative;
        z-index: 2;
      }

      .style-11 h1 {
        font-size: 4rem;
        font-weight: 700;
        margin-bottom: 30px;
        text-transform: uppercase;
        letter-spacing: 5px;
        text-shadow:
          0 0 20px #00d4ff,
          0 0 40px #00d4ff;
        animation: glow 2s ease-in-out infinite alternate;
      }

      @keyframes glow {
        from {
          text-shadow:
            0 0 20px #00d4ff,
            0 0 40px #00d4ff;
        }
        to {
          text-shadow:
            0 0 30px #00d4ff,
            0 0 60px #00d4ff,
            0 0 80px #00d4ff;
        }
      }

      .style-11 .hero-spaceship {
        width: 200px;
        height: 120px;
        margin: 0 auto 40px;
        position: relative;
        background: linear-gradient(45deg, #1a1a2e, #16213e);
        clip-path: polygon(0% 50%, 20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%);
        border: 2px solid #00d4ff;
        box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
      }

      .style-11 .hero-spaceship::before {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        background: #00d4ff;
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 20px #00d4ff;
        animation: pulse 1.5s ease-in-out infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: translate(-50%, -50%) scale(1);
        }
        50% {
          transform: translate(-50%, -50%) scale(1.2);
        }
      }

      .style-11 .cta-button {
        background: linear-gradient(45deg, #00d4ff, #0099cc);
        border: 2px solid #00d4ff;
        color: #000;
        padding: 20px 50px;
        font-size: 1.2rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        clip-path: polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%);
        transition: all 0.3s ease;
      }

      .style-11 .cta-button:hover {
        box-shadow: 0 0 30px rgba(0, 212, 255, 0.7);
        transform: translateY(-3px);
      }

      .style-11 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        padding: 80px 20px;
        position: relative;
        z-index: 2;
      }

      .style-11 .feature-card {
        background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(22, 33, 62, 0.8));
        border: 1px solid #00d4ff;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        backdrop-filter: blur(10px);
        position: relative;
        transition: all 0.3s ease;
      }

      .style-11 .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
        border-color: #00d4ff;
      }

      .style-11 .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .style-11 .feature-card:hover::before {
        opacity: 1;
      }

      .style-11 .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 25px;
        background: radial-gradient(circle, #00d4ff, #0099cc);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        position: relative;
      }

      .style-11 .feature-icon::after {
        content: '';
        position: absolute;
        width: 60px;
        height: 60px;
        border: 2px solid #00d4ff;
        border-radius: 50%;
        animation: rotate 3s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      /* Style 12: Vintage/Retro Newspaper */
      .style-12 {
        background: #f4f1e8;
        color: #2c1810;
        font-family: 'Times New Roman', serif;
        position: relative;
      }

      .style-12::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="paper" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="%23f4f1e8"/><circle cx="5" cy="5" r="0.5" fill="%23e8dcc0" opacity="0.3"/><circle cx="15" cy="15" r="0.3" fill="%23e8dcc0" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23paper)"/></svg>');
        opacity: 0.5;
      }

      .style-12 .hero {
        padding: 80px 20px;
        text-align: center;
        position: relative;
        z-index: 2;
        border-bottom: 4px double #2c1810;
        margin: 20px;
      }

      .style-12 h1 {
        font-size: 4.5rem;
        font-weight: 900;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 3px;
        text-shadow: 2px 2px 0px rgba(44, 24, 16, 0.3);
        font-family: 'Times New Roman', serif;
      }

      .style-12 .hero-banner {
        background: #2c1810;
        color: #f4f1e8;
        padding: 15px;
        margin: 30px auto;
        max-width: 600px;
        text-align: center;
        border: 3px solid #2c1810;
        position: relative;
      }

      .style-12 .hero-banner::before,
      .style-12 .hero-banner::after {
        content: '★';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.5rem;
      }

      .style-12 .hero-banner::before {
        left: 20px;
      }

      .style-12 .hero-banner::after {
        right: 20px;
      }

      .style-12 .cta-button {
        background: #2c1810;
        color: #f4f1e8;
        border: 3px solid #2c1810;
        padding: 18px 40px;
        font-size: 1.2rem;
        font-weight: 700;
        text-transform: uppercase;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        font-family: 'Times New Roman', serif;
      }

      .style-12 .cta-button:hover {
        background: #f4f1e8;
        color: #2c1810;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(44, 24, 16, 0.3);
      }

      .style-12 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        padding: 60px 20px;
        position: relative;
        z-index: 2;
      }

      .style-12 .feature-card {
        background: #f4f1e8;
        border: 2px solid #2c1810;
        padding: 40px;
        text-align: left;
        position: relative;
        box-shadow: 5px 5px 0px rgba(44, 24, 16, 0.2);
        transition: all 0.3s ease;
      }

      .style-12 .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 8px 8px 0px rgba(44, 24, 16, 0.3);
      }

      .style-12 .feature-card::before {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        background: #2c1810;
        clip-path: polygon(
          50% 0%,
          61% 35%,
          98% 35%,
          68% 57%,
          79% 91%,
          50% 70%,
          21% 91%,
          32% 57%,
          2% 35%,
          39% 35%
        );
      }

      .style-12 .feature-card h3 {
        font-size: 1.8rem;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 1px;
        border-bottom: 2px solid #2c1810;
        padding-bottom: 10px;
      }

      .style-12 .feature-card p {
        font-size: 1.1rem;
        line-height: 1.7;
        text-align: justify;
      }

      /* Style 13: Medical/Healthcare Theme */
      .style-13 {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
        color: #2c5530;
        font-family: 'Helvetica Neue', Arial, sans-serif;
      }

      .style-13 .hero {
        padding: 80px 20px;
        text-align: center;
        background: rgba(255, 255, 255, 0.9);
        margin: 20px;
        border-radius: 20px;
        border: 2px solid #4a90e2;
      }

      .style-13 h1 {
        font-size: 3.5rem;
        font-weight: 300;
        margin-bottom: 20px;
        color: #2c5530;
      }

      .style-13 .hero-cross {
        width: 120px;
        height: 120px;
        margin: 0 auto 30px;
        background: #4a90e2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 10px 30px rgba(74, 144, 226, 0.3);
      }

      .style-13 .hero-cross::before,
      .style-13 .hero-cross::after {
        content: '';
        position: absolute;
        background: white;
      }

      .style-13 .hero-cross::before {
        width: 60px;
        height: 15px;
        border-radius: 8px;
      }

      .style-13 .hero-cross::after {
        width: 15px;
        height: 60px;
        border-radius: 8px;
      }

      .style-13 .cta-button {
        background: linear-gradient(45deg, #4a90e2, #357abd);
        border: none;
        color: white;
        padding: 18px 45px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 25px;
        cursor: pointer;
        box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
        transition: all 0.3s ease;
      }

      .style-13 .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(74, 144, 226, 0.4);
      }

      .style-13 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        padding: 60px 20px;
      }

      .style-13 .feature-card {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #e8f5e8;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
      }

      .style-13 .feature-card:hover {
        transform: translateY(-5px);
        border-color: #4a90e2;
        box-shadow: 0 15px 30px rgba(74, 144, 226, 0.2);
      }

      .style-13 .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 25px;
        background: linear-gradient(45deg, #4a90e2, #357abd);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
      }

      /* Style 14: Art Deco/1920s */
      .style-14 {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d1b69 50%, #000000 100%);
        color: #f4d03f;
        font-family: 'Playfair Display', Georgia, serif;
        position: relative;
      }

      .style-14::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="artdeco" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M0,20 L10,0 L20,20 Z" fill="%23f4d03f" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23artdeco)"/></svg>');
      }

      .style-14 .hero {
        padding: 100px 20px;
        text-align: center;
        position: relative;
        z-index: 2;
      }

      .style-14 h1 {
        font-size: 4.5rem;
        font-weight: 400;
        margin-bottom: 30px;
        text-transform: uppercase;
        letter-spacing: 8px;
        font-style: italic;
        text-shadow: 3px 3px 0px rgba(244, 208, 63, 0.3);
      }

      .style-14 .hero-diamond {
        width: 150px;
        height: 150px;
        margin: 0 auto 40px;
        background: linear-gradient(45deg, #f4d03f, #f7dc6f);
        transform: rotate(45deg);
        position: relative;
        border: 3px solid #f4d03f;
        box-shadow: 0 0 40px rgba(244, 208, 63, 0.4);
      }

      .style-14 .hero-diamond::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 80px;
        height: 80px;
        background: #1a1a1a;
        transform: translate(-50%, -50%) rotate(-45deg);
        border: 2px solid #f4d03f;
      }

      .style-14 .cta-button {
        background: linear-gradient(45deg, #f4d03f, #f7dc6f);
        border: 3px solid #f4d03f;
        color: #1a1a1a;
        padding: 20px 60px;
        font-size: 1.3rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 3px;
        cursor: pointer;
        position: relative;
        clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);
        transition: all 0.3s ease;
      }

      .style-14 .cta-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(244, 208, 63, 0.4);
      }

      .style-14 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        padding: 80px 20px;
        position: relative;
        z-index: 2;
      }

      .style-14 .feature-card {
        background: linear-gradient(135deg, rgba(244, 208, 63, 0.1), rgba(45, 27, 105, 0.3));
        border: 2px solid #f4d03f;
        padding: 50px;
        text-align: center;
        position: relative;
        clip-path: polygon(0% 0%, 90% 0%, 100% 25%, 100% 100%, 10% 100%, 0% 75%);
        transition: all 0.3s ease;
      }

      .style-14 .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(244, 208, 63, 0.3);
      }

      .style-14 .feature-card::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        bottom: 20px;
        border: 1px solid rgba(244, 208, 63, 0.3);
        clip-path: polygon(0% 0%, 85% 0%, 100% 30%, 100% 100%, 15% 100%, 0% 70%);
      }

      .style-14 .feature-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 30px;
        background: linear-gradient(45deg, #f4d03f, #f7dc6f);
        transform: rotate(45deg);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #f4d03f;
        box-shadow: 0 10px 30px rgba(244, 208, 63, 0.3);
      }

      .style-14 .feature-icon svg {
        transform: rotate(-45deg);
      }

      /* Style 15: Steampunk/Industrial */
      .style-15 {
        background: linear-gradient(135deg, #3c2415 0%, #8b4513 50%, #2f1b14 100%);
        color: #d4af37;
        font-family: 'Times New Roman', serif;
        position: relative;
      }

      .style-15::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="gears" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="8" fill="none" stroke="%23d4af37" stroke-width="1" opacity="0.1"/><circle cx="15" cy="15" r="3" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23gears)"/></svg>');
      }

      .style-15 .hero {
        padding: 100px 20px;
        text-align: center;
        position: relative;
        z-index: 2;
      }

      .style-15 h1 {
        font-size: 4rem;
        font-weight: 700;
        margin-bottom: 30px;
        text-transform: uppercase;
        letter-spacing: 5px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        color: #d4af37;
      }

      .style-15 .hero-gear {
        width: 150px;
        height: 150px;
        margin: 0 auto 40px;
        background: radial-gradient(circle, #d4af37, #b8860b);
        border-radius: 50%;
        position: relative;
        border: 5px solid #8b4513;
        box-shadow: 0 0 30px rgba(212, 175, 55, 0.4);
      }

      .style-15 .hero-gear::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 50%;
        width: 20px;
        height: 20px;
        background: #d4af37;
        transform: translateX(-50%);
        border-radius: 3px;
        box-shadow:
          0 30px 0 #d4af37,
          0 60px 0 #d4af37,
          0 90px 0 #d4af37,
          0 120px 0 #d4af37,
          0 150px 0 #d4af37;
      }

      .style-15 .hero-gear::after {
        content: '';
        position: absolute;
        top: 50%;
        left: -10px;
        width: 20px;
        height: 20px;
        background: #d4af37;
        transform: translateY(-50%);
        border-radius: 3px;
        box-shadow:
          30px 0 0 #d4af37,
          60px 0 0 #d4af37,
          90px 0 0 #d4af37,
          120px 0 0 #d4af37,
          150px 0 0 #d4af37;
      }

      .style-15 .cta-button {
        background: linear-gradient(45deg, #d4af37, #b8860b);
        border: 3px solid #8b4513;
        color: #2f1b14;
        padding: 20px 50px;
        font-size: 1.2rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        box-shadow:
          inset 0 2px 4px rgba(255, 255, 255, 0.2),
          0 8px 20px rgba(0, 0, 0, 0.3);
      }

      .style-15 .cta-button:hover {
        transform: translateY(-2px);
        box-shadow:
          inset 0 2px 4px rgba(255, 255, 255, 0.3),
          0 12px 25px rgba(0, 0, 0, 0.4);
      }

      .style-15 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        padding: 80px 20px;
        position: relative;
        z-index: 2;
      }

      .style-15 .feature-card {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.8), rgba(60, 36, 21, 0.9));
        border: 3px solid #8b4513;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
        transition: all 0.3s ease;
      }

      .style-15 .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
      }

      .style-15 .feature-card::before {
        content: '';
        position: absolute;
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        border: 1px solid rgba(212, 175, 55, 0.3);
        border-radius: 10px;
      }

      .style-15 .feature-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 25px;
        background: radial-gradient(circle, #d4af37, #b8860b);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid #8b4513;
        box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
        position: relative;
      }

      .style-15 .feature-icon::after {
        content: '';
        position: absolute;
        width: 80px;
        height: 80px;
        border: 2px dashed rgba(139, 69, 19, 0.5);
        border-radius: 50%;
        animation: rotate 10s linear infinite;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 style="text-align: center; margin-bottom: 40px; color: #333">
        OnlineVPN Design Styles Preview - Part 3
      </h1>

      <!-- Style 11: Sci-Fi/Space Theme -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 11: Sci-Fi/Space Theme</h2>
          <p>Target: Sci-fi enthusiasts, gamers, tech innovators, futuristic mindset users</p>
        </div>
        <div class="style-preview style-11">
          <div class="hero">
            <div class="hero-spaceship"></div>
            <h1>ONLINE VPN</h1>
            <p style="font-size: 1.3rem; margin-bottom: 40px; opacity: 0.9">
              QUANTUM ENCRYPTION • INTERSTELLAR PRIVACY
            </p>
            <button class="cta-button">LAUNCH MISSION</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#000"
                  stroke-width="2"
                >
                  <path d="M12 2L2 7l10 5 10-5-10-5z" />
                  <path d="M2 17l10 5 10-5" />
                  <path d="M2 12l10 5 10-5" />
                </svg>
              </div>
              <h3>Quantum Shield</h3>
              <p>
                Advanced quantum encryption technology protects your data across multiple dimensions
                of cyberspace.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#000"
                  stroke-width="2"
                >
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
              </div>
              <h3>Warp Speed</h3>
              <p>
                Faster-than-light data transmission through our galactic network of quantum servers.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#000"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path
                    d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                  />
                </svg>
              </div>
              <h3>Galactic Network</h3>
              <p>Access the universe of content through our interplanetary server constellation.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#000"
                  stroke-width="2"
                >
                  <path d="M9 19c-5 0-8-3-8-6 0-3 3-6 8-6s8 3 8 6c0 3-3 6-8 6z" />
                  <path
                    d="M17 12h3c.5 0 .9-.4.9-.9V4.1c0-.5-.4-.9-.9-.9H3.1c-.5 0-.9.4-.9.9v6.9c0 .*******.9H7"
                  />
                </svg>
              </div>
              <h3>AI Command Center</h3>
              <p>Our artificial intelligence provides 24/7 mission support from the mothership.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 12: Vintage/Retro Newspaper -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 12: Vintage/Retro Newspaper</h2>
          <p>Target: History enthusiasts, vintage lovers, traditional users, journalism industry</p>
        </div>
        <div class="style-preview style-12">
          <div class="hero">
            <h1>ONLINE VPN</h1>
            <div class="hero-banner">
              <p style="font-size: 1.4rem; font-weight: 700; margin: 0">
                EXTRA! EXTRA! PRIVACY REVOLUTION!
              </p>
            </div>
            <p style="font-size: 1.2rem; margin-bottom: 40px; font-style: italic">
              The Most Trusted Name in Digital Security Since 2024
            </p>
            <button class="cta-button">Subscribe Now</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <h3>Front Page Security</h3>
              <p>
                Breaking news: Our military-grade encryption makes headlines in protecting your most
                sensitive information from prying eyes and malicious actors across the digital
                landscape.
              </p>
            </div>
            <div class="feature-card">
              <h3>Express Delivery</h3>
              <p>
                Hot off the press! Lightning-fast connections deliver your content faster than
                morning newspapers, ensuring you never miss the latest updates from around the
                world.
              </p>
            </div>
            <div class="feature-card">
              <h3>Global Correspondent</h3>
              <p>
                Our worldwide network of servers acts like international correspondents, bringing
                you exclusive access to content from every corner of the globe with unmatched
                reliability.
              </p>
            </div>
            <div class="feature-card">
              <h3>Editorial Support</h3>
              <p>
                Our expert editorial team provides round-the-clock assistance, ensuring your digital
                experience remains as smooth as reading your favorite morning publication.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 13: Medical/Healthcare Theme -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 13: Medical/Healthcare Theme</h2>
          <p>Target: Health-conscious users, medical professionals, security-focused individuals</p>
        </div>
        <div class="style-preview style-13">
          <div class="hero">
            <div class="hero-cross"></div>
            <h1>OnlineVPN</h1>
            <p style="font-size: 1.3rem; margin-bottom: 40px; color: #4a90e2">
              Healthy Internet • Protected Privacy • Secure Connections
            </p>
            <button class="cta-button">Start Treatment</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                </svg>
              </div>
              <h3>Vital Security</h3>
              <p>
                Monitor and protect your digital health with our comprehensive security diagnostics
                and real-time threat prevention.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
              </div>
              <h3>Rapid Response</h3>
              <p>
                Emergency-level speed for critical connections. Our network responds faster than
                medical emergency services.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path
                    d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                  />
                </svg>
              </div>
              <h3>Global Clinic</h3>
              <p>
                Access our worldwide network of secure servers, each maintained to the highest
                standards of digital hygiene.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
              <h3>Expert Care</h3>
              <p>
                Our certified specialists provide 24/7 support with the precision and care of
                medical professionals.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 14: Art Deco/1920s -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 14: Art Deco/1920s</h2>
          <p>
            Target: Luxury enthusiasts, vintage lovers, sophisticated users, design connoisseurs
          </p>
        </div>
        <div class="style-preview style-14">
          <div class="hero">
            <div class="hero-diamond"></div>
            <h1>ONLINE VPN</h1>
            <p style="font-size: 1.4rem; margin-bottom: 50px; opacity: 0.9; font-style: italic">
              The Golden Age of Digital Privacy
            </p>
            <button class="cta-button">JOIN THE ELITE</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
              </div>
              <h3>Prohibition-Era Security</h3>
              <p>
                Like the finest speakeasies of the 1920s, our encryption keeps your activities
                hidden from prying eyes with sophisticated elegance.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
              </div>
              <h3>Jazz Age Speed</h3>
              <p>
                Experience the roaring twenties of internet speed - fast, exhilarating, and
                absolutely electrifying in every connection.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path
                    d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                  />
                </svg>
              </div>
              <h3>Art Deco Network</h3>
              <p>
                Our geometrically perfect server architecture spans the globe like the great
                monuments of the Art Deco era.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
              <h3>Gatsby Service</h3>
              <p>
                Lavish support worthy of the Great Gatsby himself - opulent, attentive, and
                absolutely magnificent in every detail.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 15: Steampunk/Industrial -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 15: Steampunk/Industrial</h2>
          <p>Target: Alternative culture enthusiasts, makers, engineers, vintage tech lovers</p>
        </div>
        <div class="style-preview style-15">
          <div class="hero">
            <div class="hero-gear"></div>
            <h1>ONLINE VPN</h1>
            <p style="font-size: 1.3rem; margin-bottom: 40px; opacity: 0.9">
              STEAM-POWERED SECURITY • BRASS-FORGED PRIVACY
            </p>
            <button class="cta-button">ENGAGE ENGINES</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#2f1b14"
                  stroke-width="2"
                >
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
              </div>
              <h3>Clockwork Encryption</h3>
              <p>
                Precision-engineered security mechanisms work like the finest Swiss clockwork,
                protecting your data with mechanical perfection.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#2f1b14"
                  stroke-width="2"
                >
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
              </div>
              <h3>Steam Engine Speed</h3>
              <p>
                Harness the raw power of industrial-grade connections that churn through data like a
                mighty steam locomotive.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#2f1b14"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path
                    d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                  />
                </svg>
              </div>
              <h3>Brass Telegraph Network</h3>
              <p>
                Our global network operates like a vast telegraph system, connecting continents with
                brass-bound reliability.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#2f1b14"
                  stroke-width="2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
              <h3>Master Craftsmen</h3>
              <p>
                Our skilled engineers provide artisanal support, handcrafting solutions with the
                dedication of Victorian-era craftsmen.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
