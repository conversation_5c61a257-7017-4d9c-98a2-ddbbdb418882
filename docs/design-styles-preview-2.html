<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OnlineVPN Design Styles Preview - Part 2</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        background: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .style-section {
        margin-bottom: 60px;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .style-header {
        padding: 20px;
        background: #333;
        color: white;
        text-align: center;
      }

      .style-preview {
        height: 600px;
        overflow-y: auto;
      }

      /* Style 6: Neomorphism/Soft UI */
      .style-6 {
        background: #e0e5ec;
        color: #4a5568;
        font-family:
          'SF Pro Display',
          -apple-system,
          sans-serif;
      }

      .style-6 .hero {
        padding: 80px 20px;
        text-align: center;
        background: #e0e5ec;
      }

      .style-6 h1 {
        font-size: 3.5rem;
        font-weight: 300;
        margin-bottom: 30px;
        color: #2d3748;
        text-shadow:
          2px 2px 4px rgba(255, 255, 255, 0.8),
          -2px -2px 4px rgba(163, 177, 198, 0.6);
      }

      .style-6 .hero-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 30px;
        background: #e0e5ec;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow:
          20px 20px 40px rgba(163, 177, 198, 0.4),
          -20px -20px 40px rgba(255, 255, 255, 0.8);
      }

      .style-6 .cta-button {
        background: #e0e5ec;
        border: none;
        padding: 20px 40px;
        border-radius: 50px;
        color: #4a5568;
        font-weight: 600;
        cursor: pointer;
        box-shadow:
          10px 10px 20px rgba(163, 177, 198, 0.4),
          -10px -10px 20px rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
      }

      .style-6 .cta-button:hover {
        box-shadow:
          inset 10px 10px 20px rgba(163, 177, 198, 0.4),
          inset -10px -10px 20px rgba(255, 255, 255, 0.8);
      }

      .style-6 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 40px;
        padding: 60px 20px;
      }

      .style-6 .feature-card {
        background: #e0e5ec;
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        box-shadow:
          15px 15px 30px rgba(163, 177, 198, 0.4),
          -15px -15px 30px rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
      }

      .style-6 .feature-card:hover {
        transform: translateY(-5px);
      }

      .style-6 .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background: #e0e5ec;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow:
          10px 10px 20px rgba(163, 177, 198, 0.4),
          -10px -10px 20px rgba(255, 255, 255, 0.8);
      }

      /* Style 7: Organic/Nature Theme */
      .style-7 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #2d3748;
        font-family: 'Georgia', serif;
        position: relative;
      }

      .style-7::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23ffffff" opacity="0.1"/><circle cx="80" cy="40" r="1.5" fill="%23ffffff" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="90" cy="90" r="2.5" fill="%23ffffff" opacity="0.1"/></svg>');
        opacity: 0.3;
      }

      .style-7 .hero {
        padding: 80px 20px;
        text-align: center;
        background: rgba(255, 255, 255, 0.95);
        margin: 20px;
        border-radius: 30px;
        position: relative;
        backdrop-filter: blur(10px);
      }

      .style-7 h1 {
        font-size: 3.5rem;
        font-weight: 400;
        margin-bottom: 20px;
        color: #2d3748;
        font-style: italic;
      }

      .style-7 .hero-illustration {
        width: 200px;
        height: 150px;
        margin: 0 auto 30px;
        background: linear-gradient(45deg, #48bb78, #38a169);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
      }

      .style-7 .hero-illustration::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 20px;
        width: 40px;
        height: 40px;
        background: #68d391;
        border-radius: 50%;
        box-shadow:
          60px 0 0 #68d391,
          120px 0 0 #68d391,
          30px 30px 0 #68d391,
          90px 30px 0 #68d391,
          0 60px 0 #68d391,
          60px 60px 0 #68d391,
          120px 60px 0 #68d391;
      }

      .style-7 .cta-button {
        background: linear-gradient(45deg, #48bb78, #38a169);
        border: none;
        padding: 18px 45px;
        border-radius: 25px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
        transition: all 0.3s ease;
      }

      .style-7 .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(72, 187, 120, 0.4);
      }

      .style-7 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        padding: 60px 20px;
      }

      .style-7 .feature-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 25px;
        padding: 40px;
        text-align: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
      }

      .style-7 .feature-card:hover {
        transform: translateY(-8px);
        background: rgba(255, 255, 255, 0.95);
      }

      .style-7 .feature-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 25px;
        background: linear-gradient(45deg, #48bb78, #38a169);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      .style-7 .feature-icon::before {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        top: 15px;
        left: 15px;
      }

      /* Style 8: Geometric/Abstract */
      .style-8 {
        background: linear-gradient(45deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        color: white;
        font-family: 'Helvetica Neue', Arial, sans-serif;
        position: relative;
      }

      .style-8::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="20,10 40,30 20,50 0,30" fill="%23ffffff" opacity="0.05"/><polygon points="80,20 100,40 80,60 60,40" fill="%23ffffff" opacity="0.05"/><polygon points="50,60 70,80 50,100 30,80" fill="%23ffffff" opacity="0.05"/></svg>');
      }

      .style-8 .hero {
        padding: 80px 20px;
        text-align: center;
        position: relative;
      }

      .style-8 h1 {
        font-size: 4rem;
        font-weight: 100;
        margin-bottom: 30px;
        letter-spacing: 8px;
        text-transform: uppercase;
      }

      .style-8 .hero-shapes {
        position: relative;
        width: 200px;
        height: 200px;
        margin: 0 auto 40px;
      }

      .style-8 .shape {
        position: absolute;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .style-8 .shape-1 {
        width: 80px;
        height: 80px;
        top: 20px;
        left: 60px;
        transform: rotate(45deg);
      }

      .style-8 .shape-2 {
        width: 60px;
        height: 60px;
        top: 70px;
        left: 20px;
        border-radius: 50%;
      }

      .style-8 .shape-3 {
        width: 100px;
        height: 40px;
        top: 120px;
        left: 50px;
        clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
      }

      .style-8 .cta-button {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 18px 50px;
        font-size: 1.1rem;
        font-weight: 300;
        letter-spacing: 2px;
        text-transform: uppercase;
        cursor: pointer;
        transition: all 0.3s ease;
        clip-path: polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%);
      }

      .style-8 .cta-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
      }

      .style-8 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 40px;
        padding: 80px 20px;
      }

      .style-8 .feature-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 40px;
        text-align: center;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        clip-path: polygon(0% 0%, 90% 0%, 100% 20%, 100% 100%, 10% 100%, 0% 80%);
      }

      .style-8 .feature-card:hover {
        transform: translateY(-10px);
        background: rgba(255, 255, 255, 0.15);
      }

      .style-8 .feature-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.5s ease;
        opacity: 0;
      }

      .style-8 .feature-card:hover::before {
        opacity: 1;
        animation: shimmer 1s ease-in-out;
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
        100% {
          transform: translateX(100%) translateY(100%) rotate(45deg);
        }
      }

      .style-8 .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 25px;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
      }

      /* Style 9: Luxury/Premium */
      .style-9 {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: #f7fafc;
        font-family: 'Playfair Display', Georgia, serif;
      }

      .style-9 .hero {
        padding: 100px 20px;
        text-align: center;
        position: relative;
        background: radial-gradient(circle at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
      }

      .style-9 h1 {
        font-size: 4rem;
        font-weight: 400;
        margin-bottom: 30px;
        color: #d4af37;
        font-style: italic;
        text-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
      }

      .style-9 .hero-crown {
        width: 120px;
        height: 80px;
        margin: 0 auto 40px;
        position: relative;
        background: linear-gradient(45deg, #d4af37, #f7e98e);
        clip-path: polygon(0% 100%, 10% 0%, 25% 50%, 40% 0%, 60% 0%, 75% 50%, 90% 0%, 100% 100%);
        box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
      }

      .style-9 .cta-button {
        background: linear-gradient(45deg, #d4af37, #f7e98e);
        border: 2px solid #d4af37;
        color: #1a1a1a;
        padding: 20px 50px;
        font-size: 1.2rem;
        font-weight: 600;
        cursor: pointer;
        text-transform: uppercase;
        letter-spacing: 2px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .style-9 .cta-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s ease;
      }

      .style-9 .cta-button:hover::before {
        left: 100%;
      }

      .style-9 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 50px;
        padding: 100px 20px;
        max-width: 1200px;
        margin: 0 auto;
      }

      .style-9 .feature-card {
        background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(45, 45, 45, 0.8) 100%);
        border: 1px solid rgba(212, 175, 55, 0.3);
        border-radius: 15px;
        padding: 50px;
        text-align: center;
        position: relative;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
      }

      .style-9 .feature-card:hover {
        transform: translateY(-10px);
        border-color: #d4af37;
        box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
      }

      .style-9 .feature-card::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(45deg, #d4af37, transparent, #d4af37);
        border-radius: 15px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .style-9 .feature-card:hover::before {
        opacity: 1;
      }

      .style-9 .feature-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 30px;
        background: linear-gradient(45deg, #d4af37, #f7e98e);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
      }

      .style-9 .feature-icon::after {
        content: '';
        position: absolute;
        width: 80px;
        height: 80px;
        background: #1a1a1a;
        border-radius: 50%;
        top: 10px;
        left: 10px;
      }

      /* Style 10: Playful/Creative */
      .style-10 {
        background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        color: #2d3748;
        font-family: 'Comic Sans MS', cursive, sans-serif;
        position: relative;
      }

      .style-10::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="%23ffffff" opacity="0.3"/><circle cx="80" cy="30" r="2" fill="%23ffffff" opacity="0.3"/><circle cx="30" cy="70" r="4" fill="%23ffffff" opacity="0.3"/><circle cx="70" cy="80" r="2.5" fill="%23ffffff" opacity="0.3"/><path d="M10,10 Q15,5 20,10 T30,10" stroke="%23ffffff" stroke-width="2" fill="none" opacity="0.2"/><path d="M70,20 Q75,15 80,20 T90,20" stroke="%23ffffff" stroke-width="2" fill="none" opacity="0.2"/></svg>');
      }

      .style-10 .hero {
        padding: 80px 20px;
        text-align: center;
        position: relative;
      }

      .style-10 h1 {
        font-size: 3.5rem;
        font-weight: 900;
        margin-bottom: 20px;
        color: #e53e3e;
        text-shadow:
          3px 3px 0px #fff,
          6px 6px 0px #e53e3e;
        transform: rotate(-2deg);
      }

      .style-10 .hero-mascot {
        width: 150px;
        height: 150px;
        margin: 0 auto 30px;
        background: #4299e1;
        border-radius: 50%;
        position: relative;
        border: 5px solid #fff;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .style-10 .hero-mascot::before {
        content: '😊';
        position: absolute;
        font-size: 4rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .style-10 .hero-mascot::after {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        background: #fff;
        border-radius: 50%;
        top: 20px;
        right: 20px;
        box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      }

      .style-10 .cta-button {
        background: linear-gradient(45deg, #4299e1, #63b3ed);
        border: 4px solid #fff;
        color: #fff;
        padding: 20px 40px;
        font-size: 1.3rem;
        font-weight: 900;
        cursor: pointer;
        border-radius: 50px;
        text-transform: uppercase;
        box-shadow: 0 8px 20px rgba(66, 153, 225, 0.3);
        transition: all 0.3s ease;
        transform: rotate(1deg);
      }

      .style-10 .cta-button:hover {
        transform: rotate(-1deg) scale(1.05);
        box-shadow: 0 12px 30px rgba(66, 153, 225, 0.4);
      }

      .style-10 .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        padding: 60px 20px;
      }

      .style-10 .feature-card {
        background: rgba(255, 255, 255, 0.9);
        border: 4px solid #fff;
        border-radius: 25px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .style-10 .feature-card:nth-child(odd) {
        transform: rotate(1deg);
      }

      .style-10 .feature-card:nth-child(even) {
        transform: rotate(-1deg);
      }

      .style-10 .feature-card:hover {
        transform: rotate(0deg) scale(1.05);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
      }

      .style-10 .feature-icon {
        width: 100px;
        height: 100px;
        margin: 0 auto 25px;
        background: linear-gradient(45deg, #ed8936, #f6ad55);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid #fff;
        box-shadow: 0 8px 20px rgba(237, 137, 54, 0.3);
        font-size: 2.5rem;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 style="text-align: center; margin-bottom: 40px; color: #333">
        OnlineVPN Design Styles Preview - Part 2
      </h1>

      <!-- Style 6: Neomorphism/Soft UI -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 6: Neomorphism/Soft UI</h2>
          <p>Target: Premium users, minimalist enthusiasts, modern professionals</p>
        </div>
        <div class="style-preview style-6">
          <div class="hero">
            <div class="hero-icon">
              <svg
                width="60"
                height="60"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#4a5568"
                stroke-width="2"
              >
                <path d="M9 12l2 2 4-4" />
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3" />
                <path d="M12 21c0-1-1-3-3-3s-3 2-3 3 1 3 3 3 3-2 3-3" />
              </svg>
            </div>
            <h1>OnlineVPN</h1>
            <p style="font-size: 1.2rem; margin-bottom: 40px; color: #718096">
              Seamless privacy protection with elegant design
            </p>
            <button class="cta-button">Get Started</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#4a5568"
                  stroke-width="2"
                >
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
              </div>
              <h3>Advanced Security</h3>
              <p>
                Military-grade encryption protects your digital life with uncompromising security
                standards.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#4a5568"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10" />
                  <polygon points="10,8 16,12 10,16 10,8" />
                </svg>
              </div>
              <h3>Lightning Speed</h3>
              <p>
                Optimized servers deliver blazing-fast connections for seamless streaming and
                browsing.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#4a5568"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="2" y1="12" x2="22" y2="12" />
                  <path
                    d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
                  />
                </svg>
              </div>
              <h3>Global Network</h3>
              <p>
                Access content worldwide with our premium server network spanning 50+ countries.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#4a5568"
                  stroke-width="2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
              <h3>24/7 Support</h3>
              <p>
                Expert assistance available around the clock to ensure your connection stays secure.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 7: Organic/Nature Theme -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 7: Organic/Nature Theme</h2>
          <p>Target: Eco-conscious users, wellness enthusiasts, creative professionals</p>
        </div>
        <div class="style-preview style-7">
          <div class="hero">
            <div class="hero-illustration"></div>
            <h1>OnlineVPN</h1>
            <p style="font-size: 1.3rem; margin-bottom: 40px; color: #4a5568">
              Naturally secure, organically private
            </p>
            <button class="cta-button">Grow Your Privacy</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon"></div>
              <h3>Organic Security</h3>
              <p>
                Like nature's own protection, our encryption grows stronger with every connection,
                naturally safeguarding your data.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon"></div>
              <h3>Flowing Speed</h3>
              <p>
                Experience internet speeds that flow like a mountain stream - pure, uninterrupted,
                and refreshingly fast.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon"></div>
              <h3>Global Ecosystem</h3>
              <p>
                Our worldwide network connects like a living ecosystem, bringing you closer to
                content across the globe.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon"></div>
              <h3>Nurturing Support</h3>
              <p>
                Our support team tends to your needs like gardeners, ensuring your privacy grows and
                flourishes.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 8: Geometric/Abstract -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 8: Geometric/Abstract</h2>
          <p>Target: Design enthusiasts, tech-forward users, creative professionals</p>
        </div>
        <div class="style-preview style-8">
          <div class="hero">
            <div class="hero-shapes">
              <div class="shape shape-1"></div>
              <div class="shape shape-2"></div>
              <div class="shape shape-3"></div>
            </div>
            <h1>ONLINE VPN</h1>
            <p style="font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; letter-spacing: 1px">
              ABSTRACT SECURITY • GEOMETRIC PRIVACY
            </p>
            <button class="cta-button">CONNECT</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <polygon points="12,2 22,8.5 22,15.5 12,22 2,15.5 2,8.5 12,2" />
                  <polygon points="12,8 18,12 12,16 6,12 12,8" />
                </svg>
              </div>
              <h3>Crystalline Security</h3>
              <p>
                Multi-faceted encryption that protects your data from every angle with geometric
                precision.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <polygon points="1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2 1,6" />
                  <line x1="8" y1="2" x2="8" y2="18" />
                  <line x1="16" y1="6" x2="16" y2="22" />
                </svg>
              </div>
              <h3>Vector Speed</h3>
              <p>
                Optimized pathways deliver data at light speed through our abstract network
                topology.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="3" />
                  <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
                </svg>
              </div>
              <h3>Infinite Network</h3>
              <p>Boundless connectivity through our mathematically perfect server constellation.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                >
                  <path
                    d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"
                  />
                </svg>
              </div>
              <h3>Precision Support</h3>
              <p>Expertly calibrated assistance with surgical precision for optimal performance.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 9: Luxury/Premium -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 9: Luxury/Premium</h2>
          <p>Target: High-end users, luxury market, premium service seekers</p>
        </div>
        <div class="style-preview style-9">
          <div class="hero">
            <div class="hero-crown"></div>
            <h1>OnlineVPN</h1>
            <p style="font-size: 1.4rem; margin-bottom: 50px; opacity: 0.9; font-style: italic">
              The Crown Jewel of Digital Privacy
            </p>
            <button class="cta-button">Experience Excellence</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <path
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                  />
                </svg>
              </div>
              <h3>Platinum Security</h3>
              <p>
                Uncompromising protection worthy of royalty, crafted with the finest encryption
                algorithms available.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
              </div>
              <h3>Lightning Performance</h3>
              <p>
                Blazing speeds that exceed expectations, delivering premium performance without
                compromise.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <path
                    d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
                  />
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96" />
                  <line x1="12" y1="22.08" x2="12" y2="12" />
                </svg>
              </div>
              <h3>Global Prestige</h3>
              <p>
                Exclusive access to our premium server network, strategically positioned in the
                world's finest locations.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg
                  width="50"
                  height="50"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1a1a1a"
                  stroke-width="2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
              <h3>Concierge Support</h3>
              <p>
                White-glove service from our dedicated specialists, ensuring your experience remains
                flawless.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Style 10: Playful/Creative -->
      <div class="style-section">
        <div class="style-header">
          <h2>Style 10: Playful/Creative</h2>
          <p>Target: Young users, creative professionals, fun-loving individuals</p>
        </div>
        <div class="style-preview style-10">
          <div class="hero">
            <div class="hero-mascot"></div>
            <h1>OnlineVPN</h1>
            <p style="font-size: 1.3rem; margin-bottom: 40px; font-weight: 700; color: #4299e1">
              Super Fun • Super Safe • Super Fast!
            </p>
            <button class="cta-button">Let's Go!</button>
          </div>
          <div class="features">
            <div class="feature-card">
              <div class="feature-icon">🛡️</div>
              <h3>Super Shield</h3>
              <p>
                Our magical protection bubble keeps all the bad stuff out while you surf, stream,
                and play online!
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">⚡</div>
              <h3>Zoom Zoom Speed</h3>
              <p>
                Lightning-fast connections that'll make your internet go WHOOSH! Perfect for gaming
                and binge-watching.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🌍</div>
              <h3>World Explorer</h3>
              <p>
                Travel the digital world instantly! Access content from anywhere and everywhere with
                just one click.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🤝</div>
              <h3>Friendly Helpers</h3>
              <p>
                Our awesome support team is always ready to help with a smile. We're here 24/7 to
                make you happy!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
