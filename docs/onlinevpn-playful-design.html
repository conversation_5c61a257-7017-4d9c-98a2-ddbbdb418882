<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OnlineVPN - Playful & Creative Design</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'playful-pink': '#ff9a9e',
              'playful-purple': '#fecfef',
              'playful-blue': '#4299e1',
              'playful-orange': '#ed8936',
              'playful-green': '#48bb78',
              'playful-yellow': '#ecc94b',
              'playful-red': '#e53e3e',
              'playful-text': '#2d3748',
              'playful-text-light': '#4a5568'
            },
            fontFamily: {
              playful: ['Comic Sans MS', 'cursive', 'sans-serif']
            },
            animation: {
              'bounce-slow': 'bounce 2s infinite',
              wiggle: 'wiggle 1s ease-in-out infinite',
              float: 'float 3s ease-in-out infinite',
              'pulse-slow': 'pulse 3s infinite'
            },
            keyframes: {
              wiggle: {
                '0%, 100%': { transform: 'rotate(-3deg)' },
                '50%': { transform: 'rotate(3deg)' }
              },
              float: {
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-10px)' }
              }
            },
            transform: {
              'rotate-1': 'rotate(1deg)',
              'rotate-2': 'rotate(2deg)',
              'rotate-3': 'rotate(3deg)',
              '-rotate-1': 'rotate(-1deg)',
              '-rotate-2': 'rotate(-2deg)',
              '-rotate-3': 'rotate(-3deg)'
            }
          }
        }
      }
    </script>
    <style>
      .playful-card {
        transition: all 0.3s ease;
      }
      .playful-card:hover {
        transform: scale(1.05) rotate(0deg) !important;
      }
      .playful-button {
        transition: all 0.3s ease;
      }
      .playful-button:hover {
        transform: scale(1.1) rotate(-1deg);
      }
      .blob-1 {
        background: linear-gradient(45deg, #ff9a9e, #fecfef);
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        animation: float 4s ease-in-out infinite;
      }
      .blob-2 {
        background: linear-gradient(45deg, #4299e1, #ed8936);
        border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
        animation: float 5s ease-in-out infinite reverse;
      }
      .sticker {
        filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.2));
      }
    </style>
  </head>
  <body
    class="font-playful from-playful-pink via-playful-purple min-h-screen overflow-x-hidden bg-gradient-to-br to-white"
  >
    <!-- Floating Background Elements -->
    <div class="pointer-events-none fixed inset-0 z-0">
      <div class="blob-1 absolute left-10 top-20 h-32 w-32 opacity-20"></div>
      <div class="blob-2 absolute right-20 top-40 h-24 w-24 opacity-20"></div>
      <div class="blob-1 absolute bottom-40 left-1/4 h-20 w-20 opacity-15"></div>
      <div class="blob-2 absolute bottom-20 right-1/3 h-28 w-28 opacity-15"></div>

      <!-- Floating Emojis -->
      <div class="animate-float absolute left-1/3 top-32 text-4xl">🌟</div>
      <div class="animate-bounce-slow absolute right-1/4 top-60 text-3xl">🚀</div>
      <div class="left-1/5 animate-pulse-slow absolute bottom-60 text-3xl">🔒</div>
      <div class="right-1/5 animate-wiggle absolute bottom-32 text-4xl">⚡</div>
    </div>

    <!-- Navigation -->
    <nav
      class="border-playful-blue relative sticky top-0 z-10 border-b-4 bg-white/90 backdrop-blur-sm"
    >
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <div
              class="from-playful-blue to-playful-purple sticker mr-4 flex h-12 w-12 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
            >
              <span class="text-xl font-bold text-white">🛡️</span>
            </div>
            <h1 class="text-playful-text -rotate-1 transform text-2xl font-bold">OnlineVPN</h1>
          </div>
          <div class="hidden items-center space-x-8 md:flex">
            <a
              href="#features"
              class="text-playful-text-light hover:text-playful-blue font-bold transition-colors"
              >Features</a
            >
            <a
              href="#how-it-works"
              class="text-playful-text-light hover:text-playful-blue font-bold transition-colors"
              >How it Works</a
            >
            <a
              href="#faq"
              class="text-playful-text-light hover:text-playful-blue font-bold transition-colors"
              >FAQ</a
            >
          </div>
          <div class="flex items-center space-x-4">
            <button
              class="text-playful-text-light hover:text-playful-blue font-bold transition-colors"
            >
              Sign in
            </button>
            <button
              class="from-playful-blue to-playful-purple playful-button rounded-full border-4 border-white bg-gradient-to-r px-6 py-3 font-bold text-white shadow-lg"
            >
              Let's Go! 🎉
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative z-10 py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <!-- Hero Mascot -->
          <div
            class="from-playful-blue to-playful-purple sticker animate-bounce-slow mx-auto mb-8 flex h-40 w-40 items-center justify-center rounded-full border-8 border-white bg-gradient-to-br shadow-2xl"
          >
            <span class="text-6xl">😊</span>
            <div
              class="absolute right-4 top-4 flex h-8 w-8 items-center justify-center rounded-full bg-white shadow-lg"
            >
              <span class="text-lg">✨</span>
            </div>
          </div>

          <h1
            class="text-playful-text mb-6 -rotate-2 transform text-4xl font-black leading-tight md:text-6xl"
          >
            Free and Best<br />
            <span
              class="from-playful-blue via-playful-purple to-playful-orange inline-block rotate-1 transform bg-gradient-to-r bg-clip-text text-transparent"
            >
              Web Site Proxy Browser
            </span>
          </h1>

          <p
            class="text-playful-text-light mx-auto mb-12 max-w-3xl text-xl font-bold leading-relaxed"
          >
            Super Fun • Super Safe • Super Fast! 🌈<br />
            Browse the internet like never before with our magical proxy browser!
          </p>

          <!-- Fun URL Input Section -->
          <div class="mx-auto mb-12 max-w-2xl">
            <div
              class="border-playful-yellow sticker rotate-1 transform rounded-3xl border-4 bg-white p-8 shadow-2xl"
            >
              <div class="flex flex-col gap-4 sm:flex-row">
                <input
                  type="url"
                  placeholder="🌐 Enter your magical URL here..."
                  class="border-playful-blue text-playful-text placeholder-playful-text-light focus:border-playful-purple flex-1 rounded-2xl border-4 px-6 py-4 font-bold transition-colors focus:outline-none"
                />
                <button
                  class="from-playful-orange to-playful-red playful-button whitespace-nowrap rounded-2xl border-4 border-white bg-gradient-to-r px-8 py-4 font-black text-white shadow-lg"
                >
                  🚀 Start Adventure!
                </button>
              </div>
              <div class="mt-6 flex flex-wrap justify-center gap-6 text-sm font-bold">
                <div class="bg-playful-green/20 flex items-center gap-2 rounded-full px-4 py-2">
                  <span class="text-2xl">🎁</span>
                  <span class="text-playful-text">No registration</span>
                </div>
                <div class="bg-playful-blue/20 flex items-center gap-2 rounded-full px-4 py-2">
                  <span class="text-2xl">💝</span>
                  <span class="text-playful-text">100% Free</span>
                </div>
                <div class="bg-playful-purple/20 flex items-center gap-2 rounded-full px-4 py-2">
                  <span class="text-2xl">🎪</span>
                  <span class="text-playful-text">Unlimited fun</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Secondary CTAs -->
          <div class="flex flex-col justify-center gap-6 sm:flex-row">
            <button
              class="from-playful-green to-playful-blue playful-button rotate-1 transform rounded-full border-4 border-white bg-gradient-to-r px-10 py-5 text-lg font-black text-white shadow-xl"
            >
              🎮 Try Demo
            </button>
            <button
              class="text-playful-text playful-button border-playful-orange -rotate-1 transform rounded-full border-4 bg-white px-10 py-5 text-lg font-black shadow-xl"
            >
              📚 Learn More
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="relative z-10 py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-playful-text mb-4 -rotate-1 transform text-4xl font-black">
            Our Super Powers! 🦸‍♂️
          </h2>
          <p class="text-playful-text-light mx-auto max-w-2xl text-xl font-bold">
            Choose your favorite magical proxy service and start your adventure!
          </p>
        </div>

        <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          <!-- Feature Card 1 -->
          <div
            class="playful-card border-playful-blue sticker rotate-1 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div
              class="from-playful-blue to-playful-purple mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
            >
              <span class="text-4xl">🌐</span>
            </div>
            <h3 class="text-playful-text mb-3 text-xl font-black">Web Proxy</h3>
            <p class="text-playful-text-light mb-6 text-sm font-bold leading-relaxed">
              Surf the web like a pro with our super cool web proxy magic! 🏄‍♂️
            </p>
            <button
              class="from-playful-blue to-playful-purple playful-button rounded-full border-2 border-white bg-gradient-to-r px-6 py-3 text-sm font-black text-white shadow-lg"
            >
              🎯 Try Now!
            </button>
          </div>

          <!-- Feature Card 2 -->
          <div
            class="playful-card border-playful-green sticker -rotate-1 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div
              class="from-playful-green to-playful-yellow mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
            >
              <span class="text-4xl">🛡️</span>
            </div>
            <h3 class="text-playful-text mb-3 text-xl font-black">Super Shield</h3>
            <p class="text-playful-text-light mb-6 text-sm font-bold leading-relaxed">
              Our magical protection bubble keeps all the bad stuff out! ✨
            </p>
            <button
              class="from-playful-green to-playful-yellow playful-button rounded-full border-2 border-white bg-gradient-to-r px-6 py-3 text-sm font-black text-white shadow-lg"
            >
              🎯 Try Now!
            </button>
          </div>

          <!-- Feature Card 3 -->
          <div
            class="playful-card border-playful-orange sticker rotate-2 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div
              class="from-playful-orange to-playful-red mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
            >
              <span class="text-4xl">⚡</span>
            </div>
            <h3 class="text-playful-text mb-3 text-xl font-black">Zoom Zoom Speed</h3>
            <p class="text-playful-text-light mb-6 text-sm font-bold leading-relaxed">
              Lightning-fast connections that'll make your internet go WHOOSH! 💨
            </p>
            <button
              class="from-playful-orange to-playful-red playful-button rounded-full border-2 border-white bg-gradient-to-r px-6 py-3 text-sm font-black text-white shadow-lg"
            >
              🎯 Try Now!
            </button>
          </div>

          <!-- Feature Card 4 -->
          <div
            class="playful-card border-playful-purple sticker -rotate-2 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div
              class="from-playful-purple to-playful-pink mx-auto mb-6 flex h-24 w-24 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
            >
              <span class="text-4xl">🌍</span>
            </div>
            <h3 class="text-playful-text mb-3 text-xl font-black">World Explorer</h3>
            <p class="text-playful-text-light mb-6 text-sm font-bold leading-relaxed">
              Travel the digital world instantly! Access content from everywhere! 🗺️
            </p>
            <button
              class="from-playful-purple to-playful-pink playful-button rounded-full border-2 border-white bg-gradient-to-r px-6 py-3 text-sm font-black text-white shadow-lg"
            >
              🎯 Try Now!
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- How it Works Section -->
    <section id="how-it-works" class="relative z-10 py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-playful-text mb-4 rotate-1 transform text-4xl font-black">
            How to Use Our Magic! 🪄
          </h2>
          <p class="text-playful-text-light mx-auto max-w-3xl text-xl font-bold">
            It's super easy! Just follow these 3 magical steps and you're ready to go! ✨
          </p>
        </div>

        <div class="grid grid-cols-1 gap-12 md:grid-cols-3">
          <!-- Step 1 -->
          <div class="text-center">
            <div
              class="from-playful-blue to-playful-purple sticker mx-auto mb-8 flex h-32 w-32 rotate-1 transform items-center justify-center rounded-full border-8 border-white bg-gradient-to-br shadow-2xl"
            >
              <span class="text-4xl font-black text-white">1</span>
              <div
                class="bg-playful-yellow absolute -right-2 -top-2 flex h-12 w-12 items-center justify-center rounded-full border-4 border-white shadow-lg"
              >
                <span class="text-2xl">📝</span>
              </div>
            </div>
            <h3 class="text-playful-text mb-4 -rotate-1 transform text-2xl font-black">
              Type Your URL!
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              Just paste the website URL you want to visit into our super cool input box! Easy
              peasy! 🎯
            </p>
          </div>

          <!-- Step 2 -->
          <div class="text-center">
            <div
              class="from-playful-green to-playful-yellow sticker mx-auto mb-8 flex h-32 w-32 -rotate-1 transform items-center justify-center rounded-full border-8 border-white bg-gradient-to-br shadow-2xl"
            >
              <span class="text-4xl font-black text-white">2</span>
              <div
                class="bg-playful-orange absolute -right-2 -top-2 flex h-12 w-12 items-center justify-center rounded-full border-4 border-white shadow-lg"
              >
                <span class="text-2xl">🌍</span>
              </div>
            </div>
            <h3 class="text-playful-text mb-4 rotate-1 transform text-2xl font-black">
              Pick Your Location!
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              Choose your favorite server location from our magical global network! 🗺️
            </p>
          </div>

          <!-- Step 3 -->
          <div class="text-center">
            <div
              class="from-playful-orange to-playful-red sticker mx-auto mb-8 flex h-32 w-32 rotate-2 transform items-center justify-center rounded-full border-8 border-white bg-gradient-to-br shadow-2xl"
            >
              <span class="text-4xl font-black text-white">3</span>
              <div
                class="bg-playful-green absolute -right-2 -top-2 flex h-12 w-12 items-center justify-center rounded-full border-4 border-white shadow-lg"
              >
                <span class="text-2xl">🚀</span>
              </div>
            </div>
            <h3 class="text-playful-text mb-4 -rotate-1 transform text-2xl font-black">
              Start Browsing!
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              Click the magic button and start your amazing browsing adventure! Your privacy is
              protected! 🛡️
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="relative z-10 py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 items-center gap-16 lg:grid-cols-2">
          <div>
            <h2 class="text-playful-text mb-8 -rotate-1 transform text-4xl font-black">
              Why We're Awesome! 🌟
            </h2>
            <div class="space-y-6">
              <div
                class="border-playful-blue sticker rotate-1 transform rounded-2xl border-4 bg-white p-6 shadow-xl"
              >
                <div class="flex items-start gap-4">
                  <div
                    class="from-playful-blue to-playful-purple flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                  >
                    <span class="text-2xl">🎁</span>
                  </div>
                  <div>
                    <h3 class="text-playful-text mb-2 text-xl font-black">No Downloads Needed!</h3>
                    <p class="text-playful-text-light font-bold leading-relaxed">
                      Use our magical web proxy right in your browser! No messy downloads or boring
                      installations! 🎉
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="border-playful-green sticker -rotate-1 transform rounded-2xl border-4 bg-white p-6 shadow-xl"
              >
                <div class="flex items-start gap-4">
                  <div
                    class="from-playful-green to-playful-yellow flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                  >
                    <span class="text-2xl">🕵️</span>
                  </div>
                  <div>
                    <h3 class="text-playful-text mb-2 text-xl font-black">Super Secret Mode!</h3>
                    <p class="text-playful-text-light font-bold leading-relaxed">
                      Your IP address becomes invisible and your browsing stays completely secret!
                      Like a digital ninja! 🥷
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="border-playful-orange sticker rotate-1 transform rounded-2xl border-4 bg-white p-6 shadow-xl"
              >
                <div class="flex items-start gap-4">
                  <div
                    class="from-playful-orange to-playful-red flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                  >
                    <span class="text-2xl">🗝️</span>
                  </div>
                  <div>
                    <h3 class="text-playful-text mb-2 text-xl font-black">Unlock Everything!</h3>
                    <p class="text-playful-text-light font-bold leading-relaxed">
                      Access blocked websites and content from anywhere! It's like having a magic
                      key to the internet! 🔓
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="border-playful-purple sticker -rotate-1 transform rounded-2xl border-4 bg-white p-6 shadow-xl"
              >
                <div class="flex items-start gap-4">
                  <div
                    class="from-playful-purple to-playful-pink flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                  >
                    <span class="text-2xl">🚄</span>
                  </div>
                  <div>
                    <h3 class="text-playful-text mb-2 text-xl font-black">Lightning Fast!</h3>
                    <p class="text-playful-text-light font-bold leading-relaxed">
                      Super speedy connections that make browsing feel like flying! Zoom zoom! 💨
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            class="from-playful-blue via-playful-purple to-playful-pink sticker rotate-2 transform rounded-3xl border-8 border-white bg-gradient-to-br p-12 text-center text-white shadow-2xl"
          >
            <div
              class="mx-auto mb-8 flex h-32 w-32 items-center justify-center rounded-full bg-white shadow-xl"
            >
              <span class="text-6xl">🎪</span>
            </div>
            <h3 class="mb-6 text-3xl font-black">Ready for Fun?</h3>
            <p class="mb-8 text-xl font-bold leading-relaxed opacity-90">
              Join millions of happy users who love our super fun and safe proxy service! 🎊
            </p>
            <div class="mb-8 space-y-4">
              <div class="flex items-center justify-center gap-3 font-bold">
                <span class="text-3xl">🎁</span>
                <span>Free forever and ever!</span>
              </div>
              <div class="flex items-center justify-center gap-3 font-bold">
                <span class="text-3xl">🎮</span>
                <span>No boring sign-ups!</span>
              </div>
              <div class="flex items-center justify-center gap-3 font-bold">
                <span class="text-3xl">🤝</span>
                <span>Friendly helpers 24/7!</span>
              </div>
            </div>
            <button
              class="text-playful-blue playful-button border-playful-yellow rounded-full border-4 bg-white px-10 py-4 text-lg font-black shadow-xl"
            >
              🚀 Start Playing Now!
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Fun Stats Section -->
    <section class="relative z-10 py-20">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-playful-text mb-4 rotate-1 transform text-3xl font-black">
            Look at These Cool Numbers! 📊
          </h2>
        </div>
        <div class="grid grid-cols-2 gap-8 md:grid-cols-4">
          <div
            class="border-playful-blue sticker rotate-1 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div class="mb-4 text-4xl">🎉</div>
            <div class="text-playful-blue mb-2 text-4xl font-black">10M+</div>
            <div class="text-playful-text-light font-bold">Happy Users</div>
          </div>
          <div
            class="border-playful-green sticker -rotate-1 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div class="mb-4 text-4xl">🌍</div>
            <div class="text-playful-green mb-2 text-4xl font-black">50+</div>
            <div class="text-playful-text-light font-bold">Countries</div>
          </div>
          <div
            class="border-playful-orange sticker rotate-2 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div class="mb-4 text-4xl">⚡</div>
            <div class="text-playful-orange mb-2 text-4xl font-black">99.9%</div>
            <div class="text-playful-text-light font-bold">Uptime</div>
          </div>
          <div
            class="border-playful-purple sticker -rotate-2 transform rounded-3xl border-4 bg-white p-8 text-center shadow-2xl"
          >
            <div class="mb-4 text-4xl">🤝</div>
            <div class="text-playful-purple mb-2 text-4xl font-black">24/7</div>
            <div class="text-playful-text-light font-bold">Support</div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="relative z-10 py-20">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div class="mb-16 text-center">
          <h2 class="text-playful-text mb-4 -rotate-1 transform text-4xl font-black">
            Got Questions? We Got Answers! 🤔
          </h2>
          <p class="text-playful-text-light text-xl font-bold">
            Everything you need to know about our awesome service!
          </p>
        </div>

        <div class="space-y-8">
          <div
            class="border-playful-blue sticker rotate-1 transform rounded-3xl border-4 bg-white p-8 shadow-2xl"
          >
            <h3 class="text-playful-text mb-4 flex items-center gap-3 text-xl font-black">
              <span class="text-2xl">🤷‍♂️</span>
              What's a web proxy anyway?
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              A web proxy is like a super cool middleman between you and the internet! It hides your
              real location and keeps you safe while you browse. Think of it as your digital
              bodyguard! 🛡️
            </p>
          </div>

          <div
            class="border-playful-green sticker -rotate-1 transform rounded-3xl border-4 bg-white p-8 shadow-2xl"
          >
            <h3 class="text-playful-text mb-4 flex items-center gap-3 text-xl font-black">
              <span class="text-2xl">💰</span>
              Is OnlineVPN really totally free?
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              YES! It's 100% free forever and ever! No sneaky fees, no boring sign-ups, no time
              limits. Just pure awesome proxy fun! We promise! 🎁
            </p>
          </div>

          <div
            class="border-playful-orange sticker rotate-1 transform rounded-3xl border-4 bg-white p-8 shadow-2xl"
          >
            <h3 class="text-playful-text mb-4 flex items-center gap-3 text-xl font-black">
              <span class="text-2xl">🔒</span>
              How safe is my stuff?
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              Super duper safe! We use mega-strong encryption to protect your data. We don't peek at
              what you're doing, and your IP address stays completely hidden! Your secrets are safe
              with us! 🤐
            </p>
          </div>

          <div
            class="border-playful-purple sticker -rotate-1 transform rounded-3xl border-4 bg-white p-8 shadow-2xl"
          >
            <h3 class="text-playful-text mb-4 flex items-center gap-3 text-xl font-black">
              <span class="text-2xl">📱</span>
              Can I use it on my phone?
            </h3>
            <p class="text-playful-text-light font-bold leading-relaxed">
              Absolutely! OnlineVPN works on phones, tablets, laptops, computers - basically any
              device with a web browser! Just visit our website and start having fun! 📲
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="relative z-10 py-16">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="border-playful-blue sticker rounded-3xl border-4 bg-white p-12 shadow-2xl">
          <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
            <div>
              <div class="mb-6 flex items-center">
                <div
                  class="from-playful-blue to-playful-purple mr-3 flex h-12 w-12 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                >
                  <span class="text-xl text-white">🛡️</span>
                </div>
                <h3 class="text-playful-text -rotate-1 transform text-2xl font-black">OnlineVPN</h3>
              </div>
              <p class="text-playful-text-light mb-6 font-bold leading-relaxed">
                The most fun, safe, and awesome web proxy service in the universe! 🌟
              </p>
              <div class="flex space-x-4">
                <div
                  class="from-playful-blue to-playful-purple sticker flex h-12 w-12 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                >
                  <span class="text-xl text-white">📱</span>
                </div>
                <div
                  class="from-playful-green to-playful-yellow sticker flex h-12 w-12 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br shadow-lg"
                >
                  <span class="text-xl text-white">💬</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="text-playful-text mb-6 text-lg font-black">🎮 Cool Stuff</h4>
              <ul class="text-playful-text-light space-y-3 font-bold">
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors">🌐 Web Proxy</a>
                </li>
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors">🛡️ Super Shield</a>
                </li>
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors">⚡ Zoom Speed</a>
                </li>
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors"
                    >🌍 World Explorer</a
                  >
                </li>
              </ul>
            </div>

            <div>
              <h4 class="text-playful-text mb-6 text-lg font-black">🤝 Get Help</h4>
              <ul class="text-playful-text-light space-y-3 font-bold">
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors">📚 Help Center</a>
                </li>
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors">💬 Chat With Us</a>
                </li>
                <li><a href="#" class="hover:text-playful-blue transition-colors">❓ FAQ</a></li>
                <li><a href="#" class="hover:text-playful-blue transition-colors">📊 Status</a></li>
              </ul>
            </div>

            <div>
              <h4 class="text-playful-text mb-6 text-lg font-black">📋 Boring Legal Stuff</h4>
              <ul class="text-playful-text-light space-y-3 font-bold">
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors"
                    >🔒 Privacy Policy</a
                  >
                </li>
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors"
                    >📜 Terms of Service</a
                  >
                </li>
                <li>
                  <a href="#" class="hover:text-playful-blue transition-colors">🍪 Cookie Policy</a>
                </li>
                <li><a href="#" class="hover:text-playful-blue transition-colors">⚖️ DMCA</a></li>
              </ul>
            </div>
          </div>

          <div class="border-playful-blue mt-12 border-t-4 pt-8 text-center">
            <p class="text-playful-text-light font-bold">
              © 2024 OnlineVPN. All rights reserved. Made with 💖 and lots of ☕!
            </p>
          </div>
        </div>
      </div>
    </footer>
  </body>
</html>
