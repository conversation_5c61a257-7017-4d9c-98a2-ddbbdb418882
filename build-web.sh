#!/bin/bash
set -e

# 环境变量
TZ=Asia/Shanghai
DOCKER_REGISTRY="ghcr.io"
DOCKER_NAMESPACE="fechin"
DOCKER_IMAGE="proxyorb"
DOCKER_VERSION="latest"
TIMESTAMP=$(date +%Y%m%d%H%M%S)
SHORT_SHA=$(git rev-parse --short HEAD 2>/dev/null || echo "local")

# 登录到容器仓库
echo "登录到容器仓库 ${DOCKER_REGISTRY}..."

# 检查是否存在CR_PAT环境变量
if [ -n "$CR_PAT" ]; then
  echo "使用CR_PAT环境变量登录..."
  echo "$CR_PAT" | docker login "${DOCKER_REGISTRY}" -u "${DOCKER_NAMESPACE}" --password-stdin
else
  echo "未找到CR_PAT环境变量，使用交互式登录"
  echo "使用默认用户名: ${DOCKER_NAMESPACE}"
  DOCKER_USERNAME="${DOCKER_NAMESPACE}"

  echo "请输入你的密码/Token:"
  read -rs DOCKER_PASSWORD

  echo "正在登录..."
  echo "$DOCKER_PASSWORD" | docker login "${DOCKER_REGISTRY}" -u "${DOCKER_USERNAME}" --password-stdin
fi

if [ $? -ne 0 ]; then
  echo "登录失败，退出脚本"
  exit 1
fi

# 构建镜像标签
TAGS=("${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/${DOCKER_IMAGE}:latest"
      "${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/${DOCKER_IMAGE}:${SHORT_SHA}")

TAGS_ARGS=""
for tag in "${TAGS[@]}"; do
  TAGS_ARGS="$TAGS_ARGS --tag $tag"
done

# 设置Docker BuildX
echo "设置Docker BuildX..."
docker buildx prune -f
docker buildx create --use --name proxyorb-builder || true

# 构建并推送多架构镜像
echo "开始构建并推送Web镜像: ${TAGS[0]}"
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --push \
  --file ./apps/web/Dockerfile \
  $TAGS_ARGS \
  ./

echo "镜像构建并推送完成!"
echo "推送的镜像标签:"
for tag in "${TAGS[@]}"; do
  echo "  - $tag"
done

echo "开始部署到K3S..."
cd /Users/<USER>/work/me/setup && fab -H sj k3s-deploy --apps=proxyorb
echo "✅ 部署完成"
